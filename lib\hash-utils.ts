/**
 * Simple string hashing function
 * @param str String to hash
 * @returns A string representation of the hash
 */
export function hash(str: string): string {
  let h = 0
  if (str.length === 0) return h.toString()

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    h = (h << 5) - h + char
    h = h & h // Convert to 32bit integer
  }

  return h.toString()
}

/**
 * Creates a hash from an object
 * @param obj Object to hash
 * @returns A string representation of the hash
 */
export function hashObject(obj: any): string {
  return hash(JSON.stringify(obj))
}
