"use client"

import { useState, useEffect } from "react"
import { Database, RefreshCw, CheckCircle2, XCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Progress } from "@/components/ui/progress"

interface CacheStatusProps {
  className?: string
  showDetails?: boolean
}

export function CacheStatus({ className = "", showDetails = false }: CacheStatusProps) {
  const [cacheStatus, setCacheStatus] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(0)
  const [lastCheck, setLastCheck] = useState<string | null>(null)

  // Fonction pour vérifier l'état du cache
  const checkCacheStatus = async () => {
    try {
      setLoading(true)
      setProgress(10)

      // Vérifier les caches disponibles
      const cacheNames = await caches.keys()
      setProgress(30)

      // Vérifier le service worker
      const swRegistration = await navigator.serviceWorker.getRegistration()
      setProgress(50)

      // Vérifier le service worker d'authentification
      let authSwStatus = null
      if (typeof window !== "undefined" && window.checkAuthServiceWorker) {
        authSwStatus = await window.checkAuthServiceWorker()
      }
      setProgress(70)

      // Vérifier IndexedDB
      let indexedDBAvailable = false
      try {
        indexedDBAvailable = !!window.indexedDB
      } catch (e) {
        console.error("Erreur lors de la vérification d'IndexedDB:", e)
      }
      setProgress(90)

      // Calculer le nombre total d'entrées en cache
      let totalCacheEntries = 0
      for (const cacheName of cacheNames) {
        try {
          const cache = await caches.open(cacheName)
          const keys = await cache.keys()
          totalCacheEntries += keys.length
        } catch (e) {
          console.error(`Erreur lors de la vérification du cache ${cacheName}:`, e)
        }
      }

      setCacheStatus({
        cacheNames,
        totalCacheEntries,
        serviceWorker: {
          registered: !!swRegistration,
          scope: swRegistration?.scope || null,
          active: !!swRegistration?.active,
          waiting: !!swRegistration?.waiting,
          installing: !!swRegistration?.installing,
        },
        authServiceWorker: authSwStatus,
        indexedDB: {
          available: indexedDBAvailable,
        },
        timestamp: Date.now(),
      })

      setLastCheck(new Date().toLocaleString())
      setProgress(100)

      // Stocker le timestamp dans localStorage
      try {
        localStorage.setItem("cache_status_last_check", Date.now().toString())
      } catch (e) {
        console.error("Erreur lors du stockage du timestamp:", e)
      }

      setLoading(false)
    } catch (error) {
      console.error("Erreur lors de la vérification de l'état du cache:", error)
      setLoading(false)
      setProgress(100)
    }
  }

  // Charger l'état du cache au montage du composant
  useEffect(() => {
    checkCacheStatus()

    // Récupérer le dernier timestamp de vérification
    try {
      const lastCheckTimestamp = localStorage.getItem("cache_status_last_check")
      if (lastCheckTimestamp) {
        const date = new Date(Number.parseInt(lastCheckTimestamp, 10))
        setLastCheck(date.toLocaleString())
      }
    } catch (e) {
      console.error("Erreur lors de la récupération du timestamp:", e)
    }
  }, [])

  // Fonction pour déterminer l'état global du cache
  const getCacheHealthStatus = () => {
    if (!cacheStatus) return "unknown"

    const hasCaches = cacheStatus.cacheNames.length > 0
    const hasEntries = cacheStatus.totalCacheEntries > 0
    const swActive = cacheStatus.serviceWorker.active
    const authSwActive = cacheStatus.authServiceWorker?.isActive

    if (hasCaches && hasEntries && swActive && authSwActive) {
      return "healthy"
    } else if (hasCaches && hasEntries && swActive) {
      return "partial"
    } else {
      return "unhealthy"
    }
  }

  const healthStatus = getCacheHealthStatus()

  return (
    <div className={`${className}`}>
      {loading && <Progress value={progress} className="h-1 mb-2" />}

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Database className="h-4 w-4 mr-1" />
          <span className="text-sm mr-2">Cache:</span>

          {healthStatus === "healthy" && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle2 className="h-3 w-3 mr-1" />
              Opérationnel
            </Badge>
          )}

          {healthStatus === "partial" && (
            <Badge className="bg-amber-100 text-amber-800">Partiellement opérationnel</Badge>
          )}

          {healthStatus === "unhealthy" && (
            <Badge variant="destructive">
              <XCircle className="h-3 w-3 mr-1" />
              Non opérationnel
            </Badge>
          )}

          {healthStatus === "unknown" && <Badge variant="outline">Vérification...</Badge>}
        </div>

        {showDetails && (
          <Button variant="ghost" size="sm" onClick={checkCacheStatus} disabled={loading}>
            <RefreshCw className={`h-3 w-3 ${loading ? "animate-spin" : ""}`} />
          </Button>
        )}
      </div>

      {showDetails && cacheStatus && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="text-xs text-muted-foreground mt-1 cursor-help">
                {cacheStatus.totalCacheEntries} entrées dans {cacheStatus.cacheNames.length} cache(s)
                {lastCheck && ` • Dernière vérification: ${lastCheck}`}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-xs space-y-1">
                <div>Service Worker: {cacheStatus.serviceWorker.active ? "Actif" : "Inactif"}</div>
                <div>Auth Service Worker: {cacheStatus.authServiceWorker?.isActive ? "Actif" : "Inactif"}</div>
                <div>IndexedDB: {cacheStatus.indexedDB.available ? "Disponible" : "Non disponible"}</div>
                <div>Caches: {cacheStatus.cacheNames.join(", ")}</div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  )
}
