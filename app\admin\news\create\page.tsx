"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db, storage } from "@/lib/firebase"
import { collection, addDoc, serverTimestamp, query, orderBy, getDocs, limit, doc, getDoc } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, AlertCircle, ImagePlus, Calendar } from "lucide-react"
import dynamic from "next/dynamic"
import { useGroups } from "@/lib/hooks"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON>ertDes<PERSON>, AlertTitle } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"

// Add these imports at the top with the other imports
import { GridModel, type GridItem } from "@/components/content-models/grid-model"
import { ContentTypeSelector, type ContentModelType } from "@/components/content-models/content-type-selector"
import { SingleImageModel } from "@/components/content-models/single-image-model"
import { GalleryModel, type GalleryImage } from "@/components/content-models/gallery-model"
import { CarouselModel, type CarouselSlide } from "@/components/content-models/carousel-model"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Dynamically import the RichTextEditor with no SSR
const RichTextEditor = dynamic(() => import("@/components/rich-text-editor"), {
  ssr: false,
  loading: () => <Skeleton className="w-full h-64" />,
})

export default function CreateNewsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [title, setTitle] = useState("")
  const [summary, setSummary] = useState("")
  const [content, setContent] = useState("")
  const [image, setImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublished, setIsPublished] = useState(true)
  const [isPinned, setIsPinned] = useState(false)
  const [showFrame, setShowFrame] = useState(true)
  // Add new state variables for image visibility
  const [showThumbnail, setShowThumbnail] = useState(true)
  const [showContentImage, setShowContentImage] = useState(true)
  const [showPublicationDate, setShowPublicationDate] = useState(true)
  // Add new state variables for custom date
  const [useCustomDate, setUseCustomDate] = useState(false)
  const [customDate, setCustomDate] = useState<Date>(new Date())
  const [isLoading, setIsLoading] = useState(false)
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { groups, loading: loadingGroups } = useGroups()
  const [storageError, setStorageError] = useState<string | null>(null)

  // Add these states in the component, after the other state declarations
  const [contentType, setContentType] = useState<ContentModelType>("richtext")
  const [singleImage, setSingleImage] = useState<File | null>(null)
  const [singleImageUrl, setSingleImageUrl] = useState<string | null>(null)
  const [singleImageCaption, setSingleImageCaption] = useState("")
  const [singleImageAltText, setSingleImageAltText] = useState("")
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([])
  const [galleryColumns, setGalleryColumns] = useState<number>(4)
  const [carouselSlides, setCarouselSlides] = useState<CarouselSlide[]>([])
  const [carouselAutoplay, setCarouselAutoplay] = useState(true)
  const [carouselInterval, setCarouselInterval] = useState(5)
  const [gridItems, setGridItems] = useState<GridItem[]>([])

  // Charger la valeur par défaut pour showFrame
  useEffect(() => {
    const fetchDefaultSettings = async () => {
      try {
        const displaySettingsDoc = await getDoc(doc(db(), "settings", "display"))
        if (displaySettingsDoc.exists()) {
          const displayData = displaySettingsDoc.data()
          setShowFrame(displayData.defaultShowFrameNews !== false)
          // Add this line to set the default publication date visibility
          setShowPublicationDate(displayData.defaultShowPublicationDate === true)
        }
      } catch (error) {
        console.error("Erreur lors du chargement des paramètres d'affichage:", error)
      }
    }

    fetchDefaultSettings()
  }, [])

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setImage(file)
      setStorageError(null)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups((prev) => (prev.includes(groupId) ? prev.filter((id) => id !== groupId) : [...prev, groupId]))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title) {
      toast({
        title: "Erreur",
        description: "Le titre est obligatoire",
        variant: "destructive",
      })
      setIsLoading(false)
      return
    }

    // Validation spécifique selon le type de contenu
    let contentValid = true
    if (contentType === "richtext" && !content) {
      contentValid = false
    } else if (contentType === "single-image" && !singleImage) {
      contentValid = false
    } else if (contentType === "gallery" && galleryImages.length === 0) {
      contentValid = false
    } else if (contentType === "carousel" && carouselSlides.length === 0) {
      contentValid = false
    }

    if (!contentValid) {
      toast({
        title: "Erreur",
        description: "Le contenu est obligatoire selon le type sélectionné",
        variant: "destructive",
      })
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setStorageError(null)

      // Récupérer l'ordre d'affichage le plus bas pour placer le nouvel article en premier
      let displayOrder = 0
      try {
        const newsQuery = query(collection(db(), "news"), orderBy("displayOrder", "asc"), limit(1))
        const querySnapshot = await getDocs(newsQuery)
        if (!querySnapshot.empty) {
          const lowestOrder = querySnapshot.docs[0].data().displayOrder || 0
          displayOrder = lowestOrder - 1 // Placer avant le premier élément
        }
      } catch (error) {
        console.error("Erreur lors de la récupération de l'ordre d'affichage:", error)
        // Continuer avec l'ordre par défaut
      }

      // Prepare news data
      const newsData: any = {
        title,
        summary,
        content,
        isPublished,
        isPinned,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        targetGroups: selectedGroups.length > 0 ? selectedGroups : ["all"],
        imageUrl: "",
        displayOrder: displayOrder, // Ajouter l'ordre d'affichage
        showFrame,
        // Add new fields for image visibility
        showThumbnail,
        showContentImage,
        showPublicationDate,
      }

      // Add this in the handleSubmit function, before creating the document
      // Prepare model data based on content type
      let modelData = {}

      if (contentType === "single-image" && singleImage) {
        try {
          // Upload image
          const imageRef = ref(storage(), `news-content/${Date.now()}_${singleImage.name}`)
          await uploadBytes(imageRef, singleImage)
          const imageUrl = await getDownloadURL(imageRef)

          modelData = {
            imageUrl,
            caption: singleImageCaption,
            altText: singleImageAltText,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement de l'image:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger l'image du modèle",
            variant: "destructive",
          })
        }
      } else if (contentType === "gallery" && galleryImages.length > 0) {
        try {
          // Upload all gallery images
          const uploadedImages = []

          for (const image of galleryImages) {
            if (image.file) {
              const imageRef = ref(storage(), `news-content/${Date.now()}_${image.file.name}`)
              await uploadBytes(imageRef, image.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedImages.push({
                url: imageUrl,
                altText: image.altText,
              })
            } else {
              // Image already has a URL
              uploadedImages.push({
                url: image.url,
                altText: image.altText,
              })
            }
          }

          modelData = {
            images: uploadedImages,
            columns: galleryColumns,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la galerie:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la galerie",
            variant: "destructive",
          })
        }
      } else if (contentType === "carousel" && carouselSlides.length > 0) {
        try {
          // Upload all carousel slides
          const uploadedSlides = []

          for (const slide of carouselSlides) {
            if (slide.file) {
              const imageRef = ref(storage(), `news-content/${Date.now()}_${slide.file.name}`)
              await uploadBytes(imageRef, slide.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedSlides.push({
                url: imageUrl,
                title: slide.title,
                description: slide.description,
              })
            } else {
              // Slide already has a URL
              uploadedSlides.push({
                url: slide.url,
                title: slide.title,
                description: slide.description,
              })
            }
          }

          modelData = {
            slides: uploadedSlides,
            autoplay: carouselAutoplay,
            interval: carouselInterval,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images du carousel:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images du carousel",
            variant: "destructive",
          })
        }
      } else if (contentType === "grid" && gridItems.length > 0) {
        try {
          // Upload all grid item images
          const processedItems = []

          for (const item of gridItems) {
            const processedItem: any = {
              title: item.title,
              description: item.description,
              link: item.link,
            }

            if (item.file) {
              const imageRef = ref(storage(), `news-content/${Date.now()}_${item.file.name}`)
              await uploadBytes(imageRef, item.file)
              const imageUrl = await getDownloadURL(imageRef)
              processedItem.imageUrl = imageUrl
            } else if (item.imageUrl) {
              processedItem.imageUrl = item.imageUrl
            }

            processedItems.push(processedItem)
          }

          modelData = {
            items: processedItems,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la grille:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la grille",
            variant: "destructive",
          })
        }
      }

      // Add these properties to newsData
      newsData.contentType = contentType
      newsData.modelData = modelData

      // Upload image if exists
      if (image) {
        try {
          const newsImageRef = ref(storage(), `news/${Date.now()}_${image.name}`)
          await uploadBytes(newsImageRef, image)
          const imageUrl = await getDownloadURL(newsImageRef)
          newsData.imageUrl = imageUrl
        } catch (error: any) {
          console.error("Erreur lors du téléchargement de l'image:", error)
          setStorageError(
            "Impossible de télécharger l'image. Vérifiez les règles de sécurité Firebase Storage. L'article sera créé sans image.",
          )
          // Continue without image
        }
      }

      // Add document to Firestore
      const docRef = await addDoc(collection(db(), "news"), newsData)

      toast({
        title: "Succès",
        description: storageError
          ? "L'article a été créé avec succès, mais sans image."
          : "L'article a été créé avec succès",
      })

      router.push("/admin/news")
    } catch (error) {
      console.error("Erreur lors de la création de l'article:", error)
      toast({
        title: "Erreur",
        description:
          "Une erreur est survenue lors de la création de l'article: " +
          (error instanceof Error ? error.message : "Erreur inconnue"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Créer un nouvel article</h1>

      {storageError && (
        <Alert variant="warning" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Attention</AlertTitle>
          <AlertDescription>{storageError}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Informations générales</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre *</Label>
              <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="summary">Résumé</Label>
              <Textarea id="summary" value={summary} onChange={(e) => setSummary(e.target.value)} rows={3} />
            </div>

            {/* Replace the content section in the form with this */}
            <div className="space-y-4">
              <ContentTypeSelector value={contentType} onChange={setContentType} />

              <div className="border-t pt-4 mt-4">
                <Tabs defaultValue="content" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="content">Contenu</TabsTrigger>
                    <TabsTrigger value="preview" disabled={contentType === "richtext"}>
                      Aperçu
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="content">
                    {contentType === "richtext" && (
                      <div className="space-y-2">
                        <Label htmlFor="content">Contenu *</Label>
                        <RichTextEditor value={content} onChange={setContent} />
                      </div>
                    )}

                    {contentType === "single-image" && (
                      <SingleImageModel
                        imageUrl={singleImageUrl}
                        caption={singleImageCaption}
                        altText={singleImageAltText}
                        onImageChange={(file) => {
                          setSingleImage(file)
                          if (file) {
                            const url = URL.createObjectURL(file)
                            setSingleImageUrl(url)
                          } else {
                            setSingleImageUrl(null)
                          }
                        }}
                        onCaptionChange={setSingleImageCaption}
                        onAltTextChange={setSingleImageAltText}
                      />
                    )}

                    {contentType === "gallery" && (
                      <GalleryModel
                        images={galleryImages}
                        onImagesChange={setGalleryImages}
                        columns={galleryColumns}
                        onColumnsChange={setGalleryColumns}
                      />
                    )}

                    {contentType === "carousel" && (
                      <CarouselModel
                        slides={carouselSlides}
                        onSlidesChange={setCarouselSlides}
                        autoplay={carouselAutoplay}
                        onAutoplayChange={setCarouselAutoplay}
                        interval={carouselInterval}
                        onIntervalChange={setCarouselInterval}
                      />
                    )}

                    {contentType === "grid" && <GridModel items={gridItems} onItemsChange={setGridItems} />}
                  </TabsContent>
                  <TabsContent value="preview">
                    <div className="border rounded-lg p-4">
                      <p className="text-sm text-muted-foreground mb-4">Aperçu du contenu</p>
                      <div className="text-center p-8 text-muted-foreground">
                        L'aperçu sera disponible après la sauvegarde
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Image</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>
                <ImagePlus className="mr-2 h-4 w-4" />
                {imagePreview ? "Changer l'image" : "Ajouter une image"}
              </Button>
              <input ref={fileInputRef} type="file" accept="image/*" onChange={handleImageChange} className="hidden" />

              {imagePreview && (
                <div className="relative w-32 h-32">
                  <img
                    src={imagePreview || "/placeholder.svg"}
                    alt="Aperçu"
                    className="w-full h-full object-cover rounded-md"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                    onClick={() => {
                      setImage(null)
                      setImagePreview(null)
                    }}
                  >
                    ×
                  </Button>
                </div>
              )}
            </div>

            {/* Add image visibility controls */}
            {imagePreview && (
              <div className="mt-4 space-y-4 border-t pt-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="showThumbnail">Afficher l'image dans la liste des actualités</Label>
                  <Switch id="showThumbnail" checked={showThumbnail} onCheckedChange={setShowThumbnail} />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="showContentImage">Afficher l'image dans le contenu de l'article</Label>
                  <Switch id="showContentImage" checked={showContentImage} onCheckedChange={setShowContentImage} />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Paramètres de publication</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="isPublished">Publié</Label>
              <Switch id="isPublished" checked={isPublished} onCheckedChange={setIsPublished} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="isPinned">Épinglé</Label>
              <Switch id="isPinned" checked={isPinned} onCheckedChange={setIsPinned} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="showFrame">Afficher avec un cadre</Label>
              <Switch id="showFrame" checked={showFrame} onCheckedChange={setShowFrame} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="showPublicationDate" className="flex-1">
                Afficher la date de publication
                <p className="text-sm text-muted-foreground mt-1">
                  Si désactivé, la date de publication ne sera pas affichée sur la carte d'actualité
                </p>
              </Label>
              <Switch id="showPublicationDate" checked={showPublicationDate} onCheckedChange={setShowPublicationDate} />
            </div>

            {/* Add custom date section */}
            <div className="border-t pt-4 mt-4">
              <div className="flex items-center justify-between mb-4">
                <Label htmlFor="useCustomDate" className="flex-1">
                  Utiliser une date personnalisée
                  <p className="text-sm text-muted-foreground mt-1">
                    Remplacer la date de publication automatique par une date personnalisée
                  </p>
                </Label>
                <Switch id="useCustomDate" checked={useCustomDate} onCheckedChange={setUseCustomDate} />
              </div>

              {useCustomDate && (
                <div className="flex flex-col space-y-2">
                  <Label htmlFor="customDate">Date personnalisée</Label>
                  <div className="flex items-center gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !customDate && "text-muted-foreground",
                          )}
                        >
                          <Calendar className="mr-2 h-4 w-4" />
                          {customDate ? format(customDate, "dd MMMM yyyy", { locale: fr }) : "Sélectionner une date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={customDate}
                          onSelect={(date) => date && setCustomDate(date)}
                          initialFocus
                          locale={fr}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <p className="text-sm text-muted-foreground italic">
                    Cette date sera affichée à la place de la date de création automatique
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Visibilité par groupe</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingGroups ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="all-groups"
                    checked={selectedGroups.length === 0}
                    onCheckedChange={(checked) => {
                      if (checked) setSelectedGroups([])
                    }}
                  />
                  <Label htmlFor="all-groups">Tous les groupes</Label>
                </div>

                {groups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`group-${group.id}`}
                      checked={selectedGroups.includes(group.id)}
                      onCheckedChange={() => handleGroupToggle(group.id)}
                      disabled={selectedGroups.length === 0}
                    />
                    <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.push("/admin/news")} disabled={isLoading}>
            Annuler
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Créer l'article
          </Button>
        </div>
      </form>
    </div>
  )
}
