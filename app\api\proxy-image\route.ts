import { type NextRequest, NextResponse } from "next/server"

export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET(request: NextRequest) {
  try {
    // Récupérer l'URL de l'image depuis les paramètres de requête
    const url = request.nextUrl.searchParams.get("url")

    if (!url) {
      return new NextResponse("URL parameter is required", { status: 400 })
    }

    // Récupérer l'image depuis l'URL fournie
    const imageResponse = await fetch(url, {
      headers: {
        // Ajouter des en-têtes pour éviter les problèmes de cache
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
      },
    })

    if (!imageResponse.ok) {
      return new NextResponse(`Failed to fetch image: ${imageResponse.statusText}`, {
        status: imageResponse.status,
      })
    }

    // Récupérer le type de contenu et les données de l'image
    const contentType = imageResponse.headers.get("content-type") || "image/jpeg"
    const imageData = await imageResponse.arrayBuffer()

    // Renvoyer l'image avec les en-têtes CORS appropriés
    return new NextResponse(imageData, {
      headers: {
        "Content-Type": contentType,
        "Access-Control-Allow-Origin": "*",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    })
  } catch (error) {
    console.error("Error proxying image:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
