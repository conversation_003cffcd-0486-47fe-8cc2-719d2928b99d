import { doc, getDoc } from "firebase/firestore"
import { getAuth } from "firebase/auth"
import { db } from "./firebase"
import { hexToHsl, adjustHslLightness } from "./color-utils"

export type ThemeColors = {
  primary: string
  secondary: string
  accent: string
  success: string
  warning: string
  error: string
  info: string
  background: string
  foreground: string
  muted: string
  mutedForeground: string
  card?: string
  cardForeground?: string
  border?: string
  input?: string
  // Dark mode variants
  darkPrimary?: string
  darkSecondary?: string
  darkAccent?: string
  darkSuccess?: string
  darkWarning?: string
  darkError?: string
  darkInfo?: string
  darkBackground?: string
  darkForeground?: string
  darkMuted?: string
  darkMutedForeground?: string
  darkCard?: string
  darkCardForeground?: string
  darkBorder?: string
  darkInput?: string
}

// Default theme with both light and dark mode colors
export const defaultTheme: ThemeColors = {
  primary: "#3b82f6", // blue-500
  secondary: "#f3f4f6", // gray-100
  accent: "#8b5cf6", // violet-500
  success: "#10b981", // emerald-500
  warning: "#f59e0b", // amber-500
  error: "#ef4444", // red-500
  info: "#06b6d4", // cyan-500
  background: "#ffffff", // white
  foreground: "#1f2937", // gray-800
  muted: "#f3f4f6", // gray-100
  mutedForeground: "#6b7280", // gray-500
  // Dark mode colors
  darkBackground: "#0f172a", // slate-900
  darkForeground: "#f8fafc", // slate-50
  darkMuted: "#1e293b", // slate-800
  darkMutedForeground: "#94a3b8", // slate-400
  darkCard: "#1e293b", // slate-800
  darkCardForeground: "#f8fafc", // slate-50
  darkBorder: "#334155", // slate-700
}

// Modifier la fonction loadThemeFromSettings pour améliorer la gestion du cache

export async function loadThemeFromSettings(): Promise<ThemeColors> {
  try {
    // Check if user is authenticated before accessing settings
    const auth = getAuth()
    const currentUser = auth.currentUser

    // Try to get theme from sessionStorage first (for page navigations)
    const sessionTheme = sessionStorage.getItem("current_theme")
    if (sessionTheme) {
      try {
        return JSON.parse(sessionTheme)
      } catch (e) {
        console.warn("Failed to parse session theme:", e)
      }
    }

    // Then try to get cached theme from localStorage
    const cachedTheme = localStorage.getItem("cached_theme")
    if (cachedTheme) {
      try {
        const parsedTheme = JSON.parse(cachedTheme)
        // Also store in sessionStorage for immediate page navigations
        sessionStorage.setItem("current_theme", cachedTheme)
        return parsedTheme
      } catch (e) {
        console.warn("Failed to parse cached theme:", e)
      }
    }

    if (currentUser) {
      // User is authenticated, try to load settings
      try {
        const siteSettingsDoc = await getDoc(doc(db(), "settings", "site"))

        if (siteSettingsDoc.exists()) {
          const siteData = siteSettingsDoc.data()

          // Start with default theme
          const theme = { ...defaultTheme }

          // Update with stored values if they exist - mode clair
          if (siteData.primary) theme.primary = siteData.primary
          if (siteData.secondary) theme.secondary = siteData.secondary
          if (siteData.accent) theme.accent = siteData.accent
          if (siteData.success) theme.success = siteData.success
          if (siteData.warning) theme.warning = siteData.warning
          if (siteData.error) theme.error = siteData.error
          if (siteData.info) theme.info = siteData.info
          if (siteData.background) theme.background = siteData.background
          if (siteData.foreground) theme.foreground = siteData.foreground
          if (siteData.muted) theme.muted = siteData.muted
          if (siteData.mutedForeground) theme.mutedForeground = siteData.mutedForeground
          if (siteData.card) theme.card = siteData.card
          if (siteData.cardForeground) theme.cardForeground = siteData.cardForeground
          if (siteData.border) theme.border = siteData.border
          if (siteData.input) theme.input = siteData.input

          // Dark mode colors
          if (siteData.darkPrimary) theme.darkPrimary = siteData.darkPrimary
          if (siteData.darkSecondary) theme.darkSecondary = siteData.darkSecondary
          if (siteData.darkAccent) theme.darkAccent = siteData.darkAccent
          if (siteData.darkSuccess) theme.darkSuccess = siteData.darkSuccess
          if (siteData.darkWarning) theme.darkWarning = siteData.darkWarning
          if (siteData.darkError) theme.darkError = siteData.darkError
          if (siteData.darkInfo) theme.darkInfo = siteData.darkInfo
          if (siteData.darkBackground) theme.darkBackground = siteData.darkBackground
          if (siteData.darkForeground) theme.darkForeground = siteData.darkForeground
          if (siteData.darkMuted) theme.darkMuted = siteData.darkMuted
          if (siteData.darkMutedForeground) theme.darkMutedForeground = siteData.darkMutedForeground
          if (siteData.darkCard) theme.darkCard = siteData.darkCard
          if (siteData.darkCardForeground) theme.darkCardForeground = siteData.darkCardForeground
          if (siteData.darkBorder) theme.darkBorder = siteData.darkBorder
          if (siteData.darkInput) theme.darkInput = siteData.darkInput

          // Cache the theme for offline use
          localStorage.setItem("cached_theme", JSON.stringify(theme))
          sessionStorage.setItem("current_theme", JSON.stringify(theme))

          return theme
        }
      } catch (error) {
        console.error("Error loading theme from settings:", error)
        // If there's an error (like being offline), try to use cached theme
        const cachedTheme = localStorage.getItem("cached_theme")
        if (cachedTheme) {
          try {
            const parsedTheme = JSON.parse(cachedTheme)
            sessionStorage.setItem("current_theme", cachedTheme)
            return parsedTheme
          } catch (e) {
            console.warn("Failed to parse cached theme:", e)
          }
        }
      }
    }

    // If user is not authenticated or document doesn't exist, use default theme
    return defaultTheme
  } catch (error) {
    console.error("Error loading theme from settings:", error)
    // In case of error, return default theme
    return defaultTheme
  }
}

// Modifier la fonction applyTheme pour améliorer la persistance du thème

export function applyTheme(theme: ThemeColors, isDarkMode?: boolean): void {
  const root = document.documentElement

  // Check if dark mode is active if not explicitly provided
  if (isDarkMode === undefined) {
    isDarkMode = root.classList.contains("dark")
  }

  // Définir toutes les variables HSL en dehors des blocs conditionnels
  let primaryHsl, secondaryHsl, accentHsl, successHsl, warningHsl, errorHsl, infoHsl
  let backgroundHsl, foregroundHsl, mutedHsl, mutedForegroundHsl
  let cardHsl, cardForegroundHsl, borderHsl, inputHsl
  let ringHsl, primaryForegroundHsl, secondaryForegroundHsl, accentForegroundHsl
  let destructiveForegroundHsl, radiusValue

  if (isDarkMode) {
    // Appliquer les couleurs du mode sombre
    primaryHsl = hexToHsl(theme.darkPrimary || theme.primary)
    secondaryHsl = hexToHsl(theme.darkSecondary || theme.secondary)
    accentHsl = hexToHsl(theme.darkAccent || theme.accent)
    successHsl = hexToHsl(theme.darkSuccess || theme.success)
    warningHsl = hexToHsl(theme.darkWarning || theme.warning)
    errorHsl = hexToHsl(theme.darkError || theme.error)
    infoHsl = hexToHsl(theme.darkInfo || theme.info)
    backgroundHsl = hexToHsl(theme.darkBackground || "#0f172a")
    foregroundHsl = hexToHsl(theme.darkForeground || "#f8fafc")
    mutedHsl = hexToHsl(theme.darkMuted || "#1e293b")
    mutedForegroundHsl = hexToHsl(theme.darkMutedForeground || "#94a3b8")
    cardHsl = hexToHsl(theme.darkCard || theme.darkBackground || "#1e293b")
    cardForegroundHsl = hexToHsl(theme.darkCardForeground || theme.darkForeground || "#f8fafc")
    borderHsl = hexToHsl(theme.darkBorder || "#334155")
    inputHsl = hexToHsl(theme.darkInput || theme.darkMuted || "#1e293b")

    // Derived colors for dark mode
    ringHsl = primaryHsl
    primaryForegroundHsl = hexToHsl("#ffffff")
    secondaryForegroundHsl = hexToHsl("#ffffff")
    accentForegroundHsl = hexToHsl("#ffffff")
    destructiveForegroundHsl = hexToHsl("#ffffff")
  } else {
    // Appliquer les couleurs du mode clair
    primaryHsl = hexToHsl(theme.primary)
    secondaryHsl = hexToHsl(theme.secondary)
    accentHsl = hexToHsl(theme.accent)
    successHsl = hexToHsl(theme.success)
    warningHsl = hexToHsl(theme.warning)
    errorHsl = hexToHsl(theme.error)
    infoHsl = hexToHsl(theme.info)
    backgroundHsl = hexToHsl(theme.background || "#ffffff")
    foregroundHsl = hexToHsl(theme.foreground || "#1f2937")
    mutedHsl = hexToHsl(theme.muted || "#f3f4f6")
    mutedForegroundHsl = hexToHsl(theme.mutedForeground || "#6b7280")
    cardHsl = hexToHsl(theme.card || theme.background || "#ffffff")
    cardForegroundHsl = hexToHsl(theme.cardForeground || theme.foreground || "#1f2937")
    borderHsl = hexToHsl(theme.border || theme.muted || "#e5e7eb")
    inputHsl = hexToHsl(theme.input || theme.muted || "#f3f4f6")

    // Derived colors for light mode
    ringHsl = primaryHsl
    primaryForegroundHsl = hexToHsl("#ffffff")
    secondaryForegroundHsl = hexToHsl("#1f2937")
    accentForegroundHsl = hexToHsl("#ffffff")
    destructiveForegroundHsl = hexToHsl("#ffffff")
  }

  // Set radius value
  radiusValue = "0.5rem"

  // Appliquer les variables CSS
  root.style.setProperty("--primary", primaryHsl)
  root.style.setProperty("--primary-foreground", primaryForegroundHsl)
  root.style.setProperty("--secondary", secondaryHsl)
  root.style.setProperty("--secondary-foreground", secondaryForegroundHsl)
  root.style.setProperty("--accent", accentHsl)
  root.style.setProperty("--accent-foreground", accentForegroundHsl)
  root.style.setProperty("--success", successHsl)
  root.style.setProperty("--warning", warningHsl)
  root.style.setProperty("--destructive", errorHsl)
  root.style.setProperty("--destructive-foreground", destructiveForegroundHsl)
  root.style.setProperty("--info", infoHsl)
  root.style.setProperty("--background", backgroundHsl)
  root.style.setProperty("--foreground", foregroundHsl)
  root.style.setProperty("--muted", mutedHsl)
  root.style.setProperty("--muted-foreground", mutedForegroundHsl)
  root.style.setProperty("--card", cardHsl)
  root.style.setProperty("--card-foreground", cardForegroundHsl)
  root.style.setProperty("--popover", cardHsl)
  root.style.setProperty("--popover-foreground", cardForegroundHsl)
  root.style.setProperty("--border", borderHsl)
  root.style.setProperty("--input", inputHsl)
  root.style.setProperty("--ring", ringHsl)
  root.style.setProperty("--radius", radiusValue)

  // Generate derived colors for primary
  const primaryDarker = adjustHslLightness(primaryHsl, -10)
  const primaryLighter = adjustHslLightness(primaryHsl, 10)
  root.style.setProperty("--primary-darker", primaryDarker)
  root.style.setProperty("--primary-lighter", primaryLighter)

  // Store the theme in localStorage for persistence
  localStorage.setItem("cached_theme", JSON.stringify(theme))

  // Also store in sessionStorage for immediate page navigations
  sessionStorage.setItem("current_theme", JSON.stringify(theme))

  // Create a style element with CSS variables for SSR consistency
  const styleId = "theme-variables"
  let styleEl = document.getElementById(styleId) as HTMLStyleElement

  if (!styleEl) {
    styleEl = document.createElement("style")
    styleEl.id = styleId
    document.head.appendChild(styleEl)
  }

  // Add CSS variables as a style element to ensure they persist
  styleEl.textContent = `
    :root {
      --primary: ${primaryHsl};
      --primary-foreground: ${primaryForegroundHsl};
      --secondary: ${secondaryHsl};
      --secondary-foreground: ${secondaryForegroundHsl};
      --accent: ${accentHsl};
      --accent-foreground: ${accentForegroundHsl};
      --success: ${successHsl};
      --warning: ${warningHsl};
      --destructive: ${errorHsl};
      --destructive-foreground: ${destructiveForegroundHsl};
      --info: ${infoHsl};
      --background: ${backgroundHsl};
      --foreground: ${foregroundHsl};
      --muted: ${mutedHsl};
      --muted-foreground: ${mutedForegroundHsl};
      --card: ${cardHsl};
      --card-foreground: ${cardForegroundHsl};
      --popover: ${cardHsl};
      --popover-foreground: ${cardForegroundHsl};
      --border: ${borderHsl};
      --input: ${inputHsl};
      --ring: ${ringHsl};
      --radius: ${radiusValue};
      --primary-darker: ${primaryDarker};
      --primary-lighter: ${primaryLighter};
    }
  `

  // Dispatch a custom event to notify components that the theme has changed
  const themeChangeEvent = new CustomEvent("themechange", {
    detail: { theme, isDarkMode },
  })
  document.dispatchEvent(themeChangeEvent)
}

// Add a function to force reload theme for all users
export async function forceReloadThemeForAllUsers() {
  try {
    // Clear any cached themes to ensure fresh load
    localStorage.removeItem("cached_theme")
    sessionStorage.removeItem("current_theme")

    // Load fresh theme from Firestore
    const theme = await loadThemeFromSettings()

    // Apply the theme with current dark mode state
    const isDark = document.documentElement.classList.contains("dark")
    applyTheme(theme, isDark)

    return true
  } catch (error) {
    console.error("Failed to force reload theme:", error)
    return false
  }
}
