"use client"

import { Card } from "@/components/ui/card"
import Link from "next/link"
import { FavoriteButton } from "@/components/auth-provider"
import { useAuth } from "@/components/auth-provider"
import { cn } from "@/lib/utils"
import { OptimizedImage } from "@/components/optimized-image"
import { useState, useEffect } from "react"
import { getDisplaySettings } from "@/lib/display-settings"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface NewsItemProps {
  item: {
    id: string
    title: string
    summary?: string
    imageUrl?: string
    date: Date
    isPinned?: boolean
    showThumbnail?: boolean
    showPublicationDate?: boolean
    useCustomDate?: boolean
    customDate?: Date
  }
}

export function NewsFeedItem({ item }: NewsItemProps) {
  const { user } = useAuth()
  const [showDate, setShowDate] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  useEffect(() => {
    const loadDisplaySettings = async () => {
      try {
        const settings = await getDisplaySettings()
        setShowDate(settings.showPublicationDates)
      } catch (error) {
        // If we can't load display settings (e.g., offline), default to showing dates
        console.warn("Could not load display settings, defaulting to showing dates:", error)
        setShowDate(true)
      }
    }

    loadDisplaySettings()
  }, [])

  // Préchargement de l'image
  useEffect(() => {
    if (item.imageUrl) {
      const img = new Image()
      img.src = item.imageUrl
      img.onload = () => setImageLoaded(true)
      img.onerror = () => setImageError(true)
    }
  }, [item.imageUrl])

  // Determine if we should show the image
  const shouldShowImage = item.imageUrl && !imageError && item.showThumbnail !== false

  // Determine which date to display
  const displayDate = item.useCustomDate && item.customDate ? item.customDate : item.date
  const isCustomDate = item.useCustomDate && item.customDate

  return (
    <Link href={`/dashboard/news/${item.id}`} passHref>
      <Card
        className={cn(
          "overflow-hidden transition-all hover:shadow-md group p-0 dark-mode-transition news-feed-item",
          item.isPinned
            ? "border-blue-200 bg-blue-50/30 dark:border-blue-800 dark:bg-blue-950/30"
            : "border-gray-200 dark:border-gray-800 dark:bg-gray-900/40",
        )}
      >
        <div className="flex flex-row h-32 md:h-40 relative">
          {/* Square thumbnail image on the left for all screen sizes */}
          {shouldShowImage ? (
            <div className="relative w-32 h-32 md:w-40 md:h-40 flex-shrink-0 bg-gray-100 dark:bg-gray-800 overflow-hidden">
              <OptimizedImage
                src={item.imageUrl}
                alt={item.title}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
                onError={() => setImageError(true)}
                priority={true} // Marquer comme prioritaire pour charger plus tôt
                unoptimized // Désactiver l'optimisation Next.js pour permettre le cache du service worker
                fallbackSrc="/placeholder.svg" // Image de secours en cas d'erreur
              />
            </div>
          ) : (
            <div className="w-4 flex-shrink-0"></div>
          )}

          {/* Content container */}
          <div className="flex-1 p-2 md:p-3 flex flex-col h-full relative">
            {/* Main content area */}
            <div className="flex-grow overflow-hidden mb-2">
              <h3 className="text-sm md:text-base font-semibold text-blue-900 dark:text-blue-300 min-h-[1.25rem] md:min-h-[1.5rem] mb-1 md:mb-2">
                {item.title}
              </h3>

              {item.summary && (
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300 mt-0.5 line-clamp-3 md:line-clamp-4">
                  {item.summary}
                </p>
              )}
            </div>

            {/* Bottom row with date */}
            <div className="flex items-center mt-auto">
              {showDate && item.showPublicationDate !== false && (
                <>
                  <span
                    className={cn(
                      "text-xs",
                      isCustomDate
                        ? "text-blue-600 dark:text-blue-400 font-medium"
                        : "text-gray-500 dark:text-gray-400",
                    )}
                  >
                    {format(displayDate, "dd/MM/yyyy", { locale: fr })}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Favorite button - positioned absolutely in the bottom right corner */}
          {user && (
            <div className="absolute bottom-2 right-2 md:bottom-3 md:right-3 z-10">
              <div className="bg-white/70 dark:bg-gray-800/70 rounded-full p-0.5 backdrop-blur-sm">
                <FavoriteButton newsId={item.id} userId={user.uid} size="xs" />
              </div>
            </div>
          )}
        </div>
      </Card>
    </Link>
  )
}
