/**
 * Tests automatisés pour la synchronisation
 * Ce script teste la synchronisation bidirectionnelle des données
 * en utilisant Puppeteer pour simuler un navigateur
 */

const puppeteer = require("puppeteer")
const fs = require("fs")
const path = require("path")

// Configuration
const config = {
  baseUrl: "http://localhost:3000",
  loginEmail: "<EMAIL>",
  loginPassword: "password",
  outputDir: path.join(__dirname, "reports"),
  screenshotDir: path.join(__dirname, "screenshots"),
  timeout: 30000, // 30 secondes
}

// Créer les répertoires de sortie s'ils n'existent pas
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true })
}
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true })
}

// Fonction pour générer un rapport
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, "-")
  const reportPath = path.join(config.outputDir, `sync-test-report-${timestamp}.json`)

  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
  console.log(`Rapport généré: ${reportPath}`)

  // Générer un rapport HTML
  const htmlReportPath = path.join(config.outputDir, `sync-test-report-${timestamp}.html`)
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de test de synchronisation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .summary { display: flex; margin-bottom: 20px; }
        .summary-box { flex: 1; padding: 15px; margin: 0 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .failure { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .test-case { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .test-case h3 { margin-top: 0; }
        .passed { border-left: 5px solid #28a745; }
        .failed { border-left: 5px solid #dc3545; }
        .skipped { border-left: 5px solid #ffc107; }
        .error { color: #dc3545; }
        .steps { margin-left: 20px; }
        .step { margin-bottom: 5px; }
        .step-passed { color: #28a745; }
        .step-failed { color: #dc3545; }
        .screenshot { max-width: 100%; height: auto; border: 1px solid #ddd; margin-top: 10px; }
        .operation { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        .operation-title { font-weight: bold; margin-bottom: 5px; }
        .operation-details { margin-left: 15px; font-family: monospace; }
      </style>
    </head>
    <body>
      <h1>Rapport de test de synchronisation</h1>
      <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
      <p><strong>URL de base:</strong> ${config.baseUrl}</p>
      
      <div class="summary">
        <div class="summary-box ${results.success ? "success" : "failure"}">
          <h2>Résultat global</h2>
          <p>${results.success ? "Succès" : "Échec"}</p>
          <p>${results.testsPassed}/${results.totalTests} tests réussis</p>
        </div>
        <div class="summary-box info">
          <h2>Durée</h2>
          <p>${results.duration} secondes</p>
        </div>
      </div>
      
      <h2>Détails des tests</h2>
      ${results.tests
        .map(
          (test) => `
        <div class="test-case ${test.success ? "passed" : "failed"}">
          <h3>${test.name}</h3>
          <p><strong>Statut:</strong> ${test.success ? "Réussi" : "Échoué"}</p>
          ${test.error ? `<p class="error"><strong>Erreur:</strong> ${test.error}</p>` : ""}
          
          <div class="steps">
            ${test.steps
              .map(
                (step) => `
              <div class="step ${step.success ? "step-passed" : "step-failed"}">
                ${step.success ? "✓" : "✗"} ${step.name}
                ${step.error ? `<div class="error">${step.error}</div>` : ""}
              </div>
            `,
              )
              .join("")}
          </div>
          
          ${
            test.operations
              ? `
            <h4>Opérations de synchronisation</h4>
            ${test.operations
              .map(
                (op) => `
              <div class="operation">
                <div class="operation-title">${op.type} - ${op.collection}/${op.documentId}</div>
                <div class="operation-details">
                  <div>Status: ${op.status}</div>
                  ${op.data ? `<div>Data: ${JSON.stringify(op.data)}</div>` : ""}
                </div>
              </div>
            `,
              )
              .join("")}
          `
              : ""
          }
          
          ${test.screenshot ? `<img class="screenshot" src="../screenshots/${path.basename(test.screenshot)}" alt="Capture d'écran">` : ""}
        </div>
      `,
        )
        .join("")}
    </body>
    </html>
  `

  fs.writeFileSync(htmlReportPath, htmlContent)
  console.log(`Rapport HTML généré: ${htmlReportPath}`)

  return reportPath
}

// Fonction principale pour exécuter les tests
async function runTests() {
  console.log("Démarrage des tests de synchronisation...")

  const startTime = Date.now()
  const browser = await puppeteer.launch({
    headless: false, // Mettre à true pour exécuter sans interface graphique
    defaultViewport: { width: 1280, height: 800 },
    args: ["--window-size=1280,800"],
  })

  const results = {
    timestamp: new Date().toISOString(),
    baseUrl: config.baseUrl,
    success: false,
    testsPassed: 0,
    totalTests: 0,
    duration: 0,
    tests: [],
  }

  try {
    const page = await browser.newPage()
    page.setDefaultTimeout(config.timeout)

    // Activer la journalisation de la console
    page.on("console", (msg) => console.log(`[Page Console] ${msg.text()}`))

    // Test 1: Création de données en mode hors ligne
    const offlineCreateTest = {
      name: "Création de données en mode hors ligne",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
      operations: [],
    }

    try {
      // Étape 1: Se connecter à l'application
      offlineCreateTest.steps.push({ name: "Connexion à l'application", success: false })
      await page.goto(`${config.baseUrl}/`, { waitUntil: "networkidle2" })

      // Vérifier si nous sommes déjà connectés
      const currentUrl = page.url()
      if (!currentUrl.includes("/dashboard")) {
        await page.waitForSelector('input[type="email"]')
        await page.type('input[type="email"]', config.loginEmail)
        await page.type('input[type="password"]', config.loginPassword)
        await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('button[type="submit"]')])
      }

      // Vérifier que nous sommes bien sur le tableau de bord
      if (!page.url().includes("/dashboard")) {
        throw new Error("Échec de la connexion")
      }

      offlineCreateTest.steps[0].success = true

      // Étape 2: Passer en mode hors ligne
      offlineCreateTest.steps.push({ name: "Passage en mode hors ligne", success: false })
      await page.setOfflineMode(true)
      await page.waitForTimeout(1000) // Attendre que le mode hors ligne soit détecté
      offlineCreateTest.steps[1].success = true

      // Étape 3: Accéder à la page de création de favoris
      offlineCreateTest.steps.push({ name: "Accès à la page de création de favoris", success: false })
      await Promise.all([
        page.waitForNavigation({ waitUntil: "networkidle2" }),
        page.click('a[href="/dashboard/favorites"]'),
      ])
      offlineCreateTest.steps[2].success = true

      // Étape 4: Créer un nouveau favori
      offlineCreateTest.steps.push({ name: "Création d'un nouveau favori", success: false })

      // Générer un titre unique
      const favoriteTitle = `Test favori ${Date.now()}`

      // Cliquer sur le bouton d'ajout
      await page.waitForSelector('button:has-text("Ajouter")')
      await page.click('button:has-text("Ajouter")')

      // Remplir le formulaire
      await page.waitForSelector('input[name="title"]')
      await page.type('input[name="title"]', favoriteTitle)
      await page.type('input[name="url"]', "https://example.com")

      // Soumettre le formulaire
      await page.click('button[type="submit"]')

      // Attendre que le formulaire soit soumis
      await page.waitForTimeout(1000)

      offlineCreateTest.steps[3].success = true

      // Étape 5: Vérifier que l'opération est en attente
      offlineCreateTest.steps.push({ name: "Vérification de l'opération en attente", success: false })

      // Accéder à la page de synchronisation
      await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('a[href="/admin"]')])

      await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('a[href="/admin/sync"]')])

      // Vérifier qu'il y a des opérations en attente
      await page.waitForSelector(".test-case")

      // Récupérer les opérations en attente
      const pendingOperations = await page.evaluate(() => {
        const operations = []
        const operationElements = document.querySelectorAll(".operation")

        operationElements.forEach((element) => {
          const titleElement = element.querySelector(".operation-title")
          const detailsElement = element.querySelector(".operation-details")

          if (titleElement && detailsElement) {
            const titleText = titleElement.textContent
            const detailsText = detailsElement.textContent

            // Extraire le type et la collection/documentId
            const typeMatch = titleText.match(/^(\w+)\s+-\s+(.+)$/)
            if (typeMatch) {
              const type = typeMatch[1]
              const path = typeMatch[2]
              const pathParts = path.split("/")

              operations.push({
                type,
                collection: pathParts[0],
                documentId: pathParts[1],
                status: detailsText.includes("Status:") ? detailsText.match(/Status:\s+(\w+)/)[1] : "unknown",
                data: detailsText.includes("Data:") ? JSON.parse(detailsText.match(/Data:\s+(.+)$/)[1]) : null,
              })
            }
          }
        })

        return operations
      })

      // Vérifier qu'il y a au moins une opération de création
      const createOperations = pendingOperations.filter((op) => op.type === "CREATE" && op.collection === "favorites")

      if (createOperations.length === 0) {
        throw new Error("Aucune opération de création de favori en attente")
      }

      offlineCreateTest.operations = pendingOperations
      offlineCreateTest.steps[4].success = true

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "offline-create-test.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      offlineCreateTest.screenshot = screenshotPath

      offlineCreateTest.success = true
    } catch (error) {
      offlineCreateTest.error = error.message
      console.error("Erreur lors du test de création en mode hors ligne:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "offline-create-test-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      offlineCreateTest.screenshot = screenshotPath
    }

    results.tests.push(offlineCreateTest)
    results.totalTests++
    if (offlineCreateTest.success) results.testsPassed++

    // Test 2: Synchronisation des données au retour en ligne
    const syncTest = {
      name: "Synchronisation des données au retour en ligne",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
      operations: [],
    }

    try {
      // Ne pas exécuter ce test si le test précédent a échoué
      if (!offlineCreateTest.success) {
        syncTest.error = "Test ignoré car le test de création en mode hors ligne a échoué"
        syncTest.steps.push({ name: "Test ignoré", success: false })
        throw new Error("Test ignoré car le test de création en mode hors ligne a échoué")
      }

      // Étape 1: Revenir en mode en ligne
      syncTest.steps.push({ name: "Retour en mode en ligne", success: false })
      await page.setOfflineMode(false)
      await page.waitForTimeout(2000) // Attendre que le mode en ligne soit détecté
      syncTest.steps[0].success = true

      // Étape 2: Déclencher la synchronisation
      syncTest.steps.push({ name: "Déclenchement de la synchronisation", success: false })

      // Cliquer sur le bouton de synchronisation
      await page.waitForSelector('button:has-text("Synchroniser maintenant")')
      await page.click('button:has-text("Synchroniser maintenant")')

      // Attendre que la synchronisation soit terminée
      await page.waitForTimeout(5000)

      syncTest.steps[1].success = true

      // Étape 3: Vérifier que les opérations ont été synchronisées
      syncTest.steps.push({ name: "Vérification de la synchronisation des opérations", success: false })

      // Actualiser la page
      await page.reload({ waitUntil: "networkidle2" })

      // Récupérer les opérations
      const operations = await page.evaluate(() => {
        const operations = []
        const operationElements = document.querySelectorAll(".operation")

        operationElements.forEach((element) => {
          const titleElement = element.querySelector(".operation-title")
          const detailsElement = element.querySelector(".operation-details")

          if (titleElement && detailsElement) {
            const titleText = titleElement.textContent
            const detailsText = detailsElement.textContent

            // Extraire le type et la collection/documentId
            const typeMatch = titleText.match(/^(\w+)\s+-\s+(.+)$/)
            if (typeMatch) {
              const type = typeMatch[1]
              const path = typeMatch[2]
              const pathParts = path.split("/")

              operations.push({
                type,
                collection: pathParts[0],
                documentId: pathParts[1],
                status: detailsText.includes("Status:") ? detailsText.match(/Status:\s+(\w+)/)[1] : "unknown",
                data: detailsText.includes("Data:") ? JSON.parse(detailsText.match(/Data:\s+(.+)$/)[1]) : null,
              })
            }
          }
        })

        return operations
      })

      // Vérifier que les opérations de création ont été synchronisées
      const completedOperations = operations.filter((op) => op.status === "completed")

      if (completedOperations.length === 0) {
        throw new Error("Aucune opération n'a été synchronisée")
      }

      syncTest.operations = operations
      syncTest.steps[2].success = true

      // Étape 4: Vérifier que le favori a été créé
      syncTest.steps.push({ name: "Vérification de la création du favori", success: false })

      // Accéder à la page des favoris
      await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('a[href="/dashboard"]')])

      await Promise.all([
        page.waitForNavigation({ waitUntil: "networkidle2" }),
        page.click('a[href="/dashboard/favorites"]'),
      ])

      // Vérifier que le favori est présent
      const favoriteTitle = offlineCreateTest.operations.find(
        (op) => op.type === "CREATE" && op.collection === "favorites",
      )?.data?.title

      if (favoriteTitle) {
        const favoriteElement = await page.$(`text=${favoriteTitle}`)

        if (!favoriteElement) {
          throw new Error(`Le favori "${favoriteTitle}" n'a pas été trouvé`)
        }
      } else {
        throw new Error("Impossible de déterminer le titre du favori créé")
      }

      syncTest.steps[3].success = true

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "sync-test.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      syncTest.screenshot = screenshotPath

      syncTest.success = true
    } catch (error) {
      syncTest.error = error.message
      console.error("Erreur lors du test de synchronisation:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "sync-test-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      syncTest.screenshot = screenshotPath
    }

    results.tests.push(syncTest)
    results.totalTests++
    if (syncTest.success) results.testsPassed++

    // Calculer le résultat global
    results.success = results.testsPassed === results.totalTests
    results.duration = ((Date.now() - startTime) / 1000).toFixed(2)

    // Générer le rapport
    generateReport(results)
  } catch (error) {
    console.error("Erreur lors de l'exécution des tests:", error)
    results.error = error.message
  } finally {
    await browser.close()
  }

  return results
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  runTests()
    .then((results) => {
      console.log(`Tests terminés: ${results.testsPassed}/${results.totalTests} réussis`)
      process.exit(results.success ? 0 : 1)
    })
    .catch((error) => {
      console.error("Erreur lors de l'exécution des tests:", error)
      process.exit(1)
    })
}

module.exports = { runTests }
