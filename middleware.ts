import { type NextRequest, NextResponse } from "next/server"

// Spécifier que ce middleware doit s'exécuter dans l'environnement Node.js et non Edge
export const runtime = "nodejs"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Vérifier si l'utilisateur accède à la page d'accueil
  if (pathname === "/") {
    try {
      // Récupérer le cookie de session
      const sessionCookie = request.cookies.get("__session")?.value

      if (sessionCookie) {
        // Vérifier le cookie de session via une API interne
        const response = await fetch(`${request.nextUrl.origin}/api/auth/check-session`, {
          method: "GET",
          headers: {
            Cookie: `__session=${sessionCookie}`,
            "Content-Type": "application/json",
          },
          cache: "no-store",
        })

        const data = await response.json()

        if (data.authenticated && data.user) {
          console.log("Utilisateur authentifié détecté via middleware:", data.user.uid)

          // Rediriger vers le dashboard
          const dashboardUrl = new URL("/dashboard", request.url)
          return NextResponse.redirect(dashboardUrl)
        }
      }

      console.log("Aucune session valide trouvée, affichage de la page d'accueil")
    } catch (error) {
      console.error("Erreur lors de la vérification d'authentification:", error)
    }
  }

  // Vérifier si l'utilisateur accède au dashboard sans être connecté
  if (pathname.startsWith("/dashboard")) {
    try {
      const sessionCookie = request.cookies.get("__session")?.value

      if (!sessionCookie) {
        console.log("Accès au dashboard sans cookie de session, redirection vers /")
        const homeUrl = new URL("/", request.url)
        return NextResponse.redirect(homeUrl)
      }

      // Vérifier le cookie de session via une API interne
      const response = await fetch(`${request.nextUrl.origin}/api/auth/check-session`, {
        method: "GET",
        headers: {
          Cookie: `__session=${sessionCookie}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      })

      const data = await response.json()

      if (!data.authenticated || !data.user) {
        console.log("Cookie de session invalide pour l'accès au dashboard")
        const homeUrl = new URL("/", request.url)
        const nextResponse = NextResponse.redirect(homeUrl)
        // Supprimer le cookie invalide
        nextResponse.cookies.delete("__session")
        return nextResponse
      }

      console.log("Accès autorisé au dashboard pour:", data.user.uid)
    } catch (error) {
      console.error("Erreur lors de la vérification d'accès au dashboard:", error)
      const homeUrl = new URL("/", request.url)
      return NextResponse.redirect(homeUrl)
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public|logo-acr-direct.png|android-chrome|apple-touch|favicon).*)",
  ],
}
