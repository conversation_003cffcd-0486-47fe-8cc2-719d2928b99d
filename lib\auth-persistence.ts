import localforage from "localforage"
import type { User } from "firebase/auth"
import { auth } from "@/lib/firebase"

// Clés pour le stockage
const AUTH_USER_KEY = "auth_user"
const AUTH_SESSION_KEY = "auth_session"
const AUTH_TIMESTAMP_KEY = "auth_timestamp"

// Configuration de localForage avec des options optimisées pour mobile
const authStore = localforage.createInstance({
  name: "acrDirect",
  storeName: "authData",
  description: "Stockage persistant des données d'authentification",
  driver: [localforage.INDEXEDDB, localforage.WEBSQL, localforage.LOCALSTORAGE], // Utiliser tous les drivers disponibles
})

/**
 * Stocke les informations d'authentification de l'utilisateur pour la persistance
 * @param user L'utilisateur Firebase authentifié
 */
export async function storeAuthUser(user: User | null): Promise<void> {
  if (!user) {
    await clearAuthData()
    return
  }

  try {
    // Stocker les informations essentielles de l'utilisateur d'abord
    // pour garantir qu'elles sont sauvegardées même si l'obtention du token échoue
    const userInfo = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      lastLoginAt: Date.now(),
    }

    // Stocker les données utilisateur dans localforage
    await authStore.setItem(AUTH_USER_KEY, userInfo)
    await authStore.setItem(AUTH_TIMESTAMP_KEY, Date.now())

    // Stocker aussi dans localStorage pour une récupération rapide
    try {
      localStorage.setItem(AUTH_USER_KEY, JSON.stringify(userInfo))
      localStorage.setItem(AUTH_TIMESTAMP_KEY, Date.now().toString())
    } catch (e) {
      console.error("Erreur localStorage:", e)
    }

    // Tenter d'obtenir le token ID avec gestion d'erreur
    try {
      // Obtenir le token ID avec un timeout pour éviter les blocages
      const tokenPromise = user.getIdToken(true)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout lors de l'obtention du token")), 5000),
      )

      const token = (await Promise.race([tokenPromise, timeoutPromise])) as string

      // Stocker le token dans localforage
      await authStore.setItem(AUTH_SESSION_KEY, token)

      // Stocker aussi dans localStorage
      try {
        localStorage.setItem(AUTH_SESSION_KEY, token)
      } catch (e) {
        console.error("Erreur localStorage:", e)
      }

      console.log("Données d'authentification complètes stockées avec succès")
    } catch (tokenError) {
      console.warn("Impossible d'obtenir le token d'authentification:", tokenError)
      console.log("Données utilisateur stockées sans token - fonctionnalité limitée possible")

      // Tenter de récupérer un token existant comme solution de secours
      const existingToken = await getStoredAuthToken()
      if (existingToken) {
        console.log("Utilisation d'un token existant comme solution de secours")
      } else {
        console.warn("Aucun token de secours disponible - certaines fonctionnalités peuvent être limitées")
      }
    }
  } catch (error) {
    console.error("Erreur lors du stockage des données d'authentification:", error)
    // Tenter une dernière sauvegarde d'urgence des informations minimales
    try {
      if (user && user.uid) {
        localStorage.setItem("emergency_auth_uid", user.uid)
        localStorage.setItem("emergency_auth_timestamp", Date.now().toString())
      }
    } catch (e) {
      console.error("Échec de la sauvegarde d'urgence:", e)
    }
  }
}

/**
 * Récupère les informations d'authentification stockées
 */
export async function getStoredAuthUser(): Promise<{ uid: string; email: string | null } | null> {
  try {
    // Essayer d'abord localStorage pour une récupération rapide
    try {
      const localData = localStorage.getItem(AUTH_USER_KEY)
      if (localData) {
        return JSON.parse(localData)
      }
    } catch (e) {
      console.log("Pas de données dans localStorage")
    }

    // Récupérer les informations utilisateur depuis localforage
    return await authStore.getItem<{ uid: string; email: string | null }>(AUTH_USER_KEY)
  } catch (error) {
    console.error("Erreur lors de la récupération des données d'authentification:", error)
    return null
  }
}

/**
 * Récupère le token d'authentification stocké
 */
export async function getStoredAuthToken(): Promise<string | null> {
  try {
    // Essayer d'abord localStorage pour une récupération rapide
    try {
      const localToken = localStorage.getItem(AUTH_SESSION_KEY)
      if (localToken) {
        return localToken
      }
    } catch (e) {
      console.log("Pas de token dans localStorage")
    }

    // Récupérer le token depuis localforage
    return await authStore.getItem<string>(AUTH_SESSION_KEY)
  } catch (error) {
    console.error("Erreur lors de la récupération du token d'authentification:", error)
    return null
  }
}

/**
 * Vérifie si l'utilisateur a une session active
 */
export async function checkAuthSession(): Promise<boolean> {
  try {
    // Vérifier si les données utilisateur existent
    const userInfo = await getStoredAuthUser()
    const token = await getStoredAuthToken()
    return !!(userInfo && token)
  } catch (error) {
    console.error("Erreur lors de la vérification de la session:", error)
    return false
  }
}

/**
 * Nettoie les données d'authentification stockées
 */
export async function clearAuthData(): Promise<void> {
  try {
    await authStore.removeItem(AUTH_USER_KEY)
    await authStore.removeItem(AUTH_SESSION_KEY)
    await authStore.removeItem(AUTH_TIMESTAMP_KEY)

    // Nettoyer aussi localStorage
    try {
      localStorage.removeItem(AUTH_USER_KEY)
      localStorage.removeItem(AUTH_SESSION_KEY)
      localStorage.removeItem(AUTH_TIMESTAMP_KEY)
    } catch (e) {
      console.error("Erreur localStorage:", e)
    }

    console.log("Données d'authentification supprimées avec succès")
  } catch (error) {
    console.error("Erreur lors de la suppression des données d'authentification:", error)
  }
}

/**
 * Met à jour le timestamp de la session pour éviter l'expiration
 */
export async function refreshAuthSession(): Promise<void> {
  try {
    const now = Date.now()
    await authStore.setItem(AUTH_TIMESTAMP_KEY, now)

    // Mettre à jour aussi dans localStorage
    try {
      localStorage.setItem(AUTH_TIMESTAMP_KEY, now.toString())
    } catch (e) {
      console.error("Erreur localStorage:", e)
    }
  } catch (error) {
    console.error("Erreur lors du rafraîchissement de la session:", error)
  }
}

/**
 * Tente de restaurer une session d'authentification à partir des données stockées
 * @returns true si la restauration a réussi, false sinon
 */
export async function restoreAuthSession(): Promise<boolean> {
  try {
    // Vérifier si nous avons déjà un utilisateur authentifié
    if (auth().currentUser) {
      console.log("Session déjà active, pas besoin de restauration")
      return true
    }

    // Récupérer les données stockées
    const userInfo = await getStoredAuthUser()
    const token = await getStoredAuthToken()

    if (!userInfo || !token) {
      console.log("Pas de données de session à restaurer")
      return false
    }

    console.log("Tentative de restauration de session pour", userInfo.uid)

    // Attendre un court instant pour voir si Firebase restaure automatiquement la session
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Vérifier si la session a été restaurée automatiquement
    if (auth().currentUser) {
      console.log("Session restaurée automatiquement par Firebase")
      return true
    }

    // Si nous arrivons ici, la restauration automatique a échoué
    console.log("Échec de la restauration automatique de session")
    return false
  } catch (error) {
    console.error("Erreur lors de la restauration de la session:", error)
    return false
  }
}

/**
 * Vérifie si la session est toujours valide et la rafraîchit si nécessaire
 */
export async function validateAndRefreshSession(): Promise<boolean> {
  try {
    // Si nous avons un utilisateur authentifié, rafraîchir son token
    if (auth().currentUser) {
      await auth().currentUser.getIdToken(true)
      await refreshAuthSession()
      return true
    }

    // Sinon, essayer de restaurer la session
    return await restoreAuthSession()
  } catch (error) {
    console.error("Erreur lors de la validation de la session:", error)
    return false
  }
}
