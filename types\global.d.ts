import type React from "react"
interface Window {
  cacheAuthData?: (data: { token: string; uid: string }) => void
  swUpdateReady?: boolean
  workbox?: any
  forceKeepSessionAlive?: () => void
  forceMaximumPersistence?: () => Promise<boolean>
  checkAuthServiceWorker?: () => Promise<{
    version: string
    isActive: boolean
    authCacheExists: boolean
    authDataExists: boolean
  }>
}

declare module "*.svg" {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>
  export default content
}
