/**
 * Script pour exécuter tous les tests
 * Ce script exécute tous les tests et génère un rapport global
 */

const { runTests: runOfflineTests } = require("./offline-tests")
const { runTests: runCacheStrategyTests } = require("./cache-strategy-tests")
const { runTests: runSyncTests } = require("./sync-tests")
const fs = require("fs")
const path = require("path")

// Configuration
const config = {
  outputDir: path.join(__dirname, "reports"),
}

// Créer le répertoire de sortie s'il n'existe pas
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true })
}

// Fonction pour générer un rapport global
function generateGlobalReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, "-")
  const reportPath = path.join(config.outputDir, `global-test-report-${timestamp}.json`)

  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
  console.log(`Rapport global généré: ${reportPath}`)

  // Générer un rapport HTML
  const htmlReportPath = path.join(config.outputDir, `global-test-report-${timestamp}.html`)
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport global des tests</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .summary { display: flex; margin-bottom: 20px; }
        .summary-box { flex: 1; padding: 15px; margin: 0 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .failure { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .test-suite { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .test-suite h3 { margin-top: 0; }
        .passed { border-left: 5px solid #28a745; }
        .failed { border-left: 5px solid #dc3545; }
        .error { color: #dc3545; }
        .test-case { margin-left: 20px; margin-bottom: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        .test-case-title { font-weight: bold; }
        .test-case-passed { color: #28a745; }
        .test-case-failed { color: #dc3545; }
      </style>
    </head>
    <body>
      <h1>Rapport global des tests</h1>
      <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
      
      <div class="summary">
        <div class="summary-box ${results.success ? "success" : "failure"}">
          <h2>Résultat global</h2>
          <p>${results.success ? "Succès" : "Échec"}</p>
          <p>${results.testsPassed}/${results.totalTests} tests réussis</p>
        </div>
        <div class="summary-box info">
          <h2>Durée totale</h2>
          <p>${results.duration} secondes</p>
        </div>
      </div>
      
      <h2>Détails des suites de tests</h2>
      ${results.testSuites
        .map(
          (suite) => `
        <div class="test-suite ${suite.success ? "passed" : "failed"}">
          <h3>${suite.name}</h3>
          <p><strong>Statut:</strong> ${suite.success ? "Réussi" : "Échoué"}</p>
          <p><strong>Tests réussis:</strong> ${suite.testsPassed}/${suite.totalTests}</p>
          <p><strong>Durée:</strong> ${suite.duration} secondes</p>
          ${suite.error ? `<p class="error"><strong>Erreur:</strong> ${suite.error}</p>` : ""}
          
          <h4>Tests</h4>
          ${suite.tests
            .map(
              (test) => `
            <div class="test-case">
              <div class="test-case-title ${test.success ? "test-case-passed" : "test-case-failed"}">
                ${test.success ? "✓" : "✗"} ${test.name}
              </div>
              ${test.error ? `<div class="error">Erreur: ${test.error}</div>` : ""}
            </div>
          `,
            )
            .join("")}
          
          <p><a href="${suite.reportPath}" target="_blank">Voir le rapport détaillé</a></p>
        </div>
      `,
        )
        .join("")}
    </body>
    </html>
  `

  fs.writeFileSync(htmlReportPath, htmlContent)
  console.log(`Rapport HTML global généré: ${htmlReportPath}`)

  return reportPath
}

// Fonction principale pour exécuter tous les tests
async function runAllTests() {
  console.log("Démarrage de tous les tests...")

  const startTime = Date.now()
  const results = {
    timestamp: new Date().toISOString(),
    success: false,
    testsPassed: 0,
    totalTests: 0,
    duration: 0,
    testSuites: [],
  }

  try {
    // Exécuter les tests de fonctionnement hors ligne
    console.log("\n=== Tests de fonctionnement hors ligne ===")
    const offlineResults = await runOfflineTests()
    results.testSuites.push({
      name: "Tests de fonctionnement hors ligne",
      success: offlineResults.success,
      testsPassed: offlineResults.testsPassed,
      totalTests: offlineResults.totalTests,
      duration: offlineResults.duration,
      error: offlineResults.error,
      tests: offlineResults.tests,
      reportPath: path.relative(config.outputDir, offlineResults.reportPath || ""),
    })

    // Exécuter les tests de stratégies de mise en cache
    console.log("\n=== Tests des stratégies de mise en cache ===")
    const cacheStrategyResults = await runCacheStrategyTests()
    results.testSuites.push({
      name: "Tests des stratégies de mise en cache",
      success: cacheStrategyResults.success,
      testsPassed: cacheStrategyResults.testsPassed,
      totalTests: cacheStrategyResults.totalTests,
      duration: cacheStrategyResults.duration,
      error: cacheStrategyResults.error,
      tests: cacheStrategyResults.tests,
      reportPath: path.relative(config.outputDir, cacheStrategyResults.reportPath || ""),
    })

    // Exécuter les tests de synchronisation
    console.log("\n=== Tests de synchronisation ===")
    const syncResults = await runSyncTests()
    results.testSuites.push({
      name: "Tests de synchronisation",
      success: syncResults.success,
      testsPassed: syncResults.testsPassed,
      totalTests: syncResults.totalTests,
      duration: syncResults.duration,
      error: syncResults.error,
      tests: syncResults.tests,
      reportPath: path.relative(config.outputDir, syncResults.reportPath || ""),
    })

    // Calculer les résultats globaux
    results.testsPassed = results.testSuites.reduce((total, suite) => total + suite.testsPassed, 0)
    results.totalTests = results.testSuites.reduce((total, suite) => total + suite.totalTests, 0)
    results.success = results.testSuites.every((suite) => suite.success)
    results.duration = ((Date.now() - startTime) / 1000).toFixed(2)

    // Générer le rapport global
    generateGlobalReport(results)
  } catch (error) {
    console.error("Erreur lors de l'exécution des tests:", error)
    results.error = error.message
  }

  return results
}

// Exécuter tous les tests si le script est appelé directement
if (require.main === module) {
  runAllTests()
    .then((results) => {
      console.log(`\nTous les tests terminés: ${results.testsPassed}/${results.totalTests} réussis`)
      process.exit(results.success ? 0 : 1)
    })
    .catch((error) => {
      console.error("Erreur lors de l'exécution des tests:", error)
      process.exit(1)
    })
}

module.exports = { runAllTests }
