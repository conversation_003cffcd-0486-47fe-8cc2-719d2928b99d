"use client"

import { useState, useEffect, type ReactNode } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface LazyContentProps {
  children: ReactNode
  height?: string | number
  width?: string | number
  threshold?: number // Intersection observer threshold
  className?: string
  fallback?: ReactNode
}

export function LazyContent({
  children,
  height = "200px",
  width = "100%",
  threshold = 0.1,
  className,
  fallback,
}: LazyContentProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasIntersectionObserver, setHasIntersectionObserver] = useState(false)

  useEffect(() => {
    // Check if IntersectionObserver is available
    if ("IntersectionObserver" in window) {
      setHasIntersectionObserver(true)
    } else {
      // If not available, just show the content
      setIsVisible(true)
    }
  }, [])

  useEffect(() => {
    if (!hasIntersectionObserver) return

    const element = document.getElementById("lazy-content-container")
    if (!element) {
      setIsVisible(true)
      return
    }

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold },
    )

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [hasIntersectionObserver, threshold])

  const defaultFallback = (
    <div style={{ height, width }} className={className}>
      <Skeleton className="w-full h-full" />
    </div>
  )

  return (
    <div id="lazy-content-container" className={className}>
      {isVisible ? children : fallback || defaultFallback}
    </div>
  )
}
