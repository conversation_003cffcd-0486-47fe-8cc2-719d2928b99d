"use client"

import { useState, useEffect } from "react"
import { syncService, type SyncOperation } from "@/lib/sync-service"

/**
 * Hook pour utiliser le service de synchronisation
 * @returns Opérations de synchronisation et fonctions pour les gérer
 */
export function useSync() {
  const [operations, setOperations] = useState<SyncOperation[]>([])
  const [isOnline, setIsOnline] = useState<boolean>(true)
  const [pendingCount, setPendingCount] = useState<number>(0)
  const [failedCount, setFailedCount] = useState<number>(0)
  const [completedCount, setCompletedCount] = useState<number>(0)

  // Mettre à jour l'état de connexion
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    // Initialiser l'état
    setIsOnline(navigator.onLine)

    // Ajouter les écouteurs
    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  // Écouter les changements d'opérations
  useEffect(() => {
    const handleOperationsChange = (newOperations: SyncOperation[]) => {
      setOperations(newOperations)

      // Mettre à jour les compteurs
      const pending = newOperations.filter((op) => op.status === "pending").length
      const failed = newOperations.filter((op) => op.status === "failed").length
      const completed = newOperations.filter((op) => op.status === "completed").length

      setPendingCount(pending)
      setFailedCount(failed)
      setCompletedCount(completed)
    }

    // Ajouter l'écouteur
    syncService.addListener(handleOperationsChange)

    // Charger les opérations initiales
    syncService.getAllOperations().then(handleOperationsChange)

    return () => {
      syncService.removeListener(handleOperationsChange)
    }
  }, [])

  // Synchroniser les opérations en attente
  const synchronize = async () => {
    await syncService.synchronize()
  }

  // Réessayer une opération échouée
  const retryOperation = async (operationId: string) => {
    await syncService.retryOperation(operationId)
  }

  // Supprimer une opération
  const removeOperation = async (operationId: string) => {
    await syncService.removeOperation(operationId)
  }

  // Supprimer toutes les opérations terminées
  const clearCompletedOperations = async () => {
    await syncService.clearCompletedOperations()
  }

  return {
    operations,
    isOnline,
    pendingCount,
    failedCount,
    completedCount,
    synchronize,
    retryOperation,
    removeOperation,
    clearCompletedOperations,
  }
}

export default useSync
