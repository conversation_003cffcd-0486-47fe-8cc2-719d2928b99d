"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Share2, X, Loader2 } from "lucide-react"

interface SingleImageViewerProps {
  imageUrl: string
  caption?: string
  altText?: string
}

export function SingleImageViewer({ imageUrl, caption, altText }: SingleImageViewerProps) {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  // Fonction pour obtenir l'URL proxifiée
  const getProxyUrl = (url: string) => {
    // Encoder l'URL pour éviter les problèmes avec les caractères spéciaux
    const encodedUrl = encodeURIComponent(url)
    return `/api/proxy-image?url=${encodedUrl}`
  }

  const handleDownload = async () => {
    try {
      setIsDownloading(true)

      // Utiliser l'URL d'origine pour le téléchargement direct
      const fileName = caption ? `${caption}.jpg` : "image.jpg"
      const link = document.createElement("a")
      link.href = imageUrl
      link.download = fileName
      link.target = "_blank"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error("Erreur lors du téléchargement de l'image:", error)
      window.open(imageUrl, "_blank")
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShare = async () => {
    try {
      setIsSharing(true)

      // Utiliser l'URL proxifiée pour contourner CORS
      const proxyUrl = getProxyUrl(imageUrl)

      // Récupérer l'image via le proxy
      const response = await fetch(proxyUrl)

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`)
      }

      const blob = await response.blob()
      const fileName = caption ? `${caption}.jpg` : "image.jpg"
      const file = new File([blob], fileName, { type: blob.type || "image/jpeg" })

      // Vérifier si le navigateur supporte le partage de fichiers
      if (navigator.share && navigator.canShare && navigator.canShare({ files: [file] })) {
        await navigator.share({
          files: [file],
          title: caption || "Image partagée",
          text: caption || "Regardez cette image",
        })
      } else {
        // Fallback pour les navigateurs qui ne supportent pas le partage de fichiers
        if (navigator.share) {
          // Partager au moins l'URL si le partage de fichiers n'est pas supporté
          await navigator.share({
            title: caption || "Image partagée",
            text: caption || "Regardez cette image",
            url: imageUrl,
          })
        } else {
          // Fallback pour les navigateurs sans API Web Share
          await navigator.clipboard.writeText(imageUrl)
          alert(
            "L'URL de l'image a été copiée dans le presse-papier car votre navigateur ne supporte pas le partage direct.",
          )
        }
      }
    } catch (error) {
      console.error("Erreur lors du partage de l'image:", error)

      // Tenter de partager l'URL comme solution de repli
      try {
        if (navigator.share) {
          await navigator.share({
            title: caption || "Image partagée",
            text: caption || "Regardez cette image",
            url: imageUrl,
          })
        } else {
          await navigator.clipboard.writeText(imageUrl)
          alert(
            "L'URL de l'image a été copiée dans le presse-papier car votre navigateur ne supporte pas le partage direct.",
          )
        }
      } catch (shareError) {
        console.error("Erreur lors du partage de l'URL:", shareError)
        alert("Impossible de partager l'image. Veuillez essayer de la télécharger à la place.")
      }
    } finally {
      setIsSharing(false)
    }
  }

  return (
    <div className="flex flex-col items-center">
      <div className="flex justify-end w-full mb-2 -mt-6">
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            className="opacity-80 hover:opacity-100"
            onClick={handleDownload}
            disabled={isDownloading}
          >
            {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="opacity-80 hover:opacity-100"
            onClick={handleShare}
            disabled={isSharing}
          >
            {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogTrigger asChild>
          <div className="relative w-full cursor-pointer overflow-hidden" style={{ maxHeight: "calc(100vh - 200px)" }}>
            <img
              src={imageUrl || "/placeholder.svg"}
              alt={altText || caption || "Image"}
              className="w-full object-contain transition-transform duration-300"
              style={{ maxHeight: "calc(100vh - 200px)" }}
            />
          </div>
        </DialogTrigger>
        <DialogContent className="max-w-screen-lg w-[90vw] h-[90vh] p-0 border-none bg-black/90">
          <div className="relative w-full h-full flex items-center justify-center">
            <img
              src={imageUrl || "/placeholder.svg"}
              alt={altText || caption || "Image"}
              className="max-w-full max-h-full object-contain"
            />
            <Button
              size="icon"
              variant="ghost"
              className="absolute top-4 right-4 text-white"
              onClick={() => setIsFullscreen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
            <div className="absolute top-4 left-4 flex gap-2">
              <Button size="sm" variant="secondary" onClick={handleDownload} disabled={isDownloading}>
                {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
              <Button size="sm" variant="secondary" onClick={handleShare} disabled={isSharing}>
                {isSharing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Share2 className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {caption && <div className="mt-2 text-center text-sm text-muted-foreground">{caption}</div>}
    </div>
  )
}
