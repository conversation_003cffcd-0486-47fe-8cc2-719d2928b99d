"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { IOSInstallGuide } from "./ios-install-guide"
import { WindowsInstallGuide } from "./windows-install-guide"

export function PWAInstallBanner() {
  const { isInstallable, isInstalled, promptInstall, isIOS, isWindows, showIOSGuide, closeIOSGuide } = usePWAInstall()
  const [showWindowsGuide, setShowWindowsGuide] = useState(false)
  const [browserType, setBrowserType] = useState<"chrome" | "edge" | "firefox" | "other">("chrome")
  const [showBanner, setShowBanner] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Vérifier si la bannière a déjà été fermée dans cette session
    const isDismissed = sessionStorage.getItem("pwa-banner-dismissed") === "true"

    if (isDismissed) {
      setDismissed(true)
      return
    }

    // Afficher la bannière après un délai si l'app est installable et non installée
    const timer = setTimeout(() => {
      if (isInstallable && !isInstalled) {
        console.log("Affichage de la bannière d'installation")
        setShowBanner(true)
      } else {
        console.log("Conditions non remplies pour afficher la bannière:", {
          isInstallable,
          isInstalled,
        })
      }
    }, 3000)

    return () => clearTimeout(timer)
  }, [isInstallable, isInstalled])

  const handleDismiss = () => {
    setShowBanner(false)
    setDismissed(true)
    sessionStorage.setItem("pwa-banner-dismissed", "true")
  }

  const handleInstall = () => {
    if (isWindows) {
      // Détecter le navigateur
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = /chrome/.test(userAgent) && !/edge|edg/.test(userAgent)
      const isEdge = /edge|edg/.test(userAgent)
      const isFirefox = /firefox/.test(userAgent)

      if (isChrome) setBrowserType("chrome")
      else if (isEdge) setBrowserType("edge")
      else if (isFirefox) setBrowserType("firefox")
      else setBrowserType("other")

      setShowWindowsGuide(true)
    }

    promptInstall()
    setShowBanner(false)
  }

  const closeWindowsGuide = () => {
    setShowWindowsGuide(false)
  }

  if (!showBanner || dismissed || isInstalled) return null

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 animate-slide-up bg-white p-4 shadow-lg dark:bg-gray-800">
        <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <div className="mr-4 h-12 w-12 overflow-hidden rounded-lg">
              <img src="/android-chrome-192x192.png" alt="ACR Direct" className="h-full w-full object-cover" />
            </div>
            <div>
              <h3 className="font-bold">Installer ACR Direct</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Accédez rapidement à l'application depuis votre écran d'accueil
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <button
              onClick={handleInstall}
              className="mr-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
            >
              Installer
            </button>
            <button
              onClick={handleDismiss}
              className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
            >
              <X size={20} />
            </button>
          </div>
        </div>
      </div>

      {showIOSGuide && <IOSInstallGuide onClose={closeIOSGuide} />}
      {showWindowsGuide && <WindowsInstallGuide onClose={closeWindowsGuide} browserType={browserType} />}
    </>
  )
}
