import { ImageIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface PlaceholderImageProps {
  className?: string
  iconClassName?: string
}

export function PlaceholderImage({ className, iconClassName }: PlaceholderImageProps) {
  return (
    <div
      className={cn(
        "w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700",
        className,
      )}
    >
      <ImageIcon className={cn("h-8 w-8 text-gray-400 dark:text-gray-500 opacity-70", iconClassName)} />
    </div>
  )
}
