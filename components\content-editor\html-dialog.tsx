"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

interface HtmlDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (htmlContent: string) => void
}

export function HtmlDialog({ isOpen, onClose, onConfirm }: HtmlDialogProps) {
  const [htmlContent, setHtmlContent] = useState("")
  const [activeTab, setActiveTab] = useState<string>("edit")

  const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Empêcher la propagation de l'événement pour éviter la soumission du formulaire parent
    e.preventD<PERSON>ault()
    e.stopPropagation()

    onConfirm(htmlContent)
  }

  const resetForm = () => {
    setHtmlContent("")
    setActiveTab("edit")
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
          resetForm()
        }
      }}
    >
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Insérer du HTML brut</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Tabs defaultValue="edit" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="edit">Éditer</TabsTrigger>
              <TabsTrigger value="preview">Aperçu</TabsTrigger>
            </TabsList>
            <TabsContent value="edit" className="py-4">
              <Textarea
                value={htmlContent}
                onChange={(e) => setHtmlContent(e.target.value)}
                className="font-mono text-sm min-h-[200px]"
                placeholder="<div>Insérez votre HTML ici...</div>"
              />
            </TabsContent>
            <TabsContent value="preview" className="py-4">
              <div className="border rounded-md p-4 min-h-[200px]">
                {htmlContent ? (
                  <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
                ) : (
                  <p className="text-muted-foreground text-center">Aucun contenu HTML à prévisualiser</p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Annuler
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={!htmlContent.trim()}>
            Insérer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
