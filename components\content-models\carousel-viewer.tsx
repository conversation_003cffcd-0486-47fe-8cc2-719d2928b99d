"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface CarouselSlide {
  url: string
  title?: string
  description?: string
}

interface CarouselViewerProps {
  slides: CarouselSlide[]
  autoplay?: boolean
  interval?: number
}

export function CarouselViewer({ slides, autoplay = false, interval = 5 }: CarouselViewerProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1))
  }

  const goToNext = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  // Handle autoplay
  useEffect(() => {
    if (autoplay && !isPaused && slides.length > 1) {
      intervalRef.current = setInterval(() => {
        goToNext()
      }, interval * 1000)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoplay, isPaused, currentSlide, interval, slides.length])

  // Pause on hover
  const handleMouseEnter = () => {
    setIsPaused(true)
  }

  const handleMouseLeave = () => {
    setIsPaused(false)
  }

  if (slides.length === 0) {
    return <div className="text-center p-8 text-muted-foreground">Aucune diapositive à afficher</div>
  }

  return (
    <div className="relative w-full" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <div className="overflow-hidden rounded-lg">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {slides.map((slide, index) => (
            <div key={index} className="w-full flex-shrink-0">
              <div className="relative aspect-[16/9]">
                <img
                  src={slide.url || "/placeholder.svg"}
                  alt={slide.title || `Diapositive ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                {(slide.title || slide.description) && (
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-4">
                    {slide.title && <h3 className="text-lg font-medium">{slide.title}</h3>}
                    {slide.description && <p className="text-sm opacity-90">{slide.description}</p>}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {slides.length > 1 && (
        <>
          <Button
            size="icon"
            variant="secondary"
            className="absolute left-2 top-1/2 -translate-y-1/2 opacity-70 hover:opacity-100"
            onClick={goToPrevious}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            className="absolute right-2 top-1/2 -translate-y-1/2 opacity-70 hover:opacity-100"
            onClick={goToNext}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentSlide ? "bg-white" : "bg-white/50"
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Aller à la diapositive ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
}
