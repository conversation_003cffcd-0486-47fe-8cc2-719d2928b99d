"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { WifiOff, RefreshCw, Home, Loader2, Database } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { enableFirestoreNetwork } from "@/lib/firebase"
import { getPendingOperations, syncPendingOperations } from "@/lib/offline-sync"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function OfflinePage() {
  const router = useRouter()
  const [isReconnecting, setIsReconnecting] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [pendingOperations, setPendingOperations] = useState(0)
  const [lastSyncAttempt, setLastSyncAttempt] = useState<Date | null>(null)

  useEffect(() => {
    // Vérifier les opérations en attente
    const checkPendingOperations = async () => {
      try {
        const operations = await getPendingOperations()
        setPendingOperations(operations.length)
      } catch (error) {
        console.error("Erreur lors de la vérification des opérations en attente:", error)
      }
    }

    checkPendingOperations()

    // Vérifier si nous sommes de nouveau en ligne
    const handleOnline = () => {
      // Rediriger vers le tableau de bord si nous sommes de nouveau en ligne
      if (navigator.onLine) {
        router.push("/dashboard")
      }
    }

    window.addEventListener("online", handleOnline)

    return () => {
      window.removeEventListener("online", handleOnline)
    }
  }, [router])

  const handleReconnect = async () => {
    setIsReconnecting(true)

    try {
      // Try to enable the Firestore network
      const success = await enableFirestoreNetwork()

      if (success) {
        // If successful, redirect to dashboard
        router.push("/dashboard")
      } else {
        // If not successful, reload the page
        window.location.reload()
      }
    } catch (error) {
      console.error("Error reconnecting:", error)
      // If there's an error, reload the page
      window.location.reload()
    } finally {
      setIsReconnecting(false)
    }
  }

  const handleSync = async () => {
    if (isSyncing) return

    setIsSyncing(true)
    setLastSyncAttempt(new Date())

    try {
      await syncPendingOperations()
      const operations = await getPendingOperations()
      setPendingOperations(operations.length)
    } catch (error) {
      console.error("Erreur lors de la synchronisation:", error)
    } finally {
      setIsSyncing(false)
    }
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="w-full max-w-md space-y-8 z-10">
        <Card className="border border-border shadow-lg">
          <CardHeader>
            <div className="mx-auto bg-red-100 dark:bg-red-900/30 w-16 h-16 rounded-full flex items-center justify-center mb-4">
              <WifiOff className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-2xl font-bold text-center">Vous êtes hors ligne</CardTitle>
            <CardDescription className="text-center">
              Impossible de se connecter au serveur. Certaines fonctionnalités de l'application peuvent être limitées en
              mode hors ligne.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {pendingOperations > 0 && (
              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <h3 className="font-medium text-amber-800 dark:text-amber-300">Données en attente</h3>
                </div>
                <p className="text-sm text-amber-700 dark:text-amber-400">
                  {pendingOperations} modification{pendingOperations > 1 ? "s" : ""} en attente de synchronisation.
                </p>
                {lastSyncAttempt && (
                  <p className="text-xs text-amber-600 dark:text-amber-500 mt-1">
                    Dernière tentative de synchronisation: {lastSyncAttempt.toLocaleTimeString()}
                  </p>
                )}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={handleReconnect} disabled={isReconnecting} className="flex-1">
                {isReconnecting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Reconnexion...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Réessayer
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={() => router.push("/dashboard")} className="flex-1">
                <Home className="mr-2 h-4 w-4" />
                Retour à l'accueil
              </Button>
            </div>

            {pendingOperations > 0 && (
              <Button variant="secondary" onClick={handleSync} disabled={isSyncing} className="w-full mt-2">
                {isSyncing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Synchronisation...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Synchroniser les données ({pendingOperations})
                  </>
                )}
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </main>
  )
}
