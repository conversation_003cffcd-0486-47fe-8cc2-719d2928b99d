import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"

interface UserDeletionWarningProps {
  dataCount: number
}

export function UserDeletionWarning({ dataCount }: UserDeletionWarningProps) {
  if (dataCount <= 0) return null

  return (
    <Alert variant="warning" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Attention</AlertTitle>
      <AlertDescription>
        Cet utilisateur possède {dataCount} éléments de données associés qui seront également supprimés.
        {dataCount > 50 && (
          <span className="block mt-1 font-semibold">
            La suppression d'un grand volume de données peut prendre un moment.
          </span>
        )}
      </AlertDescription>
    </Alert>
  )
}
