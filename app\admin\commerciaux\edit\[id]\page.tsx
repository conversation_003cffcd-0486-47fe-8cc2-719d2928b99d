"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { X, Upload, Info } from "lucide-react"
import { getCommercialContact, saveCommercialContact, uploadCommercialPhoto } from "@/lib/commercial-utils"
import { DEPARTMENTS } from "@/lib/commercial-types"
import type { CommercialContact } from "@/lib/commercial-types"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, AlertDes<PERSON> } from "@/components/ui/alert"

// Import getUserById from user-service
import { getUsersByRole, getUserById } from "@/lib/user-service"

// Update the component with the new functionality
export default function EditCommercialPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [contact, setContact] = useState<CommercialContact | null>(null)
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [departments, setDepartments] = useState<string[]>([])
  const [photoFile, setPhotoFile] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [photoURL, setPhotoURL] = useState<string>("")
  const [commercialUsers, setCommercialUsers] = useState<any[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [isLoadingUserData, setIsLoadingUserData] = useState(false)
  const [userDataRefreshed, setUserDataRefreshed] = useState(false)

  // Load commercial contact and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Load commercial contact
        const data = await getCommercialContact(params.id)
        if (data) {
          setContact(data)
          setName(data.name)
          setEmail(data.email)
          setPhone(data.phone)
          setDepartments(data.departments)
          setPhotoURL(data.photoURL)
          setSelectedUserId(data.userId || "")
          if (data.photoURL) {
            setPhotoPreview(data.photoURL)
          }
        } else {
          toast({
            title: "Erreur",
            description: "Contact commercial introuvable.",
            variant: "destructive",
          })
          router.push("/admin/commerciaux")
        }

        // Load commercial users
        const users = await getUsersByRole("commercial")
        setCommercialUsers(users)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Erreur",
          description: "Impossible de récupérer les informations nécessaires.",
          variant: "destructive",
        })
        router.push("/admin/commerciaux")
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [params.id, router, toast])

  // Handle user selection change
  const handleUserChange = async (userId: string) => {
    if (userId === "none") {
      setSelectedUserId("")
      return
    }

    setSelectedUserId(userId)
    setIsLoadingUserData(true)

    try {
      const userData = await getUserById(userId)
      if (userData) {
        // Update form with user data
        setName(userData.displayName || "")
        setEmail(userData.email || "")
        setPhone(userData.phoneNumber || userData.phone || "")

        // Update photo if user has one and no custom photo was already set
        if (userData.photoURL && !photoURL) {
          setPhotoURL(userData.photoURL)
          setPhotoPreview(userData.photoURL)
        }

        setUserDataRefreshed(true)
      }
    } catch (error) {
      console.error("Error loading user data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'utilisateur.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingUserData(false)
    }
  }

  // Refresh user data
  const refreshUserData = async () => {
    if (!selectedUserId) return

    setIsLoadingUserData(true)
    try {
      const userData = await getUserById(selectedUserId)
      if (userData) {
        setName(userData.displayName || "")
        setEmail(userData.email || "")
        setPhone(userData.phoneNumber || userData.phone || "")

        // Only update photo if user has one and no custom photo was uploaded
        if (userData.photoURL && !photoFile) {
          setPhotoURL(userData.photoURL)
          setPhotoPreview(userData.photoURL)
        }

        setUserDataRefreshed(true)

        toast({
          title: "Succès",
          description: "Les informations de l'utilisateur ont été actualisées.",
        })
      }
      console.log("User data refreshed:", userData)
    } catch (error) {
      console.error("Error refreshing user data:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'actualiser les données de l'utilisateur.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingUserData(false)
    }
  }

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setPhotoFile(file)

      // Create a preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setPhotoPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleAddDepartment = (departmentCode: string) => {
    if (!departments.includes(departmentCode)) {
      setDepartments([...departments, departmentCode])
    }
  }

  const handleRemoveDepartment = (departmentCode: string) => {
    setDepartments(departments.filter((d) => d !== departmentCode))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!name || !email || departments.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)

    try {
      // Upload new photo if provided
      let updatedPhotoURL = photoURL
      if (photoFile) {
        updatedPhotoURL = await uploadCommercialPhoto(photoFile, params.id)
      }

      // Save the contact
      await saveCommercialContact({
        id: params.id,
        name,
        email,
        phone,
        departments,
        photoURL: updatedPhotoURL,
        userId: selectedUserId || undefined,
      })

      toast({
        title: "Succès",
        description: "Le contact commercial a été mis à jour avec succès.",
      })

      router.push("/admin/commerciaux")
    } catch (error) {
      console.error("Error updating commercial contact:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour du contact commercial.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Modifier un contact commercial</h1>
        <Button variant="outline" onClick={() => router.push("/admin/commerciaux")}>
          Retour à la liste
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informations du contact</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              {/* User selection field */}
              <div className="space-y-2">
                <Label htmlFor="userId">Utilisateur associé</Label>
                <div className="flex gap-2">
                  <Select
                    onValueChange={handleUserChange}
                    value={selectedUserId || "none"}
                    disabled={isLoadingUserData}
                  >
                    <SelectTrigger id="userId" className="flex-1">
                      <SelectValue placeholder="Sélectionner un utilisateur" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Aucun utilisateur</SelectItem>
                      {commercialUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.displayName || user.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {selectedUserId && (
                    <Button type="button" variant="outline" onClick={refreshUserData} disabled={isLoadingUserData}>
                      {isLoadingUserData ? (
                        <div className="animate-spin h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                      ) : (
                        "Actualiser"
                      )}
                    </Button>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Associer ce profil à un utilisateur lui permettra de gérer ses informations.
                </p>
              </div>

              {userDataRefreshed && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Les informations ont été mises à jour à partir du profil utilisateur.
                  </AlertDescription>
                </Alert>
              )}

              {isLoadingUserData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nom complet *</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Jean Dupont"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Téléphone</Label>
                      <Input
                        id="phone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder="06 12 34 56 78"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="department">Ajouter un département *</Label>
                      <Select onValueChange={handleAddDepartment}>
                        <SelectTrigger id="department">
                          <SelectValue placeholder="Sélectionner un département" />
                        </SelectTrigger>
                        <SelectContent>
                          {DEPARTMENTS.map((dept) => (
                            <SelectItem key={dept.code} value={dept.code}>
                              {dept.code} - {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Départements sélectionnés *</Label>
                    {departments.length === 0 ? (
                      <p className="text-sm text-muted-foreground">Aucun département sélectionné</p>
                    ) : (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {departments.map((dept) => {
                          const deptName = DEPARTMENTS.find((d) => d.code === dept)?.name || dept
                          return (
                            <Badge key={dept} variant="secondary" className="flex items-center gap-1">
                              {dept} - {deptName}
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                                onClick={() => handleRemoveDepartment(dept)}
                              >
                                <X className="h-3 w-3" />
                                <span className="sr-only">Supprimer</span>
                              </Button>
                            </Badge>
                          )
                        })}
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="photo">Photo de profil</Label>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-16 w-16">
                        {photoPreview ? (
                          <AvatarImage src={photoPreview || "/placeholder.svg"} alt="Preview" />
                        ) : (
                          <AvatarFallback>?</AvatarFallback>
                        )}
                      </Avatar>
                      <div className="flex-1">
                        <Input
                          id="photo"
                          type="file"
                          accept="image/*"
                          onChange={handlePhotoChange}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById("photo")?.click()}
                          className="w-full"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          {photoFile ? "Changer la photo" : photoURL ? "Modifier la photo" : "Télécharger une photo"}
                        </Button>
                        {photoFile && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {photoFile.name} ({Math.round(photoFile.size / 1024)} Ko)
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/commerciaux")}
                disabled={saving}
              >
                Annuler
              </Button>
              <Button type="submit" disabled={saving || isLoadingUserData}>
                {saving ? (
                  <>
                    <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                    Enregistrement...
                  </>
                ) : (
                  "Enregistrer"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
