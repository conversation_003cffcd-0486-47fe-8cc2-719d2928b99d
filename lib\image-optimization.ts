import { storage } from "./firebase"
import { ref, getDownloadURL } from "firebase/storage"
import { cacheImages } from "@/app/sw-register"

type ImageSize = "thumbnail" | "small" | "medium" | "large" | "original"

interface ImageDimensions {
  width: number
  height?: number
}

const IMAGE_SIZES: Record<ImageSize, ImageDimensions> = {
  thumbnail: { width: 100 },
  small: { width: 300 },
  medium: { width: 600 },
  large: { width: 1200 },
  original: { width: 0 }, // Original size
}

// Ajoutez un cache en mémoire pour les URLs d'images
const urlCache = new Map<string, string>()

// Ajout d'un cache pour les chemins d'images qui ont échoué
const failedPaths = new Set<string>()

/**
 * Get the optimized image URL for a given storage path and size
 * @param path Firebase storage path
 * @param size Desired image size
 * @returns Promise with the optimized image URL
 */
export async function getOptimizedImageUrl(path: string, size: ImageSize = "medium"): Promise<string> {
  // Si le chemin est vide ou null, retourner immédiatement une image par défaut
  if (!path) {
    return "/placeholder.png"
  }

  // Si ce chemin a déjà échoué précédemment, retourner immédiatement une image par défaut
  if (failedPaths.has(path)) {
    return "/placeholder.png"
  }

  // Créer une clé de cache unique pour cette combinaison path/size
  const cacheKey = `${path}_${size}`

  // Vérifier si l'URL est déjà en cache
  if (urlCache.has(cacheKey)) {
    return urlCache.get(cacheKey)!
  }

  try {
    // Pour la taille originale, retourner simplement l'URL directe
    if (size === "original") {
      const storageRef = ref(storage(), path)
      const url = await getDownloadURL(storageRef)
      urlCache.set(cacheKey, url)
      return url
    }

    // Pour les autres tailles, utiliser l'extension Firestore pour le redimensionnement d'image
    const dimensions = IMAGE_SIZES[size]
    const width = dimensions.width
    const height = dimensions.height || 0

    // Construire le chemin pour l'image redimensionnée
    const resizedPath = path.replace(/^([^.]+)(\..+)$/, `$1_${width}x${height || "auto"}$2`)
    const resizedRef = ref(storage(), `resized/${resizedPath}`)

    try {
      // Essayer d'obtenir d'abord l'image redimensionnée
      const url = await getDownloadURL(resizedRef)
      urlCache.set(cacheKey, url)
      return url
    } catch (error) {
      // Si l'image redimensionnée n'existe pas encore, retourner l'original et déclencher le redimensionnement
      const originalRef = ref(storage(), path)
      try {
        const originalUrl = await getDownloadURL(originalRef)
        urlCache.set(cacheKey, originalUrl)

        // Déclencher le redimensionnement en accédant à une URL spéciale
        fetch(`/api/resize-image?path=${encodeURIComponent(path)}&width=${width}&height=${height || "auto"}`).catch(
          (err) => console.error("Failed to trigger image resize:", err),
        )

        return originalUrl
      } catch (downloadError) {
        // Si l'image originale n'existe pas non plus, marquer ce chemin comme ayant échoué
        console.error(`Image not found in storage: ${path}`, downloadError)
        failedPaths.add(path)
        return "/placeholder.png"
      }
    }
  } catch (error) {
    console.error("Error getting optimized image:", error)
    // Marquer ce chemin comme ayant échoué pour éviter de réessayer
    failedPaths.add(path)
    return "/placeholder.png" // Fallback to placeholder
  }
}

/**
 * Précharge les images importantes pour améliorer les performances perçues
 * @param paths Tableau de chemins d'images à précharger
 * @param size Taille à précharger
 */
export function preloadImages(paths: string[], size: ImageSize = "small"): void {
  // Filtrer les chemins vides ou null
  const validPaths = paths.filter((path) => !!path)
  if (validPaths.length === 0) return

  const urls: string[] = []

  validPaths.forEach(async (path) => {
    try {
      // Ignorer les chemins qui ont déjà échoué
      if (failedPaths.has(path)) return

      const url = await getOptimizedImageUrl(path, size)
      if (url !== "/placeholder.png") {
        urls.push(url)

        const img = new Image()
        img.src = url
        // Ajouter l'URL au cache du navigateur en forçant le chargement
        img.onload = () => {
          // L'image est maintenant en cache dans le navigateur
        }
      }
    } catch (error) {
      // Échouer silencieusement sur les erreurs de préchargement
    }
  })

  // Ajouter les URLs au cache du Service Worker
  if (urls.length > 0 && typeof window !== "undefined") {
    // Attendre un peu pour s'assurer que le Service Worker est prêt
    setTimeout(() => {
      cacheImages(urls)
    }, 1000)
  }
}

/**
 * Vérifie si une image est déjà en cache dans le Service Worker
 * @param url URL de l'image à vérifier
 * @returns Promise<boolean> true si l'image est en cache
 */
export async function isImageCached(url: string): Promise<boolean> {
  if (typeof window === "undefined" || !("caches" in window)) {
    return false
  }

  try {
    const cache = await caches.open("acr-direct-images-v1")
    const response = await cache.match(url)
    return !!response
  } catch (error) {
    console.error("Error checking cache:", error)
    return false
  }
}
