"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ImagePlus, X, Loader2 } from "lucide-react"

interface SingleImageModelProps {
  imageUrl: string | null
  caption: string
  altText: string
  onImageChange: (file: File | null) => void
  onCaptionChange: (caption: string) => void
  onAltTextChange: (altText: string) => void
  isUploading?: boolean
}

export function SingleImageModel({
  imageUrl,
  caption,
  altText,
  onImageChange,
  onCaptionChange,
  onAltTextChange,
  isUploading = false,
}: SingleImageModelProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(imageUrl)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      onImageChange(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setPreviewUrl(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onImageChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Image</Label>
        <div className="flex flex-col items-center gap-4 p-4 border-2 border-dashed rounded-lg">
          {previewUrl ? (
            <div className="relative w-full">
              <div className="relative aspect-video w-full overflow-hidden rounded-lg">
                <img src={previewUrl || "/placeholder.svg"} alt="Aperçu" className="object-cover w-full h-full" />
              </div>
              <Button
                type="button"
                size="icon"
                variant="destructive"
                className="absolute top-2 right-2 h-8 w-8 rounded-full"
                onClick={handleRemoveImage}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div
              className="flex flex-col items-center justify-center w-full h-40 bg-muted rounded-lg cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              {isUploading ? (
                <Loader2 className="h-10 w-10 text-muted-foreground animate-spin" />
              ) : (
                <>
                  <ImagePlus className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">Cliquez pour ajouter une image</p>
                </>
              )}
            </div>
          )}
          <input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileChange} className="hidden" />
          {previewUrl ? (
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Téléchargement...
                </>
              ) : (
                <>
                  <ImagePlus className="mr-2 h-4 w-4" />
                  Changer l'image
                </>
              )}
            </Button>
          ) : null}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="alt-text">Texte alternatif (pour l'accessibilité)</Label>
        <Input
          id="alt-text"
          value={altText}
          onChange={(e) => onAltTextChange(e.target.value)}
          placeholder="Description de l'image pour les lecteurs d'écran"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="caption">Légende</Label>
        <Textarea
          id="caption"
          value={caption}
          onChange={(e) => onCaptionChange(e.target.value)}
          placeholder="Légende affichée sous l'image"
          rows={2}
        />
      </div>
    </div>
  )
}
