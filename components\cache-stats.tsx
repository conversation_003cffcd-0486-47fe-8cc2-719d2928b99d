"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Loader2, <PERSON>freshC<PERSON>, Pie<PERSON>hart, Clock, Database } from "lucide-react"
import { cacheService } from "@/lib/cache-service"
import { cacheLifecycle } from "@/lib/cache-lifecycle"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

export function CacheStats() {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [cacheStats, setCacheStats] = useState<any>(null)
  const [lifecycleStats, setLifecycleStats] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<string>("overview")

  // Charger les statistiques du cache
  const loadStats = async () => {
    setIsLoading(true)

    try {
      // Récupérer les statistiques du cache
      const stats = await cacheService.getStats()
      setCacheStats(stats)

      // Récupérer les statistiques du cycle de vie
      const lifecycle = await cacheLifecycle.getStats()
      setLifecycleStats(lifecycle)
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Réinitialiser les statistiques du cycle de vie
  const resetLifecycleStats = async () => {
    try {
      await cacheLifecycle.resetStats()
      await loadStats()
    } catch (error) {
      console.error("Erreur lors de la réinitialisation des statistiques:", error)
    }
  }

  // Formater la taille en KB, MB, etc.
  const formatSize = (sizeInKB: number): string => {
    if (sizeInKB < 1024) {
      return `${sizeInKB.toFixed(1)} KB`
    } else if (sizeInKB < 1024 * 1024) {
      return `${(sizeInKB / 1024).toFixed(1)} MB`
    } else {
      return `${(sizeInKB / (1024 * 1024)).toFixed(1)} GB`
    }
  }

  // Charger les statistiques au chargement du composant
  useEffect(() => {
    loadStats()
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Statistiques du cache</span>
          <Button variant="outline" size="sm" onClick={loadStats} disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
            <span className="ml-2">Actualiser</span>
          </Button>
        </CardTitle>
        <CardDescription>Statistiques détaillées sur l'utilisation et les performances du cache</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="stores">Stores</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 mt-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-muted/20 p-4 rounded-lg flex items-center">
                    <Database className="h-8 w-8 mr-3 text-blue-500" />
                    <div>
                      <h3 className="font-medium">Taille totale</h3>
                      <p className="text-2xl font-bold">
                        {cacheStats
                          ? formatSize(
                              Object.values(cacheStats).reduce(
                                (acc: number, curr: any) => acc + (curr.size || 0) / 1024,
                                0,
                              ),
                            )
                          : "N/A"}
                      </p>
                    </div>
                  </div>

                  <div className="bg-muted/20 p-4 rounded-lg flex items-center">
                    <PieChart className="h-8 w-8 mr-3 text-green-500" />
                    <div>
                      <h3 className="font-medium">Taux de succès</h3>
                      <p className="text-2xl font-bold">
                        {lifecycleStats ? `${Math.round(lifecycleStats.hitRatio * 100)}%` : "N/A"}
                      </p>
                    </div>
                  </div>

                  <div className="bg-muted/20 p-4 rounded-lg flex items-center">
                    <Clock className="h-8 w-8 mr-3 text-amber-500" />
                    <div>
                      <h3 className="font-medium">Dernière mise à jour</h3>
                      <p className="text-lg font-medium">
                        {lifecycleStats
                          ? formatDistanceToNow(lifecycleStats.lastFullUpdate, { addSuffix: true, locale: fr })
                          : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Dernières mises à jour par type</h3>
                  {lifecycleStats && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {Object.entries(lifecycleStats.lastUpdates).map(([type, date]: [string, Date]) => (
                        <div key={type} className="flex justify-between">
                          <span className="font-medium">{type}</span>
                          <span className="text-sm text-muted-foreground">
                            {date.getTime() > 0 ? formatDistanceToNow(date, { addSuffix: true, locale: fr }) : "Jamais"}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4 mt-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium mb-2">Hits</h3>
                    <p className="text-2xl font-bold text-green-600">
                      {lifecycleStats ? lifecycleStats.hits.toLocaleString() : "0"}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium mb-2">Misses</h3>
                    <p className="text-2xl font-bold text-red-600">
                      {lifecycleStats ? lifecycleStats.misses.toLocaleString() : "0"}
                    </p>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium mb-2">Taux de succès</h3>
                    <p className="text-2xl font-bold">
                      {lifecycleStats ? `${Math.round(lifecycleStats.hitRatio * 100)}%` : "0%"}
                    </p>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">Statistiques depuis</h3>
                    <Button variant="outline" size="sm" onClick={resetLifecycleStats}>
                      <RefreshCw className="mr-2 h-3 w-3" />
                      Réinitialiser
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {lifecycleStats
                      ? formatDistanceToNow(lifecycleStats.lastReset, { addSuffix: false, locale: fr })
                      : "N/A"}
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="stores" className="space-y-4 mt-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="text-left p-2 pl-4">Store</th>
                        <th className="text-right p-2">Éléments</th>
                        <th className="text-right p-2">Taille</th>
                        <th className="text-right p-2 pr-4">Dernière mise à jour</th>
                      </tr>
                    </thead>
                    <tbody>
                      {cacheStats &&
                        Object.entries(cacheStats).map(([key, value]: [string, any]) => (
                          <tr key={key} className="border-b last:border-0">
                            <td className="p-2 pl-4 font-medium">{key}</td>
                            <td className="text-right p-2">{value.count}</td>
                            <td className="text-right p-2">{formatSize(value.size / 1024)}</td>
                            <td className="text-right p-2 pr-4 text-sm text-muted-foreground">
                              {lifecycleStats &&
                              lifecycleStats.lastUpdates[key] &&
                              lifecycleStats.lastUpdates[key].getTime() > 0
                                ? formatDistanceToNow(lifecycleStats.lastUpdates[key], { addSuffix: true, locale: fr })
                                : "Jamais"}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Les statistiques sont mises à jour en temps réel lors de l'utilisation du cache
        </p>
      </CardFooter>
    </Card>
  )
}

export default CacheStats
