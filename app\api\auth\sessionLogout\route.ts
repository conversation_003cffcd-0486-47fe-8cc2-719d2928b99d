import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getUserSession, initializeFirebaseAdmin } from "@/lib/server-auth"

export async function POST(request: NextRequest) {
  try {
    // Supprimer le cookie de session immédiatement
    cookies().delete("__session")

    // Tenter de révoquer les tokens, mais ne pas bloquer la déconnexion si cela échoue
    try {
      // Récupérer la session utilisateur
      const { user } = await getUserSession()

      // Si nous avons un utilisateur, révoquer tous ses tokens
      if (user && user.uid) {
        const { auth } = initializeFirebaseAdmin()
        await auth.revokeRefreshTokens(user.uid)
        console.log(`Tokens révoqués pour l'utilisateur: ${user.uid}`)
      }
    } catch (revokeError) {
      console.error("Erreur lors de la révocation des tokens:", revokeError)
      // Continuer malgré l'erreur car le cookie a déjà été supprimé
    }

    return NextResponse.json({ status: "success", message: "Déconnexion réussie" })
  } catch (error) {
    console.error("Erreur lors de la déconnexion:", error)
    // Même en cas d'erreur, nous retournons un succès car le cookie a été supprimé
    return NextResponse.json({ status: "partial_success", message: "Cookie de session supprimé" })
  }
}
