import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface DisplaySettings {
  defaultShowFrameNews: boolean
  defaultShowFramePages: boolean
  showPublicationDates: boolean
  defaultShowPublicationDate: boolean // Add this new field
}

// Add a cache for the display settings to avoid repeated Firestore calls
// Add this at the top of the file, after the imports:
let cachedSettings: DisplaySettings | null = null
let cacheTimestamp = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

const defaultSettings: DisplaySettings = {
  defaultShowFrameNews: true,
  defaultShowFramePages: true,
  showPublicationDates: true,
  defaultShowPublicationDate: false, // Default to false as requested
}

// Then update the getDisplaySettings function:
export async function getDisplaySettings(): Promise<DisplaySettings> {
  // Check if we have valid cached settings
  const now = Date.now()
  if (cachedSettings && now - cacheTimestamp < CACHE_DURATION) {
    return cachedSettings
  }

  try {
    const displaySettingsDoc = await getDoc(doc(db(), "settings", "display"))

    if (displaySettingsDoc.exists()) {
      const data = displaySettingsDoc.data()
      const settings = {
        defaultShowFrameNews: data.defaultShowFrameNews !== false,
        defaultShowFramePages: data.defaultShowFramePages !== false,
        showPublicationDates: data.showPublicationDates !== false,
        defaultShowPublicationDate: data.defaultShowPublicationDate === true, // Default to false if not set
      }

      // Update the cache
      cachedSettings = settings
      cacheTimestamp = now

      return settings
    }

    // If no settings exist, cache the defaults
    cachedSettings = defaultSettings
    cacheTimestamp = now
    return defaultSettings
  } catch (error) {
    console.error("Error fetching display settings:", error)

    // If we have cached settings, return them even if they're expired
    if (cachedSettings) {
      return cachedSettings
    }

    return defaultSettings
  }
}
