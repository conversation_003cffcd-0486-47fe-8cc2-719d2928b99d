"use client"

import { useEffect, useState } from "react"
import { VersionService } from "@/lib/version-service"

/**
 * Hook pour initialiser le système de version au démarrage de l'application
 */
export function useVersionSystem() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const initializeSystem = async () => {
      try {
        setIsLoading(true)
        const result = await VersionService.initializeVersionSystem()
        setIsInitialized(result)
        setIsLoading(false)
      } catch (err) {
        console.error("Failed to initialize version system:", err)
        setError(err instanceof Error ? err : new Error(String(err)))
        setIsLoading(false)
      }
    }

    initializeSystem()
  }, [])

  return { isInitialized, isLoading, error }
}
