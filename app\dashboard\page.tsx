"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { getUserGroups } from "@/lib/user-utils"
import { NewsFeedItem } from "@/components/news-feed-item"
import { Skeleton } from "@/components/ui/skeleton"
import { Newspaper } from "lucide-react"
import { collection, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import { preloadImages } from "@/lib/image-optimization"

interface NewsItem {
  id: string
  title: string
  content: string
  summary?: string
  imageUrl?: string
  createdAt: any
  targetGroups: string[]
  isPinned: boolean
  displayOrder?: number
  showThumbnail?: boolean
}

export default function Dashboard() {
  const { user } = useAuth()
  const [userGroups, setUserGroups] = useState<string[]>([])
  const [newsItems, setNewsItems] = useState<NewsItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    // Drapeau pour suivre si le composant est monté
    let isMounted = true

    const fetchUserGroups = async () => {
      // Vérification de l'authentification
      if (!user) return []

      try {
        const groups = await getUserGroups(user.uid)
        // Vérifier si le composant est toujours monté avant de mettre à jour l'état
        if (isMounted) {
          setUserGroups(groups)
        }
        return groups
      } catch (error: any) {
        console.error("Error getting user groups:", error)
        toast({
          title: "Erreur",
          description: `Impossible de charger les groupes d'utilisateurs: ${error.message}`,
          variant: "destructive",
        })
        return []
      }
    }

    const fetchNews = async () => {
      try {
        // Vérifier si le composant est toujours monté
        if (!isMounted) return

        setIsLoading(true)

        // Vérifier si l'utilisateur est authentifié avant de continuer
        if (!user) {
          // Si l'utilisateur n'est pas authentifié, vider la liste d'actualités et arrêter
          if (isMounted) {
            setNewsItems([])
            setIsLoading(false)
          }
          return
        }

        // 1. Récupérer les groupes de l'utilisateur
        const groups = await fetchUserGroups()

        // Vérifier à nouveau si le composant est monté après l'opération asynchrone
        if (!isMounted) return

        // 2. Récupérer tous les articles publiés
        try {
          const newsRef = collection(db(), "news")
          const newsQuery = query(newsRef, where("isPublished", "==", true))
          const newsSnapshot = await getDocs(newsQuery)

          // Vérifier à nouveau si le composant est monté après l'opération asynchrone
          if (!isMounted) return

          // 3. Filtrer et transformer les articles
          const items: NewsItem[] = []
          newsSnapshot.forEach((doc) => {
            const data = doc.data()

            // Vérifier si l'article est destiné à tous les groupes ou aux groupes de l'utilisateur
            const isForUser =
              data.targetGroups?.includes("all") ||
              (Array.isArray(data.targetGroups) && data.targetGroups.some((group: string) => groups.includes(group)))

            if (isForUser) {
              items.push({
                id: doc.id,
                title: data.title || "Sans titre",
                content: data.content || "",
                summary: data.summary || "",
                imageUrl: data.imageUrl || "",
                createdAt: data.createdAt || new Date(),
                targetGroups: data.targetGroups || [],
                isPinned: data.isPinned || false,
                displayOrder: data.displayOrder,
                showThumbnail: data.showThumbnail,
              })
            }
          })

          // 4. Trier les articles
          const sortedItems = items.sort((a, b) => {
            // D'abord par épinglé
            if (a.isPinned && !b.isPinned) return -1
            if (!a.isPinned && b.isPinned) return 1

            // Puis par ordre d'affichage si disponible
            if (a.displayOrder !== undefined && b.displayOrder !== undefined) {
              if (a.displayOrder !== b.displayOrder) {
                return a.displayOrder - b.displayOrder
              }
            }

            // Enfin par date de création
            const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
            const dateB = b.createdAt?.toDate?.() || new Date(a.createdAt)
            return dateB.getTime() - dateA.getTime()
          })

          // Vérifier si le composant est toujours monté avant de mettre à jour l'état
          if (isMounted) {
            setNewsItems(sortedItems)
          }

          // Précharger les images des actualités pour améliorer les performances
          const imagePaths = sortedItems
            .filter((item) => item.imageUrl)
            .map((item) => item.imageUrl as string)
            .slice(0, 10) // Limiter aux 10 premières pour éviter de surcharger

          if (imagePaths.length > 0) {
            preloadImages(imagePaths, "small")
          }
        } catch (queryError: any) {
          console.error("Error querying news collection:", queryError)

          // Si l'erreur est liée à un index manquant, essayer une requête simplifiée
          if (queryError.message && queryError.message.includes("index")) {
            try {
              // Récupérer tous les documents de la collection sans filtres complexes
              const simpleNewsRef = collection(db(), "news")
              const simpleSnapshot = await getDocs(simpleNewsRef)

              // Filtrer manuellement les articles publiés
              const items: NewsItem[] = []
              simpleSnapshot.forEach((doc) => {
                const data = doc.data()

                if (data.isPublished === true) {
                  // Vérifier si l'article est destiné à tous les groupes ou aux groupes de l'utilisateur
                  const isForUser =
                    data.targetGroups?.includes("all") ||
                    (Array.isArray(data.targetGroups) &&
                      data.targetGroups.some((group: string) => groups.includes(group)))

                  if (isForUser) {
                    items.push({
                      id: doc.id,
                      title: data.title || "Sans titre",
                      content: data.content || "",
                      summary: data.summary || "",
                      imageUrl: data.imageUrl || "",
                      createdAt: data.createdAt || new Date(),
                      targetGroups: data.targetGroups || [],
                      isPinned: data.isPinned || false,
                      displayOrder: data.displayOrder,
                      showThumbnail: data.showThumbnail,
                    })
                  }
                }
              })

              // Trier manuellement les articles
              const sortedItems = items.sort((a, b) => {
                // D'abord par épinglé
                if (a.isPinned && !b.isPinned) return -1
                if (!a.isPinned && b.isPinned) return 1

                // Puis par ordre d'affichage si disponible
                if (a.displayOrder !== undefined && b.displayOrder !== undefined) {
                  if (a.displayOrder !== b.displayOrder) {
                    return a.displayOrder - b.displayOrder
                  }
                }

                // Enfin par date de création
                const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
                const dateB = b.createdAt?.toDate?.() || new Date(a.createdAt)
                return dateB.getTime() - dateA.getTime()
              })

              // Mettre à jour l'état
              if (isMounted) {
                setNewsItems(sortedItems)
              }

              // Précharger les images
              const imagePaths = sortedItems
                .filter((item) => item.imageUrl)
                .map((item) => item.imageUrl as string)
                .slice(0, 10)

              if (imagePaths.length > 0) {
                preloadImages(imagePaths, "small")
              }
            } catch (fallbackError) {
              console.error("Error with fallback news query:", fallbackError)
              if (isMounted) {
                setNewsItems([])
              }
            }
          } else {
            // Si ce n'est pas une erreur d'index, vider la liste d'actualités
            if (isMounted) {
              setNewsItems([])
            }
          }
        }
      } catch (error: any) {
        // Vérifier si le composant est toujours monté avant de logger l'erreur
        if (isMounted) {
          console.error("Error fetching news:", error)
          toast({
            title: "Erreur",
            description: `Impossible de charger les actualités: ${error.message}`,
            variant: "destructive",
          })
        }
        setNewsItems([]) // Ensure newsItems is an empty array in case of error
      } finally {
        // Vérifier si le composant est toujours monté avant de mettre à jour l'état
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchNews()

    // Fonction de nettoyage pour annuler les opérations en cours lors du démontage
    return () => {
      isMounted = false
    }
  }, [user, toast])

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 border-b pb-4 mb-6">
        <Newspaper className="h-6 w-6 text-primary" />
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Fil d'actualité</h2>
        </div>
      </div>

      <div className="grid gap-4 sm:gap-6">
        {isLoading ? (
          // Loading skeletons with improved design
          Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="rounded-lg border p-0 overflow-hidden">
              <div className="flex flex-col sm:flex-row">
                <div className="w-full sm:w-32 h-24 sm:h-32">
                  <Skeleton className="h-full w-full" />
                </div>
                <div className="p-4 flex-1">
                  <Skeleton className="h-6 w-2/3 mb-3" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-4/5 mb-4" />
                  <div className="flex justify-end">
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : newsItems.length > 0 ? (
          newsItems.map((item) => (
            <NewsFeedItem
              key={item.id}
              item={{
                ...item,
                date: item.createdAt?.toDate?.() || new Date(item.createdAt),
              }}
            />
          ))
        ) : (
          <div className="rounded-lg border p-8 text-center bg-gray-50/50 dark:bg-gray-900/50">
            <Newspaper className="h-12 w-12 mx-auto mb-3 text-gray-400" />
            <h3 className="text-lg font-medium">Aucune actualité disponible</h3>
            <p className="text-muted-foreground mt-2">Revenez plus tard pour découvrir les nouvelles actualités</p>
          </div>
        )}
      </div>
    </div>
  )
}
