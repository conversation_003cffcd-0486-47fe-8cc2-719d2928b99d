"use client"

import { X } from "lucide-react"
import { useState } from "react"

interface WindowsInstallGuideProps {
  onClose: () => void
  browserType: "chrome" | "edge" | "firefox" | "other"
}

export function WindowsInstallGuide({ onClose, browserType }: WindowsInstallGuideProps) {
  const [step, setStep] = useState(1)
  const totalSteps = browserType === "firefox" ? 1 : 3

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      onClose()
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
        >
          <X size={20} />
        </button>

        <div className="mb-4 text-center">
          <h2 className="text-xl font-bold">Installer ACR Direct sur Windows</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Étape {step} sur {totalSteps}
          </p>
        </div>

        <div className="mb-6">
          {browserType === "chrome" && (
            <>
              {step === 1 && (
                <div className="text-center">
                  <p className="mb-4">Cliquez sur les trois points (...) en haut à droite de Chrome</p>
                  <div className="relative mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute right-2 top-1 h-6 w-6 rounded-full bg-gray-400"></div>
                  </div>
                </div>
              )}
              {step === 2 && (
                <div className="text-center">
                  <p className="mb-4">Sélectionnez "Installer ACR Direct..."</p>
                  <div className="relative mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute right-0 top-8 w-48 rounded-md bg-white p-2 shadow-lg dark:bg-gray-800">
                      <div className="mb-2 h-6 rounded bg-gray-100 dark:bg-gray-700"></div>
                      <div className="h-6 rounded bg-blue-100 dark:bg-blue-900"></div>
                    </div>
                  </div>
                </div>
              )}
              {step === 3 && (
                <div className="text-center">
                  <p className="mb-4">Confirmez l'installation dans la boîte de dialogue</p>
                  <div className="mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="flex h-40 flex-col items-center justify-center bg-white p-4 dark:bg-gray-800">
                      <p className="mb-4 text-sm">Installer ACR Direct ?</p>
                      <div className="flex space-x-2">
                        <div className="h-8 w-20 rounded bg-blue-500"></div>
                        <div className="h-8 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {browserType === "edge" && (
            <>
              {step === 1 && (
                <div className="text-center">
                  <p className="mb-4">Cliquez sur les trois points (...) en haut à droite de Edge</p>
                  <div className="relative mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute right-2 top-1 h-6 w-6 rounded-full bg-gray-400"></div>
                  </div>
                </div>
              )}
              {step === 2 && (
                <div className="text-center">
                  <p className="mb-4">Sélectionnez "Applications" puis "Installer cette application"</p>
                  <div className="relative mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute right-0 top-8 w-48 rounded-md bg-white p-2 shadow-lg dark:bg-gray-800">
                      <div className="mb-2 h-6 rounded bg-gray-100 dark:bg-gray-700"></div>
                      <div className="h-6 rounded bg-blue-100 dark:bg-blue-900"></div>
                    </div>
                  </div>
                </div>
              )}
              {step === 3 && (
                <div className="text-center">
                  <p className="mb-4">Confirmez l'installation dans la boîte de dialogue</p>
                  <div className="mx-auto h-48 w-64 overflow-hidden rounded-md border">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700"></div>
                    <div className="flex h-40 flex-col items-center justify-center bg-white p-4 dark:bg-gray-800">
                      <p className="mb-4 text-sm">Installer ACR Direct ?</p>
                      <div className="flex space-x-2">
                        <div className="h-8 w-20 rounded bg-blue-500"></div>
                        <div className="h-8 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {browserType === "firefox" && (
            <div className="text-center">
              <p className="mb-4">
                Firefox ne prend pas encore en charge l'installation de PWA. Veuillez utiliser Chrome ou Edge.
              </p>
              <div className="mx-auto h-48 w-64 overflow-hidden rounded-md border bg-white p-4 dark:bg-gray-800">
                <div className="flex h-full flex-col items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="64"
                    height="64"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mb-4 text-orange-500"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="12" />
                    <line x1="12" y1="16" x2="12.01" y2="16" />
                  </svg>
                  <p className="text-sm">Veuillez utiliser Chrome ou Edge</p>
                </div>
              </div>
            </div>
          )}

          {browserType === "other" && (
            <div className="text-center">
              <p className="mb-4">
                Votre navigateur ne prend pas en charge l'installation de PWA. Veuillez utiliser Chrome ou Edge.
              </p>
              <div className="mx-auto h-48 w-64 overflow-hidden rounded-md border bg-white p-4 dark:bg-gray-800">
                <div className="flex h-full flex-col items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="64"
                    height="64"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mb-4 text-red-500"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="15" y1="9" x2="9" y2="15" />
                    <line x1="9" y1="9" x2="15" y2="15" />
                  </svg>
                  <p className="text-sm">Navigateur non compatible</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          {step > 1 ? (
            <button
              onClick={prevStep}
              className="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Précédent
            </button>
          ) : (
            <div></div>
          )}
          <button
            onClick={nextStep}
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            {step === totalSteps ? "Terminer" : "Suivant"}
          </button>
        </div>
      </div>
    </div>
  )
}
