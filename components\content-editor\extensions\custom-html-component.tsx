"use client"

import { NodeViewWrapper } from "@tiptap/react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Code } from "lucide-react"

export const CustomHtmlComponent = ({ node, updateAttributes }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [htmlContent, setHtmlContent] = useState(node.attrs.content || "")

  const handleSave = () => {
    updateAttributes({ content: htmlContent })
    setIsEditing(false)
  }

  return (
    <NodeViewWrapper className="not-prose my-4 border rounded-md overflow-hidden">
      <div className="bg-blue-50 p-2 flex items-center justify-between border-b">
        <div className="flex items-center gap-2">
          <Code className="h-4 w-4 text-blue-700" />
          <span className="text-sm font-medium text-blue-700">HTML personnalisé</span>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setIsEditing(!isEditing)}
          className="h-7 text-xs"
        >
          {isEditing ? "Annuler" : "Modifier"}
        </Button>
      </div>

      {isEditing ? (
        <div className="p-3">
          <Tabs defaultValue="edit">
            <TabsList className="mb-2">
              <TabsTrigger value="edit">Éditer</TabsTrigger>
              <TabsTrigger value="preview">Aperçu</TabsTrigger>
            </TabsList>

            <TabsContent value="edit">
              <Textarea
                value={htmlContent}
                onChange={(e) => setHtmlContent(e.target.value)}
                className="font-mono text-sm min-h-[150px]"
                placeholder="<div>Insérez votre HTML ici...</div>"
              />
              <div className="flex justify-end mt-2">
                <Button
                  type="button"
                  onClick={handleSave}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Appliquer
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="border p-3 rounded-md min-h-[150px]">
              <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="p-3">
          {htmlContent ? (
            <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
          ) : (
            <p className="text-muted-foreground text-center py-4">Aucun contenu HTML</p>
          )}
        </div>
      )}
    </NodeViewWrapper>
  )
}
