"use client"

import { Download } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { But<PERSON> } from "@/components/ui/button"
import { IOSInstallGuide } from "./ios-install-guide"
import { WindowsInstallGuide } from "./windows-install-guide"
import { useState } from "react"

export function InstallButton() {
  const { isInstallable, isInstalled, promptInstall, isIOS, isWindows, showIOSGuide, closeIOSGuide } = usePWAInstall()
  const [showWindowsGuide, setShowWindowsGuide] = useState(false)
  const [browserType, setBrowserType] = useState<"chrome" | "edge" | "firefox" | "other">("chrome")

  // Ne pas afficher le bouton si l'app est déjà installée
  if (isInstalled) return null

  const handleInstallClick = () => {
    if (isWindows) {
      // Détecter le navigateur
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = /chrome/.test(userAgent) && !/edge|edg/.test(userAgent)
      const isEdge = /edge|edg/.test(userAgent)
      const isFirefox = /firefox/.test(userAgent)

      if (isChrome) setBrowserType("chrome")
      else if (isEdge) setBrowserType("edge")
      else if (isFirefox) setBrowserType("firefox")
      else setBrowserType("other")

      setShowWindowsGuide(true)
    }

    promptInstall()
  }

  const closeWindowsGuide = () => {
    setShowWindowsGuide(false)
  }

  return (
    <>
      <Button variant="ghost" size="sm" onClick={handleInstallClick} className="w-full justify-start">
        <Download className="mr-2 h-4 w-4" />
        <span>Installer l'application</span>
      </Button>

      {showIOSGuide && <IOSInstallGuide onClose={closeIOSGuide} />}
      {showWindowsGuide && <WindowsInstallGuide onClose={closeWindowsGuide} browserType={browserType} />}
    </>
  )
}
