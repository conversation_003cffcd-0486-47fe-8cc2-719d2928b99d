"use client"

import { useState, useEffect } from "react"
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  type DocumentData,
  type FirestoreError,
} from "firebase/firestore"
import { db } from "@/lib/firebase"

interface UseFirestoreCollectionOptions {
  where?: [string, "==" | "!=" | ">" | ">=" | "<" | "<=", any][] // Where conditions
  orderBy?: [string, "asc" | "desc"][] // Order by conditions
  limit?: number // Limit the number of documents
}

export function useFirestoreCollection<T = DocumentData>(path: string, options: UseFirestoreCollectionOptions = {}) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<FirestoreError | null>(null)

  // Destructure options with defaults
  const { where: whereConditions = [], orderBy: orderByConditions = [], limit: limitCount = 20 } = options

  useEffect(() => {
    // Define an async function inside useEffect
    const fetchData = async () => {
      if (!path) {
        setLoading(false)
        return
      }

      setLoading(true)
      setError(null)

      try {
        // Start with the collection reference
        const queryRef = collection(db(), path)

        // Build the query
        const constraints = []

        // Add where conditions
        if (whereConditions.length > 0) {
          whereConditions.forEach(([field, operator, value]) => {
            constraints.push(where(field, operator, value))
          })
        }

        // Add a single orderBy condition to avoid complex index requirements
        if (orderByConditions.length > 0) {
          // Just use the first orderBy condition
          const [field, direction] = orderByConditions[0]
          constraints.push(orderBy(field, direction))
        }

        // Add limit
        if (limitCount > 0) {
          constraints.push(limit(limitCount))
        }

        // Create the query
        const q = query(queryRef, ...constraints)

        // Execute the query
        const querySnapshot = await getDocs(q)

        // Process the results
        const documents = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as T[]

        setData(documents)
      } catch (err) {
        console.error(`Error fetching collection ${path}:`, err)
        setError(err as FirestoreError)
      } finally {
        setLoading(false)
      }
    }

    // Call the async function
    fetchData()

    // Cleanup function
    return () => {
      // No cleanup needed for a simple fetch
    }
  }, [path, JSON.stringify(whereConditions), JSON.stringify(orderByConditions), limitCount])

  return { data, loading, error }
}
