"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Loader2, RefreshCw, Trash2, Wifi, WifiOff, CheckCircle2, XCircle, Clock, RotateCw } from "lucide-react"
import { useSync } from "@/lib/hooks/use-sync"
import { SyncOperationType } from "@/lib/sync-service"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

export function SyncManager() {
  const {
    operations,
    isOnline,
    pendingCount,
    failedCount,
    completedCount,
    synchronize,
    retryOperation,
    removeOperation,
    clearCompletedOperations,
  } = useSync()

  const [isSyncing, setIsSyncing] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState<string>("pending")
  const [message, setMessage] = useState<{ type: "success" | "error" | "info"; text: string } | null>(null)

  // Synchroniser les opérations en attente
  const handleSynchronize = async () => {
    if (isSyncing) return

    setIsSyncing(true)
    setMessage({ type: "info", text: "Synchronisation en cours..." })

    try {
      await synchronize()
      setMessage({ type: "success", text: "Synchronisation terminée avec succès" })
    } catch (error) {
      setMessage({ type: "error", text: `Erreur lors de la synchronisation: ${error}` })
    } finally {
      setIsSyncing(false)
    }
  }

  // Réessayer une opération échouée
  const handleRetryOperation = async (operationId: string) => {
    try {
      await retryOperation(operationId)
      setMessage({ type: "info", text: "Opération ajoutée à la file d'attente" })
    } catch (error) {
      setMessage({ type: "error", text: `Erreur lors de la reprise de l'opération: ${error}` })
    }
  }

  // Supprimer une opération
  const handleRemoveOperation = async (operationId: string) => {
    try {
      await removeOperation(operationId)
      setMessage({ type: "success", text: "Opération supprimée" })
    } catch (error) {
      setMessage({ type: "error", text: `Erreur lors de la suppression de l'opération: ${error}` })
    }
  }

  // Supprimer toutes les opérations terminées
  const handleClearCompletedOperations = async () => {
    try {
      await clearCompletedOperations()
      setMessage({ type: "success", text: "Opérations terminées supprimées" })
    } catch (error) {
      setMessage({ type: "error", text: `Erreur lors de la suppression des opérations: ${error}` })
    }
  }

  // Obtenir le libellé du type d'opération
  const getOperationTypeLabel = (type: SyncOperationType) => {
    switch (type) {
      case SyncOperationType.CREATE:
        return "Création"
      case SyncOperationType.UPDATE:
        return "Mise à jour"
      case SyncOperationType.DELETE:
        return "Suppression"
      default:
        return "Inconnu"
    }
  }

  // Obtenir la couleur du type d'opération
  const getOperationTypeColor = (type: SyncOperationType) => {
    switch (type) {
      case SyncOperationType.CREATE:
        return "bg-green-100 text-green-800"
      case SyncOperationType.UPDATE:
        return "bg-blue-100 text-blue-800"
      case SyncOperationType.DELETE:
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Obtenir l'icône du statut de l'opération
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-amber-500" />
      case "processing":
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  // Obtenir le libellé du statut de l'opération
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return "En attente"
      case "processing":
        return "En cours"
      case "completed":
        return "Terminée"
      case "failed":
        return "Échouée"
      default:
        return "Inconnu"
    }
  }

  // Filtrer les opérations en fonction de l'onglet actif
  const filteredOperations = operations.filter((operation) => {
    switch (activeTab) {
      case "pending":
        return operation.status === "pending" || operation.status === "processing"
      case "failed":
        return operation.status === "failed"
      case "completed":
        return operation.status === "completed"
      case "all":
        return true
      default:
        return false
    }
  })

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Gestionnaire de synchronisation</span>
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <div className="flex items-center text-green-600 text-sm">
                <Wifi className="h-4 w-4 mr-1" />
                <span>En ligne</span>
              </div>
            ) : (
              <div className="flex items-center text-amber-600 text-sm">
                <WifiOff className="h-4 w-4 mr-1" />
                <span>Hors ligne</span>
              </div>
            )}
          </div>
        </CardTitle>
        <CardDescription>Gérez la synchronisation des données entre le client et le serveur</CardDescription>
      </CardHeader>
      <CardContent>
        {message && (
          <Alert
            className={`mb-4 ${
              message.type === "success"
                ? "bg-green-50 border-green-200"
                : message.type === "error"
                  ? "bg-red-50 border-red-200"
                  : "bg-blue-50 border-blue-200"
            }`}
          >
            <AlertTitle>
              {message.type === "success" ? "Succès" : message.type === "error" ? "Erreur" : "Information"}
            </AlertTitle>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        {/* Afficher l'état de la synchronisation */}
        <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-muted/20 p-4 rounded-lg flex items-center">
            <Clock className="h-8 w-8 mr-3 text-amber-500" />
            <div>
              <h3 className="font-medium">En attente</h3>
              <p className="text-2xl font-bold">{pendingCount}</p>
            </div>
          </div>
          <div className="bg-muted/20 p-4 rounded-lg flex items-center">
            <XCircle className="h-8 w-8 mr-3 text-red-500" />
            <div>
              <h3 className="font-medium">Échouées</h3>
              <p className="text-2xl font-bold">{failedCount}</p>
            </div>
          </div>
          <div className="bg-muted/20 p-4 rounded-lg flex items-center">
            <CheckCircle2 className="h-8 w-8 mr-3 text-green-500" />
            <div>
              <h3 className="font-medium">Terminées</h3>
              <p className="text-2xl font-bold">{completedCount}</p>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <Button
            onClick={handleSynchronize}
            disabled={isSyncing || !isOnline || pendingCount === 0}
            className="flex-1"
          >
            {isSyncing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Synchronisation en cours...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Synchroniser maintenant
              </>
            )}
          </Button>

          <Button
            onClick={handleClearCompletedOperations}
            variant="outline"
            className="flex-1"
            disabled={completedCount === 0}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Nettoyer les terminées
          </Button>
        </div>

        <Tabs defaultValue="pending" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending">En attente {pendingCount > 0 && `(${pendingCount})`}</TabsTrigger>
            <TabsTrigger value="failed">Échouées {failedCount > 0 && `(${failedCount})`}</TabsTrigger>
            <TabsTrigger value="completed">Terminées {completedCount > 0 && `(${completedCount})`}</TabsTrigger>
            <TabsTrigger value="all">Toutes {operations.length > 0 && `(${operations.length})`}</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4 mt-4">
            <div className="space-y-4">
              {filteredOperations.length === 0 ? (
                <div className="flex justify-center py-8">
                  <p className="text-sm text-muted-foreground">
                    Aucune opération{" "}
                    {activeTab === "pending"
                      ? "en attente"
                      : activeTab === "failed"
                        ? "échouée"
                        : activeTab === "completed"
                          ? "terminée"
                          : ""}
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredOperations.map((operation) => (
                    <div key={operation.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          {getStatusIcon(operation.status)}
                          <span className="ml-2 font-medium">
                            {operation.collection}/{operation.documentId}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={getOperationTypeColor(operation.type)}>
                            {getOperationTypeLabel(operation.type)}
                          </Badge>
                          <Badge variant="outline">{getStatusLabel(operation.status)}</Badge>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground mb-2">
                        <div className="flex justify-between">
                          <span>
                            Créée {formatDistanceToNow(new Date(operation.timestamp), { addSuffix: true, locale: fr })}
                          </span>
                          {operation.lastRetry && (
                            <span>
                              Dernière tentative{" "}
                              {formatDistanceToNow(new Date(operation.lastRetry), { addSuffix: true, locale: fr })}
                            </span>
                          )}
                        </div>
                      </div>

                      {operation.error && (
                        <div className="text-xs text-red-600 mb-2 bg-red-50 p-2 rounded">
                          <strong>Erreur:</strong> {operation.error}
                        </div>
                      )}

                      <div className="flex justify-end space-x-2 mt-2">
                        {operation.status === "failed" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRetryOperation(operation.id)}
                            disabled={!isOnline}
                          >
                            <RotateCw className="mr-1 h-3 w-3" />
                            Réessayer
                          </Button>
                        )}
                        <Button size="sm" variant="outline" onClick={() => handleRemoveOperation(operation.id)}>
                          <Trash2 className="mr-1 h-3 w-3" />
                          Supprimer
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Les opérations en attente seront synchronisées automatiquement lorsque la connexion sera rétablie
        </p>
      </CardFooter>
    </Card>
  )
}

export default SyncManager
