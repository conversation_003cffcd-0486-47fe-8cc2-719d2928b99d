import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Clock, User, CalendarIcon } from "lucide-react"
import type { VersionMetadata } from "@/lib/history-types"
import type { ContentType } from "@/lib/history-types"

interface VersionDetailsCardProps {
  version: VersionMetadata
  title: string
  contentType: ContentType
  children?: React.ReactNode
}

export function VersionDetailsCard({ version, title, contentType, children }: VersionDetailsCardProps) {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return "Date inconnue"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return format(date, "d MMMM yyyy à HH:mm", { locale: fr })
  }

  return (
    <Card>
      <CardHeader className="bg-muted/50">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">{title}</CardTitle>
          <Badge variant="outline" className="bg-primary/10 text-primary">
            Version du {formatDate(version.createdAt)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2" />
            <span>Modifié par {version.createdBy.displayName}</span>
          </div>
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>{formatDate(version.createdAt)}</span>
          </div>
          {version.description && (
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>{version.description}</span>
            </div>
          )}
        </div>

        {children}
      </CardContent>
    </Card>
  )
}
