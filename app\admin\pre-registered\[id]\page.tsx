"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { db } from "@/lib/firebase"
import { doc, getDoc, updateDoc, deleteDoc, collection, query, where, getDocs } from "firebase/firestore"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, ArrowLeft, Trash2, Save, UserCheck, UserX } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertD<PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface PreRegisteredUser {
  id: string
  clientCode: string
  clientType: string
  network: string
  department: string
  name: string
  siretLastDigits: string
  additionalGroups: string[]
  importedAt: any
  isRegistered?: boolean
  registeredUserId?: string
  registeredAt?: any
  registeredUser?: {
    id: string
    email: string
    displayName: string
    createdAt: any
  }
}

interface UserDetailProps {
  params: {
    id: string
  }
}

export default function UserDetailPage({ params }: UserDetailProps) {
  const id = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [user, setUser] = useState<PreRegisteredUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    clientCode: "",
    clientType: "",
    network: "",
    department: "",
    name: "",
    siretLastDigits: "",
    additionalGroups: [] as string[],
  })

  // New group input
  const [newGroup, setNewGroup] = useState("")

  useEffect(() => {
    fetchUserData()
  }, [id])

  // Update form data when user data changes
  useEffect(() => {
    if (user) {
      setFormData({
        clientCode: user.clientCode || "",
        clientType: user.clientType || "",
        network: user.network || "",
        department: user.department || "",
        name: user.name || "",
        siretLastDigits: user.siretLastDigits || "",
        additionalGroups: user.additionalGroups || [],
      })
    }
  }, [user])

  const fetchUserData = async () => {
    try {
      setIsLoading(true)

      // Get pre-registered user data
      const docRef = doc(db, "importedUsers", id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        toast({
          title: "Erreur",
          description: "Utilisateur introuvable",
          variant: "destructive",
        })
        router.push("/admin/pre-registered")
        return
      }

      const userData = docSnap.data() as Omit<PreRegisteredUser, "id">

      // Check if user is registered
      let registeredUser = null
      const usersRef = collection(db, "users")
      const q = query(
        usersRef,
        where("clientCode", "==", userData.clientCode),
        where("siretLastDigits", "==", userData.siretLastDigits),
      )

      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const registeredUserDoc = querySnapshot.docs[0]
        registeredUser = {
          id: registeredUserDoc.id,
          ...registeredUserDoc.data(),
        }
      }

      setUser({
        id: docSnap.id,
        ...userData,
        additionalGroups: userData.additionalGroups || [],
        isRegistered: !!registeredUser,
        registeredUserId: registeredUser?.id,
        registeredAt: registeredUser?.createdAt,
        registeredUser: registeredUser
          ? {
              id: registeredUser.id,
              email: registeredUser.email,
              displayName: registeredUser.displayName,
              createdAt: registeredUser.createdAt,
            }
          : undefined,
      })
    } catch (error) {
      console.error("Erreur lors du chargement des données:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleAddGroup = () => {
    if (newGroup.trim() && !formData.additionalGroups.includes(newGroup.trim())) {
      setFormData((prev) => ({
        ...prev,
        additionalGroups: [...prev.additionalGroups, newGroup.trim()],
      }))
      setNewGroup("")
    }
  }

  const handleRemoveGroup = (group: string) => {
    setFormData((prev) => ({
      ...prev,
      additionalGroups: prev.additionalGroups.filter((g) => g !== group),
    }))
  }

  const handleSave = async () => {
    try {
      setIsSaving(true)

      // Validate required fields
      if (!formData.clientCode || !formData.clientType || !formData.siretLastDigits) {
        toast({
          title: "Erreur",
          description: "Veuillez remplir tous les champs obligatoires",
          variant: "destructive",
        })
        return
      }

      // Update document
      await updateDoc(doc(db, "importedUsers", id), {
        ...formData,
        updatedAt: new Date(),
      })

      toast({
        title: "Succès",
        description: "Les modifications ont été enregistrées",
      })

      // Refresh data
      fetchUserData()
    } catch (error) {
      console.error("Erreur lors de l'enregistrement:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'enregistrer les modifications",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async () => {
    try {
      setIsDeleting(true)

      await deleteDoc(doc(db, "importedUsers", id))

      toast({
        title: "Succès",
        description: "L'utilisateur a été supprimé",
      })

      router.push("/admin/pre-registered")
    } catch (error) {
      console.error("Erreur lors de la suppression:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer l'utilisateur",
        variant: "destructive",
      })
      setIsDeleting(false)
    }
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="py-10 text-center">
            <p>Utilisateur introuvable</p>
            <Button className="mt-4" onClick={() => router.push("/admin/pre-registered")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour à la liste
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => router.push("/admin/pre-registered")} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
          <h1 className="text-3xl font-bold">Détails de l'utilisateur pré-enregistré</h1>
        </div>

        <div className="flex gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                <Trash2 className="mr-2 h-4 w-4" />
                Supprimer
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                <AlertDialogDescription>
                  Êtes-vous sûr de vouloir supprimer cet utilisateur pré-enregistré ? Cette action est irréversible.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Annuler</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                  {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : "Supprimer"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">Informations</TabsTrigger>
              <TabsTrigger value="edit">Modifier</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Informations de l'utilisateur</CardTitle>
                  <CardDescription>Détails de l'utilisateur pré-enregistré</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Code client</h3>
                      <p className="font-semibold">{user.clientCode}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">5 derniers SIRET</h3>
                      <p className="font-semibold">{user.siretLastDigits}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Nom</h3>
                      <p className="font-semibold">{user.name || "Non spécifié"}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Type de client</h3>
                      <p className="font-semibold">{user.clientType}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Réseau</h3>
                      <p className="font-semibold">{user.network || "Non spécifié"}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Département</h3>
                      <p className="font-semibold">{user.department || "Non spécifié"}</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Groupes supplémentaires</h3>
                    <div className="flex flex-wrap gap-2">
                      {user.additionalGroups && user.additionalGroups.length > 0 ? (
                        user.additionalGroups.map((group, index) => (
                          <Badge key={index} variant="secondary">
                            {group}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-muted-foreground">Aucun groupe supplémentaire</span>
                      )}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Date d'importation</h3>
                    <p>{formatDate(user.importedAt)}</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="edit" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Modifier les informations</CardTitle>
                  <CardDescription>Modifiez les informations de l'utilisateur pré-enregistré</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="clientCode">Code client *</Label>
                      <Input
                        id="clientCode"
                        name="clientCode"
                        value={formData.clientCode}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="siretLastDigits">5 derniers SIRET *</Label>
                      <Input
                        id="siretLastDigits"
                        name="siretLastDigits"
                        value={formData.siretLastDigits}
                        onChange={handleInputChange}
                        required
                        maxLength={5}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="name">Nom</Label>
                      <Input id="name" name="name" value={formData.name} onChange={handleInputChange} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="clientType">Type de client *</Label>
                      <Input
                        id="clientType"
                        name="clientType"
                        value={formData.clientType}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="network">Réseau</Label>
                      <Input id="network" name="network" value={formData.network} onChange={handleInputChange} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="department">Département</Label>
                      <Input
                        id="department"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  <Separator className="my-4" />

                  <div className="space-y-2">
                    <Label>Groupes supplémentaires</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.additionalGroups.map((group, index) => (
                        <Badge key={index} variant="secondary" className="px-2 py-1">
                          {group}
                          <button
                            type="button"
                            className="ml-1 text-muted-foreground hover:text-foreground"
                            onClick={() => handleRemoveGroup(group)}
                          >
                            ×
                          </button>
                        </Badge>
                      ))}
                      {formData.additionalGroups.length === 0 && (
                        <span className="text-sm text-muted-foreground">Aucun groupe ajouté</span>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Ajouter un groupe"
                        value={newGroup}
                        onChange={(e) => setNewGroup(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), handleAddGroup())}
                      />
                      <Button type="button" onClick={handleAddGroup} disabled={!newGroup.trim()}>
                        Ajouter
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={handleSave} disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Enregistrement...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Enregistrer
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Statut d'inscription</CardTitle>
            </CardHeader>
            <CardContent>
              {user.isRegistered ? (
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="bg-green-100 p-2 rounded-full mr-3">
                      <UserCheck className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">Utilisateur inscrit</p>
                      <p className="text-sm text-muted-foreground">
                        Cet utilisateur s'est inscrit le {formatDate(user.registeredAt)}
                      </p>
                    </div>
                  </div>

                  {user.registeredUser && (
                    <div className="border rounded-md p-4 mt-4">
                      <h3 className="font-medium mb-2">Informations du compte</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Nom</span>
                          <span>{user.registeredUser.displayName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Email</span>
                          <span>{user.registeredUser.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Date d'inscription</span>
                          <span>{formatDate(user.registeredUser.createdAt)}</span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        className="w-full mt-4"
                        onClick={() => router.push(`/admin/users/edit/${user.registeredUser?.id}`)}
                      >
                        Voir le profil complet
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center">
                  <div className="bg-amber-100 p-2 rounded-full mr-3">
                    <UserX className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <p className="font-medium">En attente d'inscription</p>
                    <p className="text-sm text-muted-foreground">Cet utilisateur ne s'est pas encore inscrit</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
