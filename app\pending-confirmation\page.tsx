import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ThemeToggle } from "@/components/theme-toggle"
import { AlertTriangle } from "lucide-react"

export default function PendingConfirmationPage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="w-full max-w-md space-y-8 z-10">
        <div className="bg-card border border-border shadow-lg rounded-xl p-8 text-center space-y-6">
          <div className="max-w-md mx-auto text-center">
            <AlertTriangle className="h-16 w-16 text-amber-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-4">Compte en attente de validation</h1>
            <p className="mb-4">
              Votre compte a été créé mais doit être validé par un administrateur avant que vous puissiez accéder à
              l'application.
            </p>
            <p className="mb-4">
              Si vous êtes un utilisateur pré-enregistré avec un code client et SIRET valides, votre compte aurait dû
              être activé automatiquement. Veuillez vérifier les informations saisies ou contacter un administrateur.
            </p>
            <p className="text-muted-foreground">Vous serez notifié par email lorsque votre compte sera activé.</p>
            <div className="mt-6">
              <Button variant="outline" asChild>
                <Link href="/login">Retour à la page de connexion</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
