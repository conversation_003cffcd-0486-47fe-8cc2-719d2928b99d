import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { VersionService } from "./version-service"

/**
 * Service pour gérer les éléments de menu
 */
export const MenuService = {
  /**
   * Crée un nouvel élément de menu
   * @param menuData Données de l'élément de menu
   * @param userId ID de l'utilisateur qui crée l'élément
   */
  async createMenuItem(menuData: any, userId: string): Promise<string> {
    try {
      // Ajouter l'élément de menu
      const menuItemRef = await addDoc(collection(db(), "menuItems"), {
        ...menuData,
        createdAt: new Date().toISOString(),
        createdBy: userId,
        updatedAt: new Date().toISOString(),
        updatedBy: userId,
      })

      // Incrémenter la version du menu
      await VersionService.incrementMenuVersion(userId)

      return menuItemRef.id
    } catch (error) {
      console.error("Failed to create menu item:", error)
      throw error
    }
  },

  /**
   * Met à jour un élément de menu existant
   * @param menuItemId ID de l'élément de menu
   * @param menuData Nouvelles données de l'élément
   * @param userId ID de l'utilisateur qui effectue la mise à jour
   */
  async updateMenuItem(menuItemId: string, menuData: any, userId: string): Promise<boolean> {
    try {
      // Mettre à jour l'élément de menu
      await updateDoc(doc(db(), "menuItems", menuItemId), {
        ...menuData,
        updatedAt: new Date().toISOString(),
        updatedBy: userId,
      })

      // Incrémenter la version du menu
      await VersionService.incrementMenuVersion(userId)

      return true
    } catch (error) {
      console.error(`Failed to update menu item ${menuItemId}:`, error)
      throw error
    }
  },

  /**
   * Supprime un élément de menu
   * @param menuItemId ID de l'élément de menu
   * @param userId ID de l'utilisateur qui effectue la suppression
   */
  async deleteMenuItem(menuItemId: string, userId: string): Promise<boolean> {
    try {
      // Supprimer l'élément de menu
      await deleteDoc(doc(db(), "menuItems", menuItemId))

      // Incrémenter la version du menu
      await VersionService.incrementMenuVersion(userId)

      return true
    } catch (error) {
      console.error(`Failed to delete menu item ${menuItemId}:`, error)
      throw error
    }
  },

  /**
   * Récupère tous les éléments de menu
   */
  async getAllMenuItems() {
    try {
      const menuItemsSnapshot = await getDocs(collection(db(), "menuItems"))
      const menuItems: any[] = []

      menuItemsSnapshot.forEach((doc) => {
        menuItems.push({
          id: doc.id,
          ...doc.data(),
        })
      })

      return menuItems
    } catch (error) {
      console.error("Failed to get all menu items:", error)
      throw error
    }
  },

  /**
   * Récupère les éléments de menu pour des groupes spécifiques
   * @param groups Groupes pour lesquels récupérer les éléments de menu
   */
  async getMenuItemsForGroups(groups: string[]) {
    try {
      // Si "all" est inclus, récupérer tous les éléments publiés
      if (groups.includes("all")) {
        const q = query(collection(db(), "menuItems"), where("isPublished", "==", true))

        const menuItemsSnapshot = await getDocs(q)
        const menuItems: any[] = []

        menuItemsSnapshot.forEach((doc) => {
          menuItems.push({
            id: doc.id,
            ...doc.data(),
          })
        })

        return menuItems
      }

      // Sinon, récupérer tous les éléments et filtrer côté client
      // Note: Firestore ne permet pas de requêtes "array-contains-any" avec plus de 10 valeurs
      const menuItemsSnapshot = await getDocs(collection(db(), "menuItems"))
      const menuItems: any[] = []

      menuItemsSnapshot.forEach((doc) => {
        const data = doc.data()

        // Vérifier si l'élément est publié et destiné à l'un des groupes
        if (
          data.isPublished &&
          (data.targetGroups.includes("all") || data.targetGroups.some((group: string) => groups.includes(group)))
        ) {
          menuItems.push({
            id: doc.id,
            ...data,
          })
        }
      })

      return menuItems
    } catch (error) {
      console.error("Failed to get menu items for groups:", error)
      throw error
    }
  },
}
