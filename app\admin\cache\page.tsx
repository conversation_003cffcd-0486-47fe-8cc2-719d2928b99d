import { CacheManager } from "@/components/cache-manager"
import { CacheStats } from "@/components/cache-stats"
import { AuthCacheManager } from "@/components/auth-cache-manager"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Database, HardDrive, RefreshCw } from "lucide-react"

export const metadata = {
  title: "Gestion du Cache | ACR Direct",
  description: "Gérez le cache de l'application pour optimiser les performances et le fonctionnement hors ligne",
}

export default function CachePage() {
  return (
    <div className="container py-6 space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Gestion du Cache</h1>
        <p className="text-muted-foreground">
          Optimisez les performances et le fonctionnement hors ligne de l'application
        </p>
      </div>

      <Tabs defaultValue="manager" className="mb-8">
        <TabsList className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
          <TabsTrigger value="manager">Gestionnaire de cache</TabsTrigger>
          <TabsTrigger value="auth">Authentification</TabsTrigger>
          <TabsTrigger value="stats">Statistiques</TabsTrigger>
        </TabsList>

        <TabsContent value="manager" className="mt-4">
          <CacheManager />
        </TabsContent>

        <TabsContent value="auth" className="mt-4">
          <AuthCacheManager />
        </TabsContent>

        <TabsContent value="stats" className="mt-4">
          <CacheStats />
        </TabsContent>
      </Tabs>

      <Tabs defaultValue="strategies" className="mt-8">
        <TabsList className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
          <TabsTrigger value="strategies">Stratégies de cache</TabsTrigger>
          <TabsTrigger value="offline">Fonctionnement hors ligne</TabsTrigger>
          <TabsTrigger value="architecture">Architecture</TabsTrigger>
        </TabsList>

        <TabsContent value="strategies" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Stratégies de mise en cache</CardTitle>
              <CardDescription>
                L'application utilise différentes stratégies de mise en cache selon le type de contenu
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <span className="bg-blue-100 text-blue-800 p-1 rounded mr-2">Network First</span>
                    Pages HTML et API
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Tente d'abord de charger depuis le réseau, puis utilise le cache si le réseau échoue. Garantit des
                    données fraîches tout en permettant un fonctionnement hors ligne.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <span className="bg-green-100 text-green-800 p-1 rounded mr-2">Cache First</span>
                    Images et polices
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Charge d'abord depuis le cache pour des performances optimales, puis met à jour en arrière-plan.
                    Idéal pour les ressources qui changent rarement.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <span className="bg-purple-100 text-purple-800 p-1 rounded mr-2">Stale While Revalidate</span>
                    Scripts et styles
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Affiche la version en cache pendant qu'une mise à jour est récupérée en arrière-plan. Équilibre
                    parfait entre performance et fraîcheur des données.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <span className="bg-amber-100 text-amber-800 p-1 rounded mr-2">Background Sync</span>
                    Requêtes POST et mutations
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Met en file d'attente les requêtes effectuées hors ligne et les envoie automatiquement lorsque la
                    connexion est rétablie.
                  </p>
                </div>
              </div>

              <div className="text-sm text-muted-foreground mt-4">
                <p>
                  Ces stratégies sont configurées dans le Service Worker et optimisées pour offrir la meilleure
                  expérience utilisateur possible, que l'appareil soit connecté ou non à Internet.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="offline" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Fonctionnement hors ligne</CardTitle>
              <CardDescription>
                L'application est conçue pour fonctionner parfaitement même sans connexion Internet
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Détection de l'état de connexion</h3>
                  <p className="text-sm text-muted-foreground">
                    L'application détecte automatiquement si l'appareil est en ligne ou hors ligne grâce aux événements{" "}
                    <code>online</code> et <code>offline</code> du navigateur. Une notification discrète informe
                    l'utilisateur lorsque l'état de connexion change.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Préchargement des données</h3>
                  <p className="text-sm text-muted-foreground">
                    Lors de la première connexion, l'application précharge automatiquement les données essentielles
                    (actualités, pages, profil utilisateur, etc.) pour permettre une utilisation hors ligne. Ce
                    préchargement est personnalisé en fonction du rôle et des groupes de l'utilisateur.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Synchronisation en arrière-plan</h3>
                  <p className="text-sm text-muted-foreground">
                    Les modifications effectuées hors ligne (favoris, commentaires, etc.) sont stockées localement et
                    synchronisées automatiquement avec le serveur lorsque la connexion est rétablie, sans aucune action
                    requise de la part de l'utilisateur.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Expérience utilisateur transparente</h3>
                  <p className="text-sm text-muted-foreground">
                    Pour les utilisateurs avec le rôle "Lecteur", l'expérience est identique qu'ils soient en ligne ou
                    hors ligne. Ils peuvent naviguer dans l'application, consulter les actualités et les pages, et
                    utiliser toutes les fonctionnalités de base sans aucune différence visible.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="architecture" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Architecture du cache</CardTitle>
              <CardDescription>
                L'application utilise plusieurs technologies complémentaires pour une mise en cache optimale
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <HardDrive className="h-5 w-5 mr-2 text-blue-600" />
                    Service Worker
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Intercepte les requêtes réseau et sert les ressources depuis le cache. Utilise Workbox pour
                    implémenter des stratégies de cache avancées et gérer le cycle de vie du cache.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <Database className="h-5 w-5 mr-2 text-green-600" />
                    IndexedDB
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Base de données côté client pour stocker les données structurées (actualités, pages, profil
                    utilisateur). Permet des requêtes complexes et le stockage de grandes quantités de données.
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <RefreshCw className="h-5 w-5 mr-2 text-purple-600" />
                    Cache API
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Stocke les réponses HTTP brutes (HTML, CSS, JS, images). Utilisée par le Service Worker pour mettre
                    en cache les ressources statiques et les réponses d'API.
                  </p>
                </div>
              </div>

              <div className="border rounded-lg p-4 mt-4">
                <h3 className="font-medium mb-2">Cycle de vie du cache</h3>
                <p className="text-sm text-muted-foreground">Le cache est géré automatiquement par l'application :</p>
                <ul className="list-disc pl-6 text-sm space-y-1 mt-2">
                  <li>
                    <strong>Installation</strong> : Préchargement des ressources essentielles lors de la première visite
                  </li>
                  <li>
                    <strong>Mise à jour</strong> : Vérification périodique des nouvelles versions de l'application
                  </li>
                  <li>
                    <strong>Nettoyage</strong> : Suppression automatique des anciens caches lors des mises à jour
                  </li>
                  <li>
                    <strong>Expiration</strong> : Durées de validité différentes selon le type de contenu
                  </li>
                </ul>
              </div>

              <div className="text-sm text-muted-foreground mt-4">
                <p>
                  Cette architecture multicouche permet une expérience utilisateur optimale en combinant performance,
                  fraîcheur des données et fonctionnement hors ligne.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
