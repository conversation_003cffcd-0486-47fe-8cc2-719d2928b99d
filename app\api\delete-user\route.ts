import { NextResponse } from "next/server"
import { initializeApp, getApps, cert } from "firebase-admin/app"
import { getAuth } from "firebase-admin/auth"
import { getFirestore } from "firebase-admin/firestore"

// Global variable to track initialization status
let isInitialized = false

// Improved Firebase Admin initialization with detailed error logging
function initializeFirebaseAdmin() {
  if (!isInitialized && getApps().length === 0) {
    try {
      // Log environment variables (without sensitive data)
      console.log("Firebase Admin SDK initialization - checking environment variables")
      console.log(`FIREBASE_PROJECT_ID exists: ${!!process.env.FIREBASE_PROJECT_ID}`)
      console.log(`FIREBASE_CLIENT_EMAIL exists: ${!!process.env.FIREBASE_CLIENT_EMAIL}`)
      console.log(`FIREBASE_PRIVATE_KEY exists: ${!!process.env.FIREBASE_PRIVATE_KEY}`)

      // Check if we have the required environment variables
      if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_CLIENT_EMAIL || !process.env.FIREBASE_PRIVATE_KEY) {
        throw new Error("Missing required Firebase Admin SDK credentials in environment variables")
      }

      // Ensure private key is properly formatted
      const privateKey = process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n")

      // Initialize the app with explicit credentials
      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: privateKey,
        }),
      })

      isInitialized = true
      console.log("Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("Failed to initialize Firebase Admin SDK:", error)
      throw error
    }
  } else if (getApps().length > 0) {
    console.log("Firebase Admin SDK already initialized")
    isInitialized = true
  }

  return {
    auth: getAuth(),
    firestore: getFirestore(),
  }
}

export async function DELETE(request: Request) {
  try {
    // Get the user ID from query parameters
    const { searchParams } = new URL(request.url)
    const uid = searchParams.get("uid")

    if (!uid) {
      return NextResponse.json({ error: "Missing user UID" }, { status: 400 })
    }

    console.log(`Processing deletion request for user: ${uid}`)

    // Initialize Firebase Admin with better error handling
    let admin
    try {
      admin = initializeFirebaseAdmin()
      console.log("Firebase Admin SDK services obtained successfully")
    } catch (initError) {
      console.error("Firebase Admin SDK initialization failed:", initError)
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: initError instanceof Error ? initError.message : "Unknown initialization error",
        },
        { status: 500 },
      )
    }

    // Step 1: Delete associated data in Firestore
    try {
      console.log("Starting deletion of associated Firestore data")
      const db = admin.firestore
      const batch = db.batch()
      const collections = ["favorites", "comments", "activities", "commerciaux", "userFavorites"]

      for (const collectionName of collections) {
        try {
          console.log(`Checking collection: ${collectionName} for user data`)
          const queryRef = db.collection(collectionName).where("userId", "==", uid)
          const snapshot = await queryRef.get()

          if (!snapshot.empty) {
            console.log(`Found ${snapshot.size} documents to delete in ${collectionName}`)
            snapshot.forEach((doc) => batch.delete(doc.ref))
          } else {
            console.log(`No documents found in ${collectionName} for user ${uid}`)
          }
        } catch (collectionError) {
          console.warn(`Error processing collection ${collectionName}:`, collectionError)
        }
      }

      await batch.commit()
      console.log("Successfully deleted associated Firestore data")
    } catch (firestoreError) {
      console.error("Error deleting associated Firestore data:", firestoreError)
      // Continue to try deleting the auth user even if Firestore operations fail
    }

    // Step 2: Delete the user from Firebase Authentication with enhanced error handling
    try {
      console.log(`Attempting to delete user ${uid} from Firebase Authentication`)
      const auth = admin.auth

      // Check if the user exists before trying to delete
      try {
        await auth.getUser(uid)
        console.log(`User ${uid} exists in Firebase Auth, proceeding with deletion`)
      } catch (userError) {
        console.warn(`User ${uid} not found in Firebase Auth:`, userError)
        return NextResponse.json({
          success: false,
          warning: "User not found in Firebase Authentication",
          details: userError instanceof Error ? userError.message : "Unknown user error",
        })
      }

      // Attempt to delete the user
      await auth.deleteUser(uid)
      console.log(`Successfully deleted user ${uid} from Firebase Authentication`)

      return NextResponse.json({
        success: true,
        message: "User and associated data successfully deleted",
      })
    } catch (authError) {
      console.error(`Error deleting user ${uid} from Firebase Auth:`, authError)

      // Provide detailed error information
      const errorMessage = "Failed to delete user from Firebase Authentication"
      const errorDetails = authError instanceof Error ? authError.message : "Unknown authentication error"
      const errorCode = authError.code || "unknown"

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          details: errorDetails,
          code: errorCode,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Unhandled error in delete-user route:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Failed to process user deletion request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
