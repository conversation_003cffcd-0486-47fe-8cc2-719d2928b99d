import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  orderBy,
  serverTimestamp,
  getDoc,
  type DocumentReference,
} from "firebase/firestore"
import { db, auth } from "@/lib/firebase"
import type { NewsVersionData, PageVersionData, VersionMetadata } from "./history-types"

// Définir ContentType directly dans ce fichier
export enum ContentType {
  NEWS = "news",
  PAGE = "menuItems",
}

// Function to capture a version of a news item before updating
export const captureNewsVersionBeforeUpdate = async (
  newsId: string,
  description?: string,
  contentType: ContentType = ContentType.NEWS,
) => {
  console.log(`captureNewsVersionBeforeUpdate called for newsId: ${newsId}`)
  try {
    const newsDoc = await getDoc(doc(db(), "news", newsId))

    if (newsDoc.exists()) {
      console.log(`News item with ID ${newsId} found`)
      const newsData = newsDoc.data() as NewsVersionData

      const versionData: NewsVersionData = {
        title: newsData.title,
        summary: newsData.summary,
        content: newsData.content,
        imageUrl: newsData.imageUrl,
        isPublished: newsData.isPublished,
        isPinned: newsData.isPinned,
        showFrame: newsData.showFrame,
        showThumbnail: newsData.showThumbnail,
        showContentImage: newsData.showContentImage,
        targetGroups: newsData.targetGroups,
      }

      const user = auth().currentUser
      if (!user) {
        console.error("No authenticated user found")
        // Continue without user information instead of returning
        const versionMetadata: VersionMetadata = {
          versionId: `news_${newsId}_${Date.now()}`,
          createdAt: serverTimestamp(),
          createdBy: {
            uid: "system",
            displayName: "Système",
          },
          description: description || "Mise à jour de l'article",
        }

        // Get a reference to the news document
        const newsDocRef = doc(db(), "news", newsId)

        // Create a new version in the newsVersions subcollection
        console.log(`Adding version to subcollection for newsId: ${newsId}`)
        await addDoc(collection(newsDocRef, "versions"), {
          ...versionMetadata,
          newsId: newsId,
          data: versionData,
        })
        console.log(`Version added successfully for newsId: ${newsId}`)
        return
      }

      const versionMetadata: VersionMetadata = {
        versionId: `news_${newsId}_${Date.now()}`,
        createdAt: serverTimestamp(),
        createdBy: {
          uid: user.uid,
          displayName: user.displayName || "Unknown User",
        },
        description: description || "Mise à jour de l'article",
      }

      // Get a reference to the news document
      const newsDocRef = doc(db(), "news", newsId)

      // Create a new version in the newsVersions subcollection
      console.log(`Adding version to subcollection for newsId: ${newsId}`)
      await addDoc(collection(newsDocRef, "versions"), {
        ...versionMetadata,
        newsId: newsId,
        data: versionData,
      })
      console.log(`Version added successfully for newsId: ${newsId}`)
    } else {
      console.warn(`News item with ID ${newsId} not found`)
    }
  } catch (error) {
    console.error("Error capturing news version:", error)
    throw error
  }
}

// Function to capture a version of a page before updating
export const capturePageVersionBeforeUpdate = async (
  pageId: string,
  description?: string,
  contentType: ContentType = ContentType.PAGE,
) => {
  console.log(`capturePageVersionBeforeUpdate called for pageId: ${pageId}`)
  try {
    const pageDoc = await getDoc(doc(db(), "menuItems", pageId))

    if (pageDoc.exists()) {
      console.log(`Page with ID ${pageId} found`)
      const pageData = pageDoc.data() as PageVersionData

      const versionData: PageVersionData = {
        title: pageData.title || "",
        slug: pageData.slug || "",
        content: pageData.content || "",
        isPublished: pageData.isPublished ?? false,
        showFrame: pageData.showFrame ?? true,
        targetGroups: pageData.targetGroups || [],
      }

      // Only add iconUrl if it exists
      if (pageData.iconUrl) {
        versionData.iconUrl = pageData.iconUrl
      }

      const user = auth().currentUser
      if (!user) {
        console.error("No authenticated user found")
        // Continue without user information instead of returning
        const versionMetadata: VersionMetadata = {
          versionId: `page_${pageId}_${Date.now()}`,
          createdAt: serverTimestamp(),
          createdBy: {
            uid: "system",
            displayName: "Système",
          },
          description: description || "Mise à jour de la page",
        }

        // Get a reference to the menuItems document
        const pageDocRef = doc(db(), "menuItems", pageId)

        // Create a new version in the pageVersions subcollection
        console.log(`Adding version to subcollection for pageId: ${pageId}`)
        await addDoc(collection(pageDocRef, "versions"), {
          ...versionMetadata,
          pageId: pageId,
          data: versionData,
        })
        console.log(`Version added successfully for pageId: ${pageId}`)
        return
      }

      const versionMetadata: VersionMetadata = {
        versionId: `page_${pageId}_${Date.now()}`,
        createdAt: serverTimestamp(),
        createdBy: {
          uid: user.uid,
          displayName: user.displayName || "Unknown User",
        },
        description: description || "Mise à jour de la page",
      }

      // Get a reference to the menuItems document
      const pageDocRef = doc(db(), "menuItems", pageId)

      // Create a new version in the pageVersions subcollection
      console.log(`Adding version to subcollection for pageId: ${pageId}`)
      await addDoc(collection(pageDocRef, "versions"), {
        ...versionMetadata,
        pageId: pageId,
        data: versionData,
      })
      console.log(`Version added successfully for pageId: ${pageId}`)
    } else {
      console.warn(`Page with ID ${pageId} not found`)
    }
  } catch (error) {
    console.error("Error capturing page version:", error)
    throw error
  }
}

// Function to retrieve the history of a content (news or page)
export const getContentHistory = async (contentType: ContentType, contentId: string) => {
  try {
    // Use the correct collection based on the contentType
    const baseCollection = contentType === ContentType.NEWS ? "news" : "menuItems"
    const contentDocRef: DocumentReference = doc(db(), baseCollection, contentId)
    // Pass the DocumentReference to the collection function to get the subcollection
    const versionsCollection = collection(contentDocRef, "versions")

    const versionsQuery = query(versionsCollection, orderBy("createdAt", "desc"))
    const versionsSnapshot = await getDocs(versionsQuery)

    const versions = versionsSnapshot.docs.map((doc) => ({
      ...doc.data(),
      versionId: doc.id,
    }))

    return versions
  } catch (error) {
    console.error(`Error getting content history for ${contentType} with ID ${contentId}:`, error)
    return []
  }
}

// Function to get details of a specific version
export const getVersionDetails = async (contentType: ContentType, contentId: string, versionId: string) => {
  try {
    const versionDoc = await getDoc(
      doc(db(), contentType === ContentType.NEWS ? "news" : "menuItems", contentId, "versions", versionId),
    )

    if (versionDoc.exists()) {
      return versionDoc.data()
    } else {
      console.warn(`Version with ID ${versionId} not found`)
      return null
    }
  } catch (error) {
    console.error(`Error getting version details for ${contentType} with ID ${contentId}, version ${versionId}:`, error)
    return null
  }
}
