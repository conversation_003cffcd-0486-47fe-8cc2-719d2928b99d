import { doc, getDoc, setDoc, updateDoc, increment } from "firebase/firestore"
import { db } from "@/lib/firebase"

// Constantes pour les noms de collections et documents
const SYSTEM_COLLECTION = "system"
const MENU_VERSION_DOC = "menuVersion"
const CONTENT_VERSION_DOC = "contentVersion"

/**
 * Service pour gérer les versions des différentes ressources de l'application
 */
export const VersionService = {
  /**
   * Initialise le système de version s'il n'existe pas déjà
   */
  async initializeVersionSystem() {
    try {
      const menuVersionRef = doc(db(), SYSTEM_COLLECTION, MENU_VERSION_DOC)
      const menuVersionDoc = await getDoc(menuVersionRef)

      if (!menuVersionDoc.exists()) {
        await setDoc(menuVersionRef, {
          version: 1,
          lastUpdated: new Date().toISOString(),
          updatedBy: "system",
        })
        console.log("Menu version system initialized")
      }

      const contentVersionRef = doc(db(), SYSTEM_COLLECTION, CONTENT_VERSION_DOC)
      const contentVersionDoc = await getDoc(contentVersionRef)

      if (!contentVersionDoc.exists()) {
        await setDoc(contentVersionRef, {
          version: 1,
          lastUpdated: new Date().toISOString(),
          updatedBy: "system",
        })
        console.log("Content version system initialized")
      }

      return true
    } catch (error) {
      console.error("Failed to initialize version system:", error)
      return false
    }
  },

  /**
   * Récupère la version actuelle du menu
   */
  async getMenuVersion(): Promise<number> {
    try {
      const menuVersionRef = doc(db(), SYSTEM_COLLECTION, MENU_VERSION_DOC)
      const menuVersionDoc = await getDoc(menuVersionRef)

      if (menuVersionDoc.exists()) {
        return menuVersionDoc.data().version || 1
      }

      // Si le document n'existe pas, l'initialiser
      await this.initializeVersionSystem()
      return 1
    } catch (error) {
      console.error("Failed to get menu version:", error)
      // En cas d'erreur de permission, retourner une version par défaut
      // au lieu de faire échouer toute la chaîne d'appels
      return 1
    }
  },

  /**
   * Incrémente la version du menu après une modification
   * @param userId ID de l'utilisateur qui a effectué la modification
   */
  async incrementMenuVersion(userId: string): Promise<number> {
    try {
      const menuVersionRef = doc(db(), SYSTEM_COLLECTION, MENU_VERSION_DOC)

      await updateDoc(menuVersionRef, {
        version: increment(1),
        lastUpdated: new Date().toISOString(),
        updatedBy: userId,
      })

      // Récupérer la nouvelle version
      const updatedDoc = await getDoc(menuVersionRef)
      return updatedDoc.data()?.version || 1
    } catch (error) {
      console.error("Failed to increment menu version:", error)
      return -1
    }
  },

  /**
   * Met à jour le timestamp de dernière modification des permissions d'un utilisateur
   * @param userId ID de l'utilisateur dont les permissions ont été modifiées
   */
  async updateUserPermissionTimestamp(userId: string): Promise<boolean> {
    try {
      const userRef = doc(db(), "users", userId)

      await updateDoc(userRef, {
        lastPermissionUpdate: Date.now(),
      })

      return true
    } catch (error) {
      console.error(`Failed to update permission timestamp for user ${userId}:`, error)
      return false
    }
  },
}
