/**
 * Extrait toutes les URLs d'images d'une page HTML
 * @param html Contenu HTML de la page
 * @returns Liste des URLs d'images trouvées
 */
export function extractImagesFromHTML(html: string): string[] {
  const imgRegex = /<img[^>]+src="([^">]+)"/g
  const cssRegex = /url$$['"]?([^'"()]+)['"]?$$/g

  const images: string[] = []
  let match

  // Extraire les images des balises <img>
  while ((match = imgRegex.exec(html)) !== null) {
    if (match[1] && !match[1].startsWith("data:")) {
      images.push(match[1])
    }
  }

  // Extraire les images des CSS (background-image, etc.)
  while ((match = cssRegex.exec(html)) !== null) {
    if (match[1] && !match[1].startsWith("data:")) {
      images.push(match[1])
    }
  }

  return [...new Set(images)] // Éliminer les doublons
}

/**
 * Précharge une liste d'images
 * @param urls Liste des URLs d'images à précharger
 * @returns Promise qui se résout quand toutes les images sont préchargées
 */
export function preloadImages(urls: string[]): Promise<void[]> {
  return Promise.all(
    urls.map(
      (url) =>
        new Promise<void>((resolve) => {
          const img = new Image()
          img.onload = () => resolve()
          img.onerror = () => resolve() // Continuer même en cas d'erreur
          img.src = url
        }),
    ),
  )
}

/**
 * Précharge les images d'une page
 * @param url URL de la page à précharger
 * @returns Promise qui se résout avec la liste des URLs d'images préchargées
 */
export async function preloadImagesFromPage(url: string): Promise<string[]> {
  try {
    const response = await fetch(url)
    const html = await response.text()
    const imageUrls = extractImagesFromHTML(html)
    await preloadImages(imageUrls)
    return imageUrls
  } catch (error) {
    console.error(`Erreur lors du préchargement des images de ${url}:`, error)
    return []
  }
}
