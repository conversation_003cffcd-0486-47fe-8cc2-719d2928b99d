/**
 * Script de persistance de session ultra-robuste
 * Ce script s'exécute avant tout le reste pour garantir la persistance de session
 * et assurer le fonctionnement hors ligne de l'authentification
 */
;(() => {
  // Vérifier si nous sommes dans un navigateur
  if (typeof window === "undefined") return

  // Configuration
  const AUTH_PERSISTENCE_VERSION = "v3" // Mise à jour de la version
  const PING_INTERVAL = 3 * 60 * 1000 // 3 minutes (réduit pour plus de fiabilité)
  const STORAGE_KEYS = {
    USER: `auth_user_${AUTH_PERSISTENCE_VERSION}`,
    TOKEN: `auth_session_${AUTH_PERSISTENCE_VERSION}`,
    TIMESTAMP: `auth_timestamp_${AUTH_PERSISTENCE_VERSION}`,
    UID: "auth_user_uid",
    LAST_ACTIVE: "auth_last_active",
    LAST_TOKEN: "auth_last_token",
    REFRESH_TOKEN: "auth_refresh_token",
    USER_DATA: "auth_user_data",
    SESSION_STATE: "auth_session_state",
  }

  // Fonction pour vérifier si l'application a été fermée de manière inattendue
  function checkForUnexpectedClose() {
    const lastActive = localStorage.getItem(STORAGE_KEYS.LAST_ACTIVE)
    if (lastActive) {
      const now = Date.now()
      const lastActiveTime = Number.parseInt(lastActive, 10)
      const timeDiff = now - lastActiveTime

      // Si la dernière activité date de moins de 24 heures, considérer que l'app a été fermée
      if (timeDiff < 24 * 60 * 60 * 1000) {
        console.log(`Application fermée il y a ${Math.round(timeDiff / 1000 / 60)} minutes`)
        return true
      }
    }
    return false
  }

  // Fonction pour vérifier si IndexedDB est disponible
  function isIndexedDBAvailable() {
    try {
      return typeof window !== "undefined" && typeof window.indexedDB !== "undefined" && window.indexedDB !== null
    } catch (e) {
      return false
    }
  }

  // Fonction pour stocker les données dans IndexedDB
  async function storeInIndexedDB(key, value) {
    if (!isIndexedDBAvailable()) return false

    try {
      // Utiliser une promesse pour gérer l'opération asynchrone
      return new Promise((resolve, reject) => {
        const request = window.indexedDB.open("acrDirectAuth", 1)

        request.onupgradeneeded = (event) => {
          const db = event.target.result
          if (!db.objectStoreNames.contains("authData")) {
            db.createObjectStore("authData", { keyPath: "key" })
          }
        }

        request.onerror = (event) => {
          console.error("Erreur d'ouverture IndexedDB:", event.target.error)
          resolve(false)
        }

        request.onsuccess = (event) => {
          const db = event.target.result
          const transaction = db.transaction(["authData"], "readwrite")
          const store = transaction.objectStore("authData")

          const storeRequest = store.put({
            key: key,
            value: value,
            timestamp: Date.now(),
          })

          storeRequest.onsuccess = () => {
            resolve(true)
          }

          storeRequest.onerror = (event) => {
            console.error("Erreur de stockage IndexedDB:", event.target.error)
            resolve(false)
          }
        }
      })
    } catch (e) {
      console.error("Erreur lors du stockage dans IndexedDB:", e)
      return false
    }
  }

  // Fonction pour maintenir la session active
  async function keepSessionAlive() {
    try {
      // Vérifier si nous avons des données d'authentification
      const uid = localStorage.getItem(STORAGE_KEYS.UID)
      const token = localStorage.getItem(STORAGE_KEYS.LAST_TOKEN)

      if (uid && token) {
        // Mettre à jour le timestamp
        const now = Date.now()
        localStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, now.toString())
        localStorage.setItem(STORAGE_KEYS.TIMESTAMP, now.toString())
        localStorage.setItem(STORAGE_KEYS.SESSION_STATE, "active")

        // Stocker également dans sessionStorage pour une redondance
        try {
          sessionStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, now.toString())
          sessionStorage.setItem(STORAGE_KEYS.TIMESTAMP, now.toString())
          sessionStorage.setItem(STORAGE_KEYS.SESSION_STATE, "active")
        } catch (e) {
          console.error("Erreur sessionStorage:", e)
        }

        // Stocker dans IndexedDB pour une persistance maximale
        await storeInIndexedDB("sessionState", {
          uid: uid,
          token: token,
          timestamp: now,
          state: "active",
        })

        // Envoyer un ping au service worker si disponible
        if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: "KEEP_AUTH_ALIVE",
            uid: uid,
            timestamp: now,
            token: token,
          })
        }

        console.log("Session maintenue active à", new Date().toLocaleTimeString())
      }
    } catch (e) {
      console.error("Erreur lors du maintien de la session:", e)
    }
  }

  // Fonction pour sauvegarder l'état avant fermeture
  async function saveStateBeforeUnload() {
    try {
      // Vérifier si Firebase est disponible
      if (window.firebase && window.firebase.auth && window.firebase.auth().currentUser) {
        const user = window.firebase.auth().currentUser

        // Sauvegarder l'UID
        localStorage.setItem(STORAGE_KEYS.UID, user.uid)

        // Sauvegarder le timestamp
        const now = Date.now()
        localStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, now.toString())
        localStorage.setItem(STORAGE_KEYS.SESSION_STATE, "closing")

        // Stocker également dans sessionStorage pour une redondance
        try {
          sessionStorage.setItem(STORAGE_KEYS.UID, user.uid)
          sessionStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, now.toString())
          sessionStorage.setItem(STORAGE_KEYS.SESSION_STATE, "closing")
        } catch (e) {
          console.error("Erreur sessionStorage:", e)
        }

        // Stocker les données utilisateur de base
        const userData = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          timestamp: now,
        }
        localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData))

        // Stocker dans IndexedDB pour une persistance maximale
        await storeInIndexedDB("userData", userData)

        // Forcer le rafraîchissement du token
        try {
          const token = await user.getIdToken(true)
          localStorage.setItem(STORAGE_KEYS.LAST_TOKEN, token)

          // Stocker également dans sessionStorage
          try {
            sessionStorage.setItem(STORAGE_KEYS.LAST_TOKEN, token)
          } catch (e) {
            console.error("Erreur sessionStorage token:", e)
          }

          // Stocker dans IndexedDB
          await storeInIndexedDB("authToken", { token, timestamp: now })

          console.log("État sauvegardé avant fermeture")
        } catch (error) {
          console.error("Erreur lors du rafraîchissement du token:", error)
        }

        // Envoyer au service worker si disponible
        if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: "SAVE_AUTH_STATE",
            uid: user.uid,
            timestamp: now,
            userData: userData,
          })
        }
      }
    } catch (e) {
      console.error("Erreur lors de la sauvegarde de l'état:", e)
    }
  }

  // Vérifier si l'application a été fermée de manière inattendue
  if (checkForUnexpectedClose()) {
    console.log("Détection de fermeture inattendue, préparation à la restauration de session")
    // Marquer que nous avons détecté une fermeture inattendue
    // L'application utilisera cette information pour tenter une restauration de session
    localStorage.setItem("auth_needs_restore", "true")
  }

  // Configurer un intervalle pour maintenir la session active
  setInterval(keepSessionAlive, PING_INTERVAL)

  // Exécuter immédiatement
  keepSessionAlive()

  // Ajouter un gestionnaire pour l'événement beforeunload
  window.addEventListener("beforeunload", saveStateBeforeUnload)

  // Ajouter un gestionnaire pour l'événement visibilitychange
  document.addEventListener("visibilitychange", () => {
    if (document.visibilityState === "visible") {
      console.log("Application redevenue visible, rafraîchissement de la session")
      keepSessionAlive()
    }
  })

  // Exposer une fonction globale pour forcer le maintien de la session
  window.forceKeepSessionAlive = keepSessionAlive

  // Fonction pour récupérer les données depuis IndexedDB
  async function getFromIndexedDB(key) {
    if (!isIndexedDBAvailable()) return null

    try {
      return new Promise((resolve, reject) => {
        const request = window.indexedDB.open("acrDirectAuth", 1)

        request.onupgradeneeded = (event) => {
          const db = event.target.result
          if (!db.objectStoreNames.contains("authData")) {
            db.createObjectStore("authData", { keyPath: "key" })
          }
        }

        request.onerror = (event) => {
          console.error("Erreur d'ouverture IndexedDB:", event.target.error)
          resolve(null)
        }

        request.onsuccess = (event) => {
          const db = event.target.result
          try {
            const transaction = db.transaction(["authData"], "readonly")
            const store = transaction.objectStore("authData")

            const getRequest = store.get(key)

            getRequest.onsuccess = (event) => {
              if (event.target.result) {
                resolve(event.target.result.value)
              } else {
                resolve(null)
              }
            }

            getRequest.onerror = (event) => {
              console.error("Erreur de récupération IndexedDB:", event.target.error)
              resolve(null)
            }
          } catch (e) {
            console.error("Erreur de transaction IndexedDB:", e)
            resolve(null)
          }
        }
      })
    } catch (e) {
      console.error("Erreur lors de la récupération depuis IndexedDB:", e)
      return null
    }
  }

  // Fonction pour forcer la persistance maximale
  async function forceMaximumPersistence() {
    try {
      // Vérifier si Firebase est disponible
      if (window.firebase && window.firebase.auth && window.firebase.auth().currentUser) {
        const user = window.firebase.auth().currentUser

        // Sauvegarder l'UID
        localStorage.setItem(STORAGE_KEYS.UID, user.uid)

        // Sauvegarder le timestamp
        const now = Date.now()
        localStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, now.toString())
        localStorage.setItem(STORAGE_KEYS.TIMESTAMP, now.toString())
        localStorage.setItem(STORAGE_KEYS.SESSION_STATE, "forced")

        // Stocker les données utilisateur de base
        const userData = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          timestamp: now,
        }
        localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData))

        // Stocker dans IndexedDB
        await storeInIndexedDB("userData", userData)
        await storeInIndexedDB("sessionState", {
          uid: user.uid,
          timestamp: now,
          state: "forced",
        })

        try {
          // Forcer le rafraîchissement du token
          const token = await user.getIdToken(true)
          localStorage.setItem(STORAGE_KEYS.LAST_TOKEN, token)

          // Stocker également dans sessionStorage pour une redondance
          try {
            sessionStorage.setItem(STORAGE_KEYS.LAST_TOKEN, token)
            sessionStorage.setItem(STORAGE_KEYS.UID, user.uid)
            sessionStorage.setItem(STORAGE_KEYS.TIMESTAMP, now.toString())
            sessionStorage.setItem(STORAGE_KEYS.SESSION_STATE, "forced")
            sessionStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData))
          } catch (e) {
            console.error("Erreur sessionStorage:", e)
          }

          // Stocker le token dans IndexedDB
          await storeInIndexedDB("authToken", { token, timestamp: now })

          // Envoyer au service worker si disponible
          if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: "FORCE_AUTH_PERSISTENCE",
              uid: user.uid,
              token: token,
              timestamp: now,
              userData: userData,
            })
          }

          console.log("Persistance maximale forcée avec succès")
          return true
        } catch (error) {
          console.error("Erreur lors du rafraîchissement du token:", error)
          return false
        }
      } else {
        console.warn("Aucun utilisateur connecté, impossible de forcer la persistance")
        return false
      }
    } catch (e) {
      console.error("Erreur lors du forçage de la persistance maximale:", e)
      return false
    }
  }

  // Fonction pour tenter de restaurer la session depuis le stockage local
  async function attemptSessionRestore() {
    try {
      console.log("Tentative de restauration de session depuis le stockage local...")

      // Vérifier si nous avons des données d'authentification en localStorage
      const uid = localStorage.getItem(STORAGE_KEYS.UID)
      const token = localStorage.getItem(STORAGE_KEYS.LAST_TOKEN)
      const userDataStr = localStorage.getItem(STORAGE_KEYS.USER_DATA)

      if (!uid || !token) {
        console.log("Aucune donnée d'authentification en localStorage")

        // Essayer de récupérer depuis IndexedDB
        if (isIndexedDBAvailable()) {
          console.log("Tentative de récupération depuis IndexedDB...")
          const sessionState = await getFromIndexedDB("sessionState")
          const authToken = await getFromIndexedDB("authToken")
          const userData = await getFromIndexedDB("userData")

          if (sessionState && authToken && userData) {
            console.log("Données d'authentification trouvées dans IndexedDB")

            // Restaurer dans localStorage
            localStorage.setItem(STORAGE_KEYS.UID, userData.uid)
            localStorage.setItem(STORAGE_KEYS.LAST_TOKEN, authToken.token)
            localStorage.setItem(STORAGE_KEYS.LAST_ACTIVE, Date.now().toString())
            localStorage.setItem(STORAGE_KEYS.SESSION_STATE, "restored")
            localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData))

            // Informer le service worker
            if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
              navigator.serviceWorker.controller.postMessage({
                type: "RESTORE_AUTH_STATE",
                uid: userData.uid,
                token: authToken.token,
                timestamp: Date.now(),
                userData: userData,
              })
            }

            console.log("Session restaurée depuis IndexedDB")
            return {
              uid: userData.uid,
              token: authToken.token,
              userData: userData,
            }
          }
        }

        return null
      }

      // Restaurer les données utilisateur
      let userData = null
      if (userDataStr) {
        try {
          userData = JSON.parse(userDataStr)
        } catch (e) {
          console.error("Erreur lors du parsing des données utilisateur:", e)
        }
      }

      // Informer le service worker
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: "RESTORE_AUTH_STATE",
          uid: uid,
          token: token,
          timestamp: Date.now(),
          userData: userData,
        })
      }

      console.log("Session restaurée depuis localStorage")
      return {
        uid: uid,
        token: token,
        userData: userData,
      }
    } catch (e) {
      console.error("Erreur lors de la tentative de restauration de session:", e)
      return null
    }
  }

  // Exposer les fonctions globalement
  window.forceMaximumPersistence = forceMaximumPersistence
  window.attemptSessionRestore = attemptSessionRestore
  window.storeInIndexedDB = storeInIndexedDB
  window.getFromIndexedDB = getFromIndexedDB

  // Vérifier si nous devons restaurer la session
  if (localStorage.getItem("auth_needs_restore") === "true") {
    console.log("Restauration de session nécessaire, tentative automatique...")
    localStorage.removeItem("auth_needs_restore")

    // Attendre un peu pour s'assurer que Firebase est initialisé
    setTimeout(() => {
      attemptSessionRestore()
        .then((result) => {
          if (result) {
            console.log("Restauration automatique réussie")
          } else {
            console.log("Échec de la restauration automatique")
          }
        })
        .catch((error) => {
          console.error("Erreur lors de la restauration automatique:", error)
        })
    }, 1000)
  }

  console.log("Script de persistance ultra-robuste initialisé (v3)")
})()
