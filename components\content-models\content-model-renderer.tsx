"use client"
import type { ContentModelType } from "./content-type-selector"
import { SingleImageViewer } from "./single-image-viewer"
import { GalleryViewer } from "./gallery-viewer"
import { CarouselViewer } from "./carousel-viewer"
import { GridViewer } from "./grid-viewer"

interface ContentModelRendererProps {
  contentType: ContentModelType
  content: any
  richTextContent?: string
}

export function ContentModelRenderer({ contentType, content, richTextContent }: ContentModelRendererProps) {
  // Render the appropriate component based on contentType
  switch (contentType) {
    case "single-image":
      return <SingleImageViewer imageUrl={content.imageUrl} caption={content.caption} altText={content.altText} />
    case "gallery":
      return <GalleryViewer images={content.images} columns={content.columns} />
    case "carousel":
      return <CarouselViewer slides={content.slides} autoplay={content.autoplay} interval={content.interval} />
    case "grid":
      return <GridViewer items={content.items} />
    case "richtext":
    default:
      // For richtext or fallback, render the HTML content
      return (
        <div
          className="prose max-w-none prose-sm sm:prose dark:prose-invert"
          dangerouslySetInnerHTML={{ __html: richTextContent || "" }}
        />
      )
  }
}
