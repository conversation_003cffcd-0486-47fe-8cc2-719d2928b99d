"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import { doc, updateDoc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { User, Building } from "lucide-react"
import { updatePassword, reauthenticateWithCredential, EmailAuthProvider } from "firebase/auth"
import { Eye, EyeOff, Shield } from "lucide-react"

// Modifier l'interface UserProfile pour ajouter firstName et lastName
interface UserProfile {
  firstName: string
  lastName: string
  displayName: string
  email: string
  phoneNumber?: string
  company?: string
  position?: string
  address?: string
  city?: string
  postalCode?: string
}

export default function ProfilePage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  // Mettre à jour l'état initial pour inclure firstName et lastName
  const [profile, setProfile] = useState<UserProfile>({
    firstName: "",
    lastName: "",
    displayName: "",
    email: "",
    phoneNumber: "",
    company: "",
    position: "",
    address: "",
    city: "",
    postalCode: "",
  })
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  useEffect(() => {
    // Mettre à jour la fonction fetchUserProfile pour extraire firstName et lastName
    async function fetchUserProfile() {
      if (!user) return

      setIsLoading(true)
      try {
        const userDoc = await getDoc(doc(db(), "users", user.uid))
        if (userDoc.exists()) {
          const userData = userDoc.data()
          setProfile({
            firstName: userData.firstName || "",
            lastName: userData.lastName || "",
            displayName: userData.displayName || "",
            email: userData.email || "",
            phoneNumber: userData.phoneNumber || "",
            company: userData.company || "",
            position: userData.position || "",
            address: userData.address || "",
            city: userData.city || "",
            postalCode: userData.postalCode || "",
          })
        }
      } catch (error) {
        console.error("Error fetching user profile:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger votre profil. Veuillez réessayer plus tard.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
  }, [user, toast])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setProfile((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  // Mettre à jour la fonction handleSubmit pour combiner firstName et lastName en displayName
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Générer le displayName à partir du prénom et du nom
    const displayName = `${profile.firstName} ${profile.lastName}`.trim()

    setIsSaving(true)
    try {
      await updateDoc(doc(db(), "users", user.uid), {
        firstName: profile.firstName,
        lastName: profile.lastName,
        displayName: displayName,
        phoneNumber: profile.phoneNumber,
        company: profile.company,
        position: profile.position,
        address: profile.address,
        city: profile.city,
        postalCode: profile.postalCode,
        updatedAt: new Date().toISOString(),
      })

      // Mettre à jour le displayName dans l'état local
      setProfile((prev) => ({
        ...prev,
        displayName,
      }))

      toast({
        title: "Profil mis à jour",
        description: "Vos informations ont été enregistrées avec succès.",
      })
    } catch (error) {
      console.error("Error updating user profile:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour votre profil. Veuillez réessayer plus tard.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // Clear errors when user types
    setPasswordErrors((prev) => ({
      ...prev,
      [name]: "",
    }))
  }

  const validatePassword = () => {
    const errors = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }

    if (!passwordData.currentPassword) {
      errors.currentPassword = "Le mot de passe actuel est requis"
    }

    if (!passwordData.newPassword) {
      errors.newPassword = "Le nouveau mot de passe est requis"
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = "Le mot de passe doit contenir au moins 6 caractères"
    }

    if (!passwordData.confirmPassword) {
      errors.confirmPassword = "Veuillez confirmer le nouveau mot de passe"
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = "Les mots de passe ne correspondent pas"
    }

    setPasswordErrors(errors)
    return !errors.currentPassword && !errors.newPassword && !errors.confirmPassword
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validatePassword() || !user?.email) return

    setIsSaving(true)
    try {
      // Re-authenticate the user
      const credential = EmailAuthProvider.credential(user.email, passwordData.currentPassword)

      await reauthenticateWithCredential(user, credential)

      // Update password
      await updatePassword(user, passwordData.newPassword)

      // Clear form
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      })

      toast({
        title: "Mot de passe mis à jour",
        description: "Votre mot de passe a été modifié avec succès.",
      })
    } catch (error: any) {
      console.error("Error updating password:", error)

      if (error.code === "auth/wrong-password") {
        setPasswordErrors({
          ...passwordErrors,
          currentPassword: "Mot de passe incorrect",
        })
      } else if (error.code === "auth/weak-password") {
        setPasswordErrors({
          ...passwordErrors,
          newPassword: "Le mot de passe est trop faible",
        })
      } else {
        toast({
          title: "Erreur",
          description: "Impossible de mettre à jour le mot de passe. Veuillez réessayer.",
          variant: "destructive",
        })
      }
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-3 border-b pb-4 mb-6">
          <User className="h-6 w-6 text-primary" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-32 mb-2" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 border-b pb-4 mb-6">
        <User className="h-6 w-6 text-primary" />
        <div>
          <h2 className="text-xl sm:text-2xl font-bold tracking-tight">Mon profil</h2>
          <p className="text-sm sm:text-base text-muted-foreground">Gérez vos informations personnelles</p>
        </div>
      </div>

      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="mb-4 flex flex-wrap w-full h-auto sm:h-10 sm:flex-nowrap">
          <TabsTrigger value="personal" className="flex items-center gap-2 flex-1 h-10">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Informations personnelles</span>
            <span className="sm:hidden">Personnel</span>
          </TabsTrigger>
          <TabsTrigger value="professional" className="flex items-center gap-2 flex-1 h-10">
            <Building className="h-4 w-4" />
            <span className="hidden sm:inline">Informations professionnelles</span>
            <span className="sm:hidden">Professionnel</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2 flex-1 h-10">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Sécurité</span>
            <span className="sm:hidden">Sécurité</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Informations personnelles</CardTitle>
              <CardDescription className="text-sm">
                Mettez à jour vos informations personnelles. Ces informations seront visibles par l'équipe ACR Direct.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                {/* Remplacer le champ displayName par firstName et lastName dans le formulaire */}
                {/* Remplacer cette section dans le TabsContent "personal": */}
                {/* <div className="space-y-2">
                  <Label htmlFor="displayName">Nom complet</Label>
                  <Input
                    id="displayName"
                    name="displayName"
                    value={profile.displayName}
                    onChange={handleChange}
                    placeholder="Votre nom complet"
                  />
                </div> */}

                {/* Par ces deux champs: */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Prénom</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={profile.firstName}
                      onChange={handleChange}
                      placeholder="Votre prénom"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nom</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={profile.lastName}
                      onChange={handleChange}
                      placeholder="Votre nom"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" name="email" value={profile.email} disabled placeholder="<EMAIL>" />
                  <p className="text-sm text-muted-foreground">
                    L'adresse email ne peut pas être modifiée. Contactez l'administrateur pour tout changement.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Téléphone</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profile.phoneNumber}
                    onChange={handleChange}
                    placeholder="Votre numéro de téléphone"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Enregistrement..." : "Enregistrer les modifications"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="professional">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Informations professionnelles</CardTitle>
              <CardDescription className="text-sm">
                Mettez à jour vos informations professionnelles pour faciliter les échanges avec ACR Direct.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company">Entreprise</Label>
                  <Input
                    id="company"
                    name="company"
                    value={profile.company}
                    onChange={handleChange}
                    placeholder="Nom de votre entreprise"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">Poste</Label>
                  <Input
                    id="position"
                    name="position"
                    value={profile.position}
                    onChange={handleChange}
                    placeholder="Votre poste dans l'entreprise"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Adresse</Label>
                  <Input
                    id="address"
                    name="address"
                    value={profile.address}
                    onChange={handleChange}
                    placeholder="Adresse de l'entreprise"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">Ville</Label>
                    <Input id="city" name="city" value={profile.city} onChange={handleChange} placeholder="Ville" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Code postal</Label>
                    <Input
                      id="postalCode"
                      name="postalCode"
                      value={profile.postalCode}
                      onChange={handleChange}
                      placeholder="Code postal"
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Enregistrement..." : "Enregistrer les modifications"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Sécurité du compte</CardTitle>
              <CardDescription className="text-sm">
                Gérez la sécurité de votre compte et modifiez votre mot de passe.
              </CardDescription>
            </CardHeader>
            <form onSubmit={handlePasswordSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Mot de passe actuel</Label>
                  <div className="relative">
                    <Input
                      id="currentPassword"
                      name="currentPassword"
                      type={showCurrentPassword ? "text" : "password"}
                      value={passwordData.currentPassword}
                      onChange={handlePasswordChange}
                      placeholder="Entrez votre mot de passe actuel"
                      className={passwordErrors.currentPassword ? "border-red-500" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.currentPassword && (
                    <p className="text-sm text-red-500">{passwordErrors.currentPassword}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">Nouveau mot de passe</Label>
                  <div className="relative">
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type={showNewPassword ? "text" : "password"}
                      value={passwordData.newPassword}
                      onChange={handlePasswordChange}
                      placeholder="Entrez votre nouveau mot de passe"
                      className={passwordErrors.newPassword ? "border-red-500" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.newPassword && <p className="text-sm text-red-500">{passwordErrors.newPassword}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirmer le nouveau mot de passe</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordChange}
                      placeholder="Confirmez votre nouveau mot de passe"
                      className={passwordErrors.confirmPassword ? "border-red-500" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.confirmPassword && (
                    <p className="text-sm text-red-500">{passwordErrors.confirmPassword}</p>
                  )}
                </div>

                <div className="rounded-lg bg-muted p-4">
                  <p className="text-sm text-muted-foreground">
                    <strong>Conseils pour un mot de passe sécurisé :</strong>
                  </p>
                  <ul className="mt-2 space-y-1 text-sm text-muted-foreground list-disc list-inside">
                    <li>Utilisez au moins 6 caractères</li>
                    <li>Mélangez lettres majuscules et minuscules</li>
                    <li>Incluez des chiffres et des caractères spéciaux</li>
                    <li>Évitez les mots du dictionnaire ou les informations personnelles</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? "Mise à jour..." : "Changer le mot de passe"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
