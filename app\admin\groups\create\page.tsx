"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { db } from "@/lib/firebase"
import { collection, addDoc, serverTimestamp, getDocs, query, where } from "firebase/firestore"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"

export default function CreateGroupPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log("handleSubmit called") // Add this line

    if (!name) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)
      console.log("setIsLoading(true) called") // Add this line

      // Check if group name already exists
      const nameQuery = query(collection(db, "groups"), where("name", "==", name))
      const nameSnapshot = await getDocs(nameQuery)
      console.log("nameSnapshot", nameSnapshot) // Add this line

      if (!nameSnapshot.empty) {
        toast({
          title: "Erreur",
          description: "Un groupe avec ce nom existe déjà",
          variant: "destructive",
        })
        return
      }

      // Prepare group data
      const groupData = {
        name,
        description,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }
      console.log("groupData", groupData) // Add this line

      // Add document to Firestore
      await addDoc(collection(db, "groups"), groupData)
      console.log("addDoc called") // Add this line

      toast({
        title: "Succès",
        description: "Le groupe a été créé avec succès",
      })

      router.push("/admin/groups")
    } catch (error) {
      console.error("Erreur lors de la création du groupe:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du groupe",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      console.log("setIsLoading(false) called") // Add this line
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Créer un nouveau groupe</h1>

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Informations du groupe</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nom *</Label>
              <Input id="name" value={name} onChange={(e) => setName(e.target.value)} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.push("/admin/groups")} disabled={isLoading}>
            Annuler
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Créer le groupe
          </Button>
        </div>
      </form>
    </div>
  )
}
