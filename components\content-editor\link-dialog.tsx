"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface LinkDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (url: string) => void
  initialUrl?: string
}

export function LinkDialog({ isOpen, onClose, onConfirm, initialUrl = "" }: LinkDialogProps) {
  const [url, setUrl] = useState(initialUrl)

  useEffect(() => {
    if (isOpen) {
      setUrl(initialUrl)
    }
  }, [isOpen, initialUrl])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onConfirm(url)
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Insérer un lien</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                placeholder="https://exemple.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button type="submit" disabled={!url.trim()}>
              Confirmer
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
