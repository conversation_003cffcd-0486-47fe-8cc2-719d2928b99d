/**
 * Service de gestion du cycle de vie du cache
 * Ce service gère le cycle de vie du cache, notamment la mise à jour progressive,
 * l'expiration et la purge des données obsolètes
 */

import { cacheService, CACHE_DURATIONS, type CacheStoreType } from "@/lib/cache-service"
import localforage from "localforage"

// Configuration du store pour les métadonnées du cache
const cacheMetaStore = localforage.createInstance({
  name: "acrDirect",
  storeName: "cacheMetadata",
  description: "Métadonnées du cache",
})

// Interface pour les métadonnées du cache
export interface CacheMetadata {
  /** Dernière mise à jour complète du cache */
  lastFullUpdate: number
  /** Dernières mises à jour par type de cache */
  lastUpdates: Record<CacheStoreType, number>
  /** Versions des données par collection */
  versions: Record<string, string>
  /** Statistiques d'utilisation du cache */
  stats: {
    hits: number
    misses: number
    lastReset: number
  }
}

// Classe pour la gestion du cycle de vie du cache
class CacheLifecycleService {
  private initialized = false
  private initPromise: Promise<void> | null = null
  private metadata: CacheMetadata | null = null
  private updateInterval: number = 30 * 60 * 1000 // 30 minutes
  private intervalId: NodeJS.Timeout | null = null

  constructor() {
    this.initPromise = this.initialize()
  }

  /**
   * Initialise le service de gestion du cycle de vie du cache
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Vérifier si nous sommes dans un environnement navigateur
      if (typeof window === "undefined") {
        console.warn("CacheLifecycleService: Tentative d'initialisation côté serveur, opération ignorée")
        this.initialized = true
        return
      }

      // Récupérer les métadonnées du cache
      this.metadata = await this.getMetadata()

      // Démarrer la vérification périodique du cache
      this.startPeriodicCheck()

      this.initialized = true
      console.log("CacheLifecycleService: Initialisation réussie")
    } catch (error) {
      console.error("CacheLifecycleService: Erreur lors de l'initialisation", error)
      // Marquer comme initialisé même en cas d'erreur pour éviter les tentatives répétées
      this.initialized = true
    }
  }

  /**
   * S'assure que le service est initialisé avant d'exécuter une opération
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      if (this.initPromise) {
        await this.initPromise
      } else {
        await this.initialize()
      }
    }
  }

  /**
   * Récupère les métadonnées du cache
   */
  private async getMetadata(): Promise<CacheMetadata> {
    const metadata = await cacheMetaStore.getItem<CacheMetadata>("metadata")

    if (metadata) {
      return metadata
    }

    // Créer des métadonnées par défaut
    const defaultMetadata: CacheMetadata = {
      lastFullUpdate: 0,
      lastUpdates: {
        firestore: 0,
        auth: 0,
        ui: 0,
        api: 0,
        images: 0,
        pages: 0,
      },
      versions: {},
      stats: {
        hits: 0,
        misses: 0,
        lastReset: Date.now(),
      },
    }

    await cacheMetaStore.setItem("metadata", defaultMetadata)
    return defaultMetadata
  }

  /**
   * Enregistre les métadonnées du cache
   */
  private async saveMetadata(): Promise<void> {
    if (!this.metadata) return

    await cacheMetaStore.setItem("metadata", this.metadata)
  }

  /**
   * Démarre la vérification périodique du cache
   */
  private startPeriodicCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }

    this.intervalId = setInterval(() => {
      this.checkCacheExpiration()
    }, this.updateInterval)
  }

  /**
   * Arrête la vérification périodique du cache
   */
  private stopPeriodicCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  /**
   * Vérifie l'expiration du cache et purge les données obsolètes
   */
  async checkCacheExpiration(): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    try {
      console.log("CacheLifecycleService: Vérification de l'expiration du cache...")

      // Vérifier si une mise à jour complète est nécessaire
      const now = Date.now()
      const fullUpdateAge = now - this.metadata.lastFullUpdate

      if (fullUpdateAge > CACHE_DURATIONS.VERY_LONG) {
        console.log("CacheLifecycleService: Mise à jour complète du cache nécessaire")

        // Purger les données très anciennes (plus de 30 jours)
        await this.purgeOldData()

        // Mettre à jour les métadonnées
        this.metadata.lastFullUpdate = now
        await this.saveMetadata()
      }

      // Réinitialiser les statistiques si nécessaires (tous les 7 jours)
      const statsAge = now - this.metadata.stats.lastReset
      if (statsAge > 7 * 24 * 60 * 60 * 1000) {
        this.metadata.stats = {
          hits: 0,
          misses: 0,
          lastReset: now,
        }
        await this.saveMetadata()
      }
    } catch (error) {
      console.error("CacheLifecycleService: Erreur lors de la vérification de l'expiration du cache", error)
    }
  }

  /**
   * Purge les données anciennes du cache
   */
  async purgeOldData(): Promise<void> {
    await this.ensureInitialized()

    try {
      console.log("CacheLifecycleService: Purge des données anciennes...")

      // Récupérer les statistiques du cache
      const stats = await cacheService.getStats()

      // Parcourir chaque store et purger les données anciennes
      for (const [storeType, storeStats] of Object.entries(stats)) {
        if (storeStats.count > 0) {
          console.log(`CacheLifecycleService: Purge des données anciennes du store ${storeType}...`)

          // Purger les données anciennes du store
          await this.purgeStoreOldData(storeType as CacheStoreType)
        }
      }
    } catch (error) {
      console.error("CacheLifecycleService: Erreur lors de la purge des données anciennes", error)
    }
  }

  /**
   * Purge les données anciennes d'un store spécifique
   * @param storeType Type de store à purger
   */
  private async purgeStoreOldData(storeType: CacheStoreType): Promise<void> {
    // Cette méthode doit être implémentée en fonction des besoins spécifiques
    // Elle peut utiliser différentes stratégies de purge selon le type de store

    // Exemple : purger les données plus anciennes que CACHE_DURATIONS.PERMANENT
    const now = Date.now()
    const maxAge = CACHE_DURATIONS.PERMANENT

    // Utiliser localforage directement pour accéder au store
    const store = localforage.createInstance({
      name: "acrDirect",
      storeName: `${storeType}Cache`,
    })

    // Parcourir toutes les entrées du store
    await store.iterate((value: any, key) => {
      if (value && value.timestamp) {
        const age = now - value.timestamp
        if (age > maxAge) {
          // Supprimer l'entrée si elle est trop ancienne
          store.removeItem(key)
        }
      }
    })
  }

  /**
   * Enregistre un hit de cache
   */
  async recordCacheHit(): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    this.metadata.stats.hits++

    // Sauvegarder les métadonnées toutes les 10 hits pour éviter trop d'écritures
    if (this.metadata.stats.hits % 10 === 0) {
      await this.saveMetadata()
    }
  }

  /**
   * Enregistre un miss de cache
   */
  async recordCacheMiss(): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    this.metadata.stats.misses++

    // Sauvegarder les métadonnées toutes les 10 misses pour éviter trop d'écritures
    if (this.metadata.stats.misses % 10 === 0) {
      await this.saveMetadata()
    }
  }

  /**
   * Met à jour la version d'une collection
   * @param collection Nom de la collection
   * @param version Nouvelle version
   */
  async updateCollectionVersion(collection: string, version: string): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    this.metadata.versions[collection] = version
    await this.saveMetadata()
  }

  /**
   * Récupère la version d'une collection
   * @param collection Nom de la collection
   * @returns Version de la collection ou null si non trouvée
   */
  async getCollectionVersion(collection: string): Promise<string | null> {
    await this.ensureInitialized()

    if (!this.metadata) return null

    return this.metadata.versions[collection] || null
  }

  /**
   * Enregistre la dernière mise à jour d'un type de cache
   * @param storeType Type de store mis à jour
   */
  async recordUpdate(storeType: CacheStoreType): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    this.metadata.lastUpdates[storeType] = Date.now()
    await this.saveMetadata()
  }

  /**
   * Récupère les statistiques du cache
   * @returns Statistiques du cache
   */
  async getStats(): Promise<{
    hits: number
    misses: number
    hitRatio: number
    lastReset: Date
    lastFullUpdate: Date
    lastUpdates: Record<CacheStoreType, Date>
  }> {
    await this.ensureInitialized()

    if (!this.metadata) {
      return {
        hits: 0,
        misses: 0,
        hitRatio: 0,
        lastReset: new Date(0),
        lastFullUpdate: new Date(0),
        lastUpdates: {
          firestore: new Date(0),
          auth: new Date(0),
          ui: new Date(0),
          api: new Date(0),
          images: new Date(0),
          pages: new Date(0),
        },
      }
    }

    const total = this.metadata.stats.hits + this.metadata.stats.misses
    const hitRatio = total > 0 ? this.metadata.stats.hits / total : 0

    return {
      hits: this.metadata.stats.hits,
      misses: this.metadata.stats.misses,
      hitRatio,
      lastReset: new Date(this.metadata.stats.lastReset),
      lastFullUpdate: new Date(this.metadata.lastFullUpdate),
      lastUpdates: {
        firestore: new Date(this.metadata.lastUpdates.firestore),
        auth: new Date(this.metadata.lastUpdates.auth),
        ui: new Date(this.metadata.lastUpdates.ui),
        api: new Date(this.metadata.lastUpdates.api),
        images: new Date(this.metadata.lastUpdates.images),
        pages: new Date(this.metadata.lastUpdates.pages),
      },
    }
  }

  /**
   * Réinitialise les statistiques du cache
   */
  async resetStats(): Promise<void> {
    await this.ensureInitialized()

    if (!this.metadata) return

    this.metadata.stats = {
      hits: 0,
      misses: 0,
      lastReset: Date.now(),
    }

    await this.saveMetadata()
  }

  /**
   * Nettoie les ressources lors de la destruction du service
   */
  cleanup(): void {
    this.stopPeriodicCheck()
  }
}

// Exporter une instance singleton du service
export const cacheLifecycle = new CacheLifecycleService()
