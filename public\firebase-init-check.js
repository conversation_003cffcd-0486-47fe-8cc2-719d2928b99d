// Script pour vérifier l'initialisation correcte de Firebase
;(() => {
  if (typeof window !== "undefined") {
    // Vérifier si Firebase est correctement initialisé
    window.addEventListener("load", () => {
      setTimeout(() => {
        try {
          // Vérifier si l'objet auth existe dans window
          if (window.firebase && window.firebase.auth) {
            console.log("Firebase Auth est correctement initialisé")
          } else {
            console.log("Firebase Auth n'est pas disponible globalement")
          }

          // Vérifier si l'utilisateur est connecté
          const checkAuthStatus = () => {
            if (localStorage.getItem("auth_user")) {
              console.log("Utilisateur trouvé dans le stockage local")
            } else {
              console.log("Aucun utilisateur trouvé dans le stockage local")
            }
          }

          checkAuthStatus()
        } catch (e) {
          console.error("Erreur lors de la vérification de Firebase:", e)
        }
      }, 2000) // Attendre 2 secondes pour s'assurer que tout est chargé
    })
  }
})()
