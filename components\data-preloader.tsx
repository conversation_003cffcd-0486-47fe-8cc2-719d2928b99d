/**
 * Composant de préchargement des données
 * Ce composant précharge les données en arrière-plan pour accélérer l'affichage
 */

"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { preloadAllData, preloadEssentialData } from "@/lib/preload-service"
import { getUserGroups } from "@/lib/user-utils"
import { cacheService } from "@/lib/cache-service"

interface DataPreloaderProps {
  children: React.ReactNode
  mode?: "essential" | "complete"
}

export function DataPreloader({ children, mode = "essential" }: DataPreloaderProps) {
  const { user } = useAuth()
  const [isPreloaded, setIsPreloaded] = useState(false)

  useEffect(() => {
    // Définir l'ID utilisateur dans le service de cache
    if (user) {
      cacheService.setUserId(user.uid)
    } else {
      cacheService.setUserId(null)
    }

    // Fonction de préchargement
    const preloadData = async () => {
      if (!user) return

      try {
        // Marquer le début du préchargement
        const preloadKey = `preload_started_${user.uid}`
        const lastPreload = localStorage.getItem(preloadKey)
        const now = Date.now()

        // Si un préchargement a été fait récemment (moins de 5 minutes), ne pas le refaire
        if (lastPreload && now - Number.parseInt(lastPreload) < 5 * 60 * 1000) {
          console.log("Préchargement récent détecté, utilisation du cache existant")
          setIsPreloaded(true)
          return
        }

        // Marquer le début du préchargement
        localStorage.setItem(preloadKey, now.toString())

        // Récupérer les groupes de l'utilisateur
        const userGroups = await getUserGroups(user.uid)

        // Précharger les données selon le mode
        if (mode === "essential") {
          await preloadEssentialData(user.uid)
        } else {
          await preloadAllData(user.uid, userGroups)
        }

        // Marquer comme préchargé
        setIsPreloaded(true)
      } catch (error) {
        console.error("Erreur lors du préchargement des données:", error)
        // Même en cas d'erreur, on considère que c'est préchargé pour ne pas bloquer l'UI
        setIsPreloaded(true)
      }
    }

    // Lancer le préchargement
    preloadData()
  }, [user, mode])

  // Toujours rendre les enfants, le préchargement se fait en arrière-plan
  return <>{children}</>
}
