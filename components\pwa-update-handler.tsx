"use client"

import { useEffect, useState } from "react"
import { updateServiceWorker, checkForUpdates } from "@/app/sw-register"
import { Button } from "@/components/ui/button"
import { AlertCircle, RefreshCw } from "lucide-react"

export function PWAUpdateHandler() {
  const [updateAvailable, setUpdateAvailable] = useState(false)

  useEffect(() => {
    // Vérifier si une mise à jour est déjà disponible
    if (typeof window !== "undefined" && window.swUpdateReady) {
      setUpdateAvailable(true)
    }

    // Écouter les événements de mise à jour
    const handleUpdateFound = () => {
      setUpdateAvailable(true)
    }

    window.addEventListener("swUpdateReady", handleUpdateFound)

    // Vérifier périodiquement les mises à jour
    const checkInterval = setInterval(
      () => {
        checkForUpdates()
      },
      60 * 60 * 1000,
    ) // Vérifier toutes les heures

    return () => {
      window.removeEventListener("swUpdateReady", handleUpdateFound)
      clearInterval(checkInterval)
    }
  }, [])

  const handleUpdate = () => {
    updateServiceWorker()
  }

  if (!updateAvailable) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-blue-500" />
        </div>
        <div className="ml-3 w-0 flex-1">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Mise à jour disponible</p>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Une nouvelle version de l'application est disponible. Veuillez mettre à jour pour bénéficier des dernières
            fonctionnalités.
          </p>
          <div className="mt-4">
            <Button onClick={handleUpdate} className="inline-flex items-center" variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Mettre à jour maintenant
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
