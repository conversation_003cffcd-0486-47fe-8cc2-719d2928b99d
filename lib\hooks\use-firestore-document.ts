"use client"

import { useState, useEffect } from "react"
import { doc, getDoc, onSnapshot, type DocumentData } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { startMeasure, endMeasure, reportPerformance } from "@/lib/performance"

interface UseFirestoreDocumentOptions {
  listen?: boolean // Whether to listen for real-time updates
}

interface UseFirestoreDocumentResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refresh: () => Promise<void>
}

export function useFirestoreDocument<T = DocumentData>(
  path: string,
  id: string,
  options: UseFirestoreDocumentOptions = {},
): UseFirestoreDocumentResult<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)

  const { listen = false } = options

  // Function to fetch the document
  const fetchDocument = async () => {
    if (!path || !id) {
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const measureId = `fetch-document-${path}-${id}`
      startMeasure(measureId)

      const docRef = doc(db, path, id)
      const docSnap = await getDoc(docRef)

      const duration = endMeasure(measureId)
      if (duration) reportPerformance(measureId, duration)

      if (docSnap.exists()) {
        setData({ id: docSnap.id, ...docSnap.data() } as T)
      } else {
        setData(null)
      }
    } catch (err) {
      console.error(`Error fetching document ${path}/${id}:`, err)
      setError(err instanceof Error ? err : new Error(String(err)))
    } finally {
      setLoading(false)
    }
  }

  // Effect for initial fetch and real-time updates
  useEffect(() => {
    if (!path || !id) {
      setLoading(false)
      return
    }

    setLoading(true)

    if (listen) {
      // Set up real-time listener
      const docRef = doc(db, path, id)
      const unsubscribe = onSnapshot(
        docRef,
        (docSnap) => {
          if (docSnap.exists()) {
            setData({ id: docSnap.id, ...docSnap.data() } as T)
          } else {
            setData(null)
          }
          setLoading(false)
        },
        (err) => {
          console.error(`Error in document listener ${path}/${id}:`, err)
          setError(err)
          setLoading(false)
        },
      )

      // Clean up listener
      return () => unsubscribe()
    } else {
      // Just fetch once
      fetchDocument()
      return () => {}
    }
  }, [path, id, listen])

  return {
    data,
    loading,
    error,
    refresh: fetchDocument,
  }
}
