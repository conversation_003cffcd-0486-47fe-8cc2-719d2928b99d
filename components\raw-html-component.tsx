"use client"

import type React from "react"

import { Node<PERSON><PERSON>w<PERSON><PERSON><PERSON>, <PERSON>deViewContent } from "@tiptap/react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Code } from "lucide-react"

export const RawHTMLComponent = ({ node, updateAttributes, editor }) => {
  const [htmlContent, setHtmlContent] = useState(node.attrs.htmlContent || "")

  const handleChange = (e) => {
    setHtmlContent(e.target.value)
    updateAttributes({ htmlContent: e.target.value })
  }

  // Modifier la fonction handleSave pour explicitement accepter un événement de clic
  // et appeler preventDefault pour empêcher tout comportement par défaut
  const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    updateAttributes({ htmlContent })
  }

  return (
    <NodeViewWrapper className="raw-html-block border rounded-md my-4">
      <div className="bg-blue-50 p-2 flex items-center gap-2 border-b">
        <Code className="h-4 w-4 text-blue-700" />
        <span className="text-sm font-medium text-blue-700">HTML Brut</span>
      </div>

      <Tabs defaultValue="edit" className="p-2">
        <TabsList className="mb-2">
          <TabsTrigger value="edit">Éditer</TabsTrigger>
          <TabsTrigger value="preview">Aperçu</TabsTrigger>
        </TabsList>

        <TabsContent value="edit">
          <Textarea
            value={htmlContent}
            onChange={handleChange}
            className="font-mono text-sm"
            rows={5}
            placeholder="<div>Insérez votre HTML ici...</div>"
          />
          <div className="flex justify-end mt-2">
            <Button type="button" size="sm" onClick={handleSave} className="bg-blue-600 hover:bg-blue-700 text-white">
              Appliquer
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="preview" className="border p-3 rounded-md min-h-[100px]">
          <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
        </TabsContent>
      </Tabs>

      <NodeViewContent className="hidden" />
    </NodeViewWrapper>
  )
}
