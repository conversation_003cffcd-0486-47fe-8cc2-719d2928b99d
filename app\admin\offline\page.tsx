"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { OfflineTester } from "@/components/offline-tester"
import { OfflinePreloader } from "@/components/offline-preloader"
import { CacheStatus } from "@/components/cache-status"
import { WifiOff, Database, Download } from "lucide-react"

export default function OfflinePage() {
  const [activeTab, setActiveTab] = useState("tester")

  return (
    <div className="container py-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Mode Hors Ligne</h1>
        <p className="text-muted-foreground">
          Outils pour tester, configurer et gérer le mode hors ligne de l'application
        </p>
      </div>

      <Tabs defaultValue="tester" value={activeTab} onValue<PERSON>hange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
          <TabsTrigger value="tester">
            <WifiOff className="h-4 w-4 mr-2" />
            Test hors ligne
          </TabsTrigger>
          <TabsTrigger value="preload">
            <Download className="h-4 w-4 mr-2" />
            Préchargement
          </TabsTrigger>
          <TabsTrigger value="cache">
            <Database className="h-4 w-4 mr-2" />
            État du cache
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tester" className="space-y-4">
          <OfflineTester />
        </TabsContent>

        <TabsContent value="preload" className="space-y-4">
          <OfflinePreloader />

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Download className="h-5 w-5 mr-2 text-primary" />À propos du préchargement
              </CardTitle>
              <CardDescription>
                Informations sur le fonctionnement du préchargement des données pour le mode hors ligne
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Comment ça marche ?</h3>
                  <p className="text-sm text-muted-foreground">
                    ACR Direct est conçu pour fonctionner parfaitement même sans connexion Internet. Lorsque vous êtes
                    connecté, l'application met automatiquement en cache les pages, les images et les données que vous
                    consultez pour les rendre disponibles hors ligne.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Préchargement des données</h3>
                  <p className="text-sm text-muted-foreground">
                    Pour garantir une expérience optimale hors ligne, vous pouvez précharger manuellement toutes les
                    données essentielles en utilisant le bouton "Précharger les données". Cette opération télécharge et
                    met en cache les pages, les actualités et les ressources nécessaires pour une utilisation complète
                    de l'application.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Authentification hors ligne</h3>
                  <p className="text-sm text-muted-foreground">
                    Votre session d'authentification est également préservée en mode hors ligne. Vous pouvez fermer et
                    rouvrir l'application sans avoir à vous reconnecter, même sans connexion Internet.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Synchronisation</h3>
                  <p className="text-sm text-muted-foreground">
                    Lorsque vous revenez en ligne, l'application synchronise automatiquement les données avec le serveur
                    pour vous assurer d'avoir toujours les informations les plus à jour.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2 text-primary" />
                État du cache
              </CardTitle>
              <CardDescription>Informations détaillées sur l'état du cache de l'application</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <CacheStatus showDetails={true} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
