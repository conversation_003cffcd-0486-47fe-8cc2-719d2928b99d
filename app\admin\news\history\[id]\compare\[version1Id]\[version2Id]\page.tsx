import VersionComparison from "@/components/version-comparison"
import { ContentType } from "@/lib/history-utils"

interface NewsVersionCompareProps {
  params: {
    id: string
    version1Id: string
    version2Id: string
  }
}

export default function NewsVersionComparePage({ params }: NewsVersionCompareProps) {
  // Default to comparing with the "current" version if version1Id is "current"
  const version1Id = "current"
  const version2Id = params.version2Id

  return (
    <VersionComparison
      contentType={ContentType.NEWS}
      contentId={params.id}
      version1Id={version1Id}
      version2Id={version2Id}
    />
  )
}
