import { db } from "./firebase"
import { doc, setDoc, deleteDoc } from "firebase/firestore"
import { getAuth } from "firebase/auth"

// Types pour les opérations en attente
export type PendingOperation = {
  id: string
  collection: string
  documentId: string
  operation: "create" | "update" | "delete"
  data?: any
  timestamp: number
  userId: string
}

// Nom de la collection pour les opérations en attente
const PENDING_OPERATIONS_COLLECTION = "pendingOperations"
const DB_NAME = "ACRDirectOfflineDB"
const DB_VERSION = 1

// Fonction pour ouvrir et initialiser la base de données
function openDatabase(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    // Vérifier si IndexedDB est disponible
    if (!("indexedDB" in window)) {
      reject(new Error("IndexedDB n'est pas supporté par ce navigateur"))
      return
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result

      // Créer le store d'objets s'il n'existe pas
      if (!db.objectStoreNames.contains(PENDING_OPERATIONS_COLLECTION)) {
        const store = db.createObjectStore(PENDING_OPERATIONS_COLLECTION, { keyPath: "id" })
        store.createIndex("userId", "userId", { unique: false })
        store.createIndex("timestamp", "timestamp", { unique: false })
        console.log(`Object store ${PENDING_OPERATIONS_COLLECTION} créé avec succès`)
      }
    }

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result
      resolve(db)
    }

    request.onerror = (event) => {
      console.error("Erreur lors de l'ouverture de la base de données:", (event.target as IDBOpenDBRequest).error)
      reject((event.target as IDBOpenDBRequest).error)
    }
  })
}

// Fonction pour vérifier si la base de données est correctement initialisée
async function ensureDatabaseInitialized(): Promise<boolean> {
  try {
    const db = await openDatabase()

    // Vérifier si l'object store existe
    const storeExists = db.objectStoreNames.contains(PENDING_OPERATIONS_COLLECTION)
    db.close()

    if (!storeExists) {
      console.warn(`L'object store ${PENDING_OPERATIONS_COLLECTION} n'existe pas, tentative de réinitialisation...`)

      // Supprimer et recréer la base de données
      await new Promise<void>((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(DB_NAME)

        deleteRequest.onsuccess = () => {
          console.log("Base de données supprimée avec succès, prête pour la réinitialisation")
          resolve()
        }

        deleteRequest.onerror = (event) => {
          console.error(
            "Erreur lors de la suppression de la base de données:",
            (event.target as IDBOpenDBRequest).error,
          )
          reject((event.target as IDBOpenDBRequest).error)
        }
      })

      // Réouvrir la base de données pour déclencher onupgradeneeded
      await openDatabase()
    }

    return true
  } catch (error) {
    console.error("Erreur lors de l'initialisation de la base de données:", error)
    return false
  }
}

// Fonction pour enregistrer une opération en attente dans IndexedDB
export async function savePendingOperation(
  operation: Omit<PendingOperation, "id" | "timestamp" | "userId">,
): Promise<void> {
  try {
    // S'assurer que la base de données est initialisée
    await ensureDatabaseInitialized()

    const db = await openDatabase()

    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'object store existe
        if (!db.objectStoreNames.contains(PENDING_OPERATIONS_COLLECTION)) {
          db.close()
          reject(new Error(`L'object store ${PENDING_OPERATIONS_COLLECTION} n'existe pas`))
          return
        }

        const transaction = db.transaction([PENDING_OPERATIONS_COLLECTION], "readwrite")
        const store = transaction.objectStore(PENDING_OPERATIONS_COLLECTION)

        // Créer l'opération complète
        const auth = getAuth()
        const userId = auth.currentUser?.uid || "anonymous"

        const completeOperation: PendingOperation = {
          id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          ...operation,
          timestamp: Date.now(),
          userId,
        }

        // Ajouter l'opération au store
        const request = store.add(completeOperation)

        request.onsuccess = () => {
          console.log("Opération en attente enregistrée avec succès")

          // Déclencher une synchronisation si nous sommes en ligne
          if (navigator.onLine) {
            syncPendingOperations()
          } else if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
            // Enregistrer une tâche de synchronisation en arrière-plan
            navigator.serviceWorker.ready.then((registration) => {
              if ("sync" in registration) {
                registration.sync
                  .register("sync-pending-data")
                  .catch((err) => console.error("Erreur lors de l'enregistrement de la synchronisation:", err))
              }
            })
          }

          resolve()
        }

        request.onerror = (event) => {
          console.error("Erreur lors de l'enregistrement de l'opération en attente:", request.error)
          reject(request.error)
        }

        transaction.oncomplete = () => {
          db.close()
        }

        transaction.onerror = (event) => {
          console.error("Erreur de transaction:", transaction.error)
          reject(transaction.error)
        }
      } catch (error) {
        db.close()
        reject(error)
      }
    })
  } catch (error) {
    console.error("Erreur lors de la sauvegarde de l'opération en attente:", error)
    throw error
  }
}

// Fonction pour récupérer les opérations en attente
export async function getPendingOperations(): Promise<PendingOperation[]> {
  try {
    // S'assurer que la base de données est initialisée
    await ensureDatabaseInitialized()

    const db = await openDatabase()

    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'object store existe
        if (!db.objectStoreNames.contains(PENDING_OPERATIONS_COLLECTION)) {
          db.close()
          resolve([]) // Retourner un tableau vide si l'object store n'existe pas
          return
        }

        const transaction = db.transaction([PENDING_OPERATIONS_COLLECTION], "readonly")
        const store = transaction.objectStore(PENDING_OPERATIONS_COLLECTION)

        // Récupérer toutes les opérations
        const request = store.getAll()

        request.onsuccess = () => {
          resolve(request.result || [])
        }

        request.onerror = () => {
          console.error("Erreur lors de la récupération des opérations en attente:", request.error)
          resolve([])
        }

        transaction.oncomplete = () => {
          db.close()
        }
      } catch (error) {
        db.close()
        resolve([])
      }
    })
  } catch (error) {
    console.error("Erreur lors de la récupération des opérations en attente:", error)
    return []
  }
}

// Fonction pour supprimer une opération en attente
export async function deletePendingOperation(id: string): Promise<void> {
  try {
    // S'assurer que la base de données est initialisée
    await ensureDatabaseInitialized()

    const db = await openDatabase()

    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'object store existe
        if (!db.objectStoreNames.contains(PENDING_OPERATIONS_COLLECTION)) {
          db.close()
          resolve() // Ne rien faire si l'object store n'existe pas
          return
        }

        const transaction = db.transaction([PENDING_OPERATIONS_COLLECTION], "readwrite")
        const store = transaction.objectStore(PENDING_OPERATIONS_COLLECTION)

        // Supprimer l'opération
        const request = store.delete(id)

        request.onsuccess = () => {
          resolve()
        }

        request.onerror = () => {
          console.error("Erreur lors de la suppression de l'opération en attente:", request.error)
          reject(request.error)
        }

        transaction.oncomplete = () => {
          db.close()
        }
      } catch (error) {
        db.close()
        reject(error)
      }
    })
  } catch (error) {
    console.error("Erreur lors de la suppression de l'opération en attente:", error)
    throw error
  }
}

// Fonction pour synchroniser les opérations en attente avec Firestore
export async function syncPendingOperations(): Promise<void> {
  try {
    // Récupérer les opérations en attente
    const operations = await getPendingOperations()

    if (operations.length === 0) {
      return
    }

    console.log(`Synchronisation de ${operations.length} opérations en attente...`)

    // Trier les opérations par timestamp (les plus anciennes d'abord)
    operations.sort((a, b) => a.timestamp - b.timestamp)

    // Traiter chaque opération
    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case "create":
          case "update":
            if (operation.data) {
              // Créer ou mettre à jour le document
              await setDoc(doc(db(), operation.collection, operation.documentId), operation.data, { merge: true })
            }
            break
          case "delete":
            // Supprimer le document
            await deleteDoc(doc(db(), operation.collection, operation.documentId))
            break
        }

        // Supprimer l'opération traitée
        await deletePendingOperation(operation.id)
      } catch (error) {
        console.error(`Erreur lors de la synchronisation de l'opération ${operation.id}:`, error)
        // Continuer avec les autres opérations
      }
    }

    console.log("Synchronisation terminée")
  } catch (error) {
    console.error("Erreur lors de la synchronisation des opérations en attente:", error)
  }
}

// Fonction pour créer un document avec prise en charge du mode hors ligne
export async function createDocumentWithOfflineSupport(
  collectionPath: string,
  documentId: string,
  data: any,
): Promise<void> {
  try {
    if (navigator.onLine) {
      // Si en ligne, essayer d'abord d'écrire directement dans Firestore
      try {
        await setDoc(doc(db(), collectionPath, documentId), data)
        return
      } catch (error) {
        console.warn("Erreur lors de la création du document en ligne, passage en mode hors ligne:", error)
      }
    }

    // Si hors ligne ou si l'écriture en ligne a échoué, enregistrer l'opération en attente
    await savePendingOperation({
      collection: collectionPath,
      documentId,
      operation: "create",
      data,
    })
  } catch (error) {
    console.error("Erreur lors de la création du document avec support hors ligne:", error)
    throw error
  }
}

// Fonction pour mettre à jour un document avec prise en charge du mode hors ligne
export async function updateDocumentWithOfflineSupport(
  collectionPath: string,
  documentId: string,
  data: any,
): Promise<void> {
  try {
    if (navigator.onLine) {
      // Si en ligne, essayer d'abord d'écrire directement dans Firestore
      try {
        await setDoc(doc(db(), collectionPath, documentId), data, { merge: true })
        return
      } catch (error) {
        console.warn("Erreur lors de la mise à jour du document en ligne, passage en mode hors ligne:", error)
      }
    }

    // Si hors ligne ou si l'écriture en ligne a échoué, enregistrer l'opération en attente
    await savePendingOperation({
      collection: collectionPath,
      documentId,
      operation: "update",
      data,
    })
  } catch (error) {
    console.error("Erreur lors de la mise à jour du document avec support hors ligne:", error)
    throw error
  }
}

// Fonction pour supprimer un document avec prise en charge du mode hors ligne
export async function deleteDocumentWithOfflineSupport(collectionPath: string, documentId: string): Promise<void> {
  try {
    if (navigator.onLine) {
      // Si en ligne, essayer d'abord de supprimer directement dans Firestore
      try {
        await deleteDoc(doc(db(), collectionPath, documentId))
        return
      } catch (error) {
        console.warn("Erreur lors de la suppression du document en ligne, passage en mode hors ligne:", error)
      }
    }

    // Si hors ligne ou si la suppression en ligne a échoué, enregistrer l'opération en attente
    await savePendingOperation({
      collection: collectionPath,
      documentId,
      operation: "delete",
    })
  } catch (error) {
    console.error("Erreur lors de la suppression du document avec support hors ligne:", error)
    throw error
  }
}

// Initialiser la synchronisation au démarrage de l'application
export async function initOfflineSync() {
  try {
    // S'assurer que la base de données est correctement initialisée
    await ensureDatabaseInitialized()

    // Synchroniser les opérations en attente lorsque la connexion est rétablie
    window.addEventListener("online", () => {
      console.log("Connexion rétablie, synchronisation des données...")
      syncPendingOperations()
    })

    // Écouter les messages du Service Worker
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.addEventListener("message", (event) => {
        if (event.data && event.data.type === "SYNC_STARTED") {
          console.log("Synchronisation en arrière-plan démarrée")
        } else if (event.data && event.data.type === "SYNC_COMPLETED") {
          console.log("Synchronisation en arrière-plan terminée:", event.data.success ? "succès" : "échec")
        }
      })
    }

    // Synchroniser au démarrage si en ligne
    if (navigator.onLine) {
      syncPendingOperations()
    }

    return true
  } catch (error) {
    console.error("Erreur lors de l'initialisation du système de synchronisation hors ligne:", error)
    return false
  }
}

// Fonction pour vérifier l'état de la base de données
export async function checkDatabaseStatus(): Promise<{
  initialized: boolean
  pendingOperationsCount: number
}> {
  try {
    // S'assurer que la base de données est initialisée
    const initialized = await ensureDatabaseInitialized()

    // Récupérer le nombre d'opérations en attente
    const operations = await getPendingOperations()

    return {
      initialized,
      pendingOperationsCount: operations.length,
    }
  } catch (error) {
    console.error("Erreur lors de la vérification de l'état de la base de données:", error)
    return {
      initialized: false,
      pendingOperationsCount: 0,
    }
  }
}
