import { type Permission, PERMISSIONS } from "./permissions"

// Update the Role interface to include isPreDefined
export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  isSystem?: boolean
  isPreDefined?: boolean
}

// Predefined roles
export const PREDEFINED_ROLES: Record<string, Role> = {
  ADMIN: {
    id: "admin",
    name: "Administra<PERSON><PERSON>",
    description: "Accès complet à toutes les fonctionnalités",
    permissions: [PERMISSIONS.ADMIN],
    isSystem: true, // Keep admin as system role
  },
  EDITOR: {
    id: "editor",
    name: "<PERSON><PERSON>eur",
    description: "Peut créer et modifier du contenu",
    permissions: [
      PERMISSIONS.CREATE_NEWS,
      PERMISSIONS.READ_NEWS,
      PERMISSIONS.UPDATE_NEWS,
      PERMISSIONS.PUBLISH_NEWS,
      PERMISSIONS.CREATE_PAGES,
      PERMISSIONS.READ_PAGES,
      PERMISSIONS.UPDATE_PAGES,
      PERMISSIONS.PUBLISH_PAGES,
    ],
    isPreDefined: true, // Change from isSystem to isPreDefined
  },
  USER_MANAGER: {
    id: "user_manager",
    name: "Gestionnaire d'utilisateurs",
    description: "Peut gérer les utilisateurs et les groupes",
    permissions: [
      PERMISSIONS.CREATE_USERS,
      PERMISSIONS.READ_USERS,
      PERMISSIONS.UPDATE_USERS,
      PERMISSIONS.DELETE_USERS,
      PERMISSIONS.READ_GROUPS,
      PERMISSIONS.ASSIGN_GROUPS,
    ],
    isPreDefined: true, // Change from isSystem to isPreDefined
  },
  VIEWER: {
    id: "viewer",
    name: "Lecteur",
    description: "Peut uniquement consulter le contenu",
    permissions: [PERMISSIONS.READ_NEWS, PERMISSIONS.READ_PAGES],
    isPreDefined: true, // Change from isSystem to isPreDefined
  },
  // Add the following role
  SALES_REPRESENTATIVE: {
    id: "sales_representative",
    name: "Commercial",
    description: "Peut gérer son profil et consulter les informations des clients",
    permissions: [PERMISSIONS.READ_USERS, PERMISSIONS.UPDATE_USERS],
    isPreDefined: true,
  },
}

// Helper function to get all permissions for a set of roles
export function getRolePermissions(roles: string[], availableRoles: Role[]): Permission[] {
  const permissions: Permission[] = []

  roles.forEach((roleId) => {
    const role = availableRoles.find((r) => r.id === roleId)
    if (role) {
      role.permissions.forEach((permission) => {
        if (!permissions.includes(permission)) {
          permissions.push(permission)
        }
      })
    }
  })

  return permissions
}
