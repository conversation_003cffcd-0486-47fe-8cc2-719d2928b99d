"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
// Modifier les imports pour utiliser la nouvelle structure d'auth
import { auth } from "@/lib/firebase"
import { signInWithEmailAndPassword } from "firebase/auth"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Mail, Lock } from "lucide-react"

export function LoginForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const [formError, setFormError] = useState<string | null>(null)

  // Modifier la fonction handleSubmit pour utiliser auth() au lieu de auth directement
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Sign in with Firebase
      const userCredential = await signInWithEmailAndPassword(auth(), email, password)

      // Wait for the session to be created on the server
      try {
        const idToken = await userCredential.user.getIdToken()
        const response = await fetch("/api/auth/sessionLogin", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ idToken }),
        })

        if (!response.ok) {
          throw new Error("Failed to create session")
        }

        console.log("Session created successfully, redirecting to dashboard...")
      } catch (sessionError) {
        console.error("Session creation error:", sessionError)
        // Continue with redirection even if session creation fails
        // The session recovery mechanism will handle it
      }

      // Force navigation to dashboard
      window.location.href = "/dashboard"
    } catch (error: any) {
      console.error("Login error:", error)

      // Gérer les différents types d'erreurs d'authentification
      let errorMessage = "Identifiants incorrects. Veuillez réessayer."

      if (
        error.code === "auth/invalid-credential" ||
        error.code === "auth/user-not-found" ||
        error.code === "auth/wrong-password"
      ) {
        errorMessage = "Email ou mot de passe incorrect. Veuillez vérifier vos informations et réessayer."
      } else if (error.code === "auth/user-disabled") {
        errorMessage = "Ce compte a été désactivé. Veuillez contacter l'administrateur."
      } else if (error.code === "auth/too-many-requests") {
        errorMessage = "Trop de tentatives de connexion. Veuillez réessayer plus tard."
      } else if (error.code === "auth/network-request-failed") {
        errorMessage = "Problème de connexion réseau. Vérifiez votre connexion internet."
      }

      toast({
        title: "Erreur de connexion",
        description: errorMessage,
        variant: "destructive",
      })
      setFormError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          Email
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value)
              setFormError(null)
            }}
            required
            className="pl-10 bg-background"
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="password" className="text-sm font-medium">
          Mot de passe
        </Label>
        <div className="relative">
          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value)
              setFormError(null)
            }}
            required
            className="pl-10 bg-background"
          />
        </div>
      </div>
      {formError && (
        <div className="p-3 rounded-md bg-red-50 border border-red-200 text-red-800 text-sm">
          <p className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            {formError}
          </p>
        </div>
      )}
      <Button type="submit" className="w-full font-medium transition-all" disabled={isLoading}>
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Connexion en cours...
          </>
        ) : (
          "Se connecter"
        )}
      </Button>
    </form>
  )
}
