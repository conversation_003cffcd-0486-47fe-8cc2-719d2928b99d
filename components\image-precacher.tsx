"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { prefetchImages } from "@/app/sw-register"

interface ImagePrecacherProps {
  urls: string[]
  children?: React.ReactNode
}

export function ImagePrecacher({ urls, children }: ImagePrecacherProps) {
  const [precached, setPrecached] = useState(false)

  useEffect(() => {
    // Vérifier si le service worker est actif
    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
      // Précharger les images
      prefetchImages(urls)
        .then(() => {
          console.log("Images préchargées avec succès")
          setPrecached(true)
        })
        .catch((error) => {
          console.error("Erreur lors du préchargement des images:", error)
          setPrecached(true) // Continuer même en cas d'erreur
        })
    } else {
      // Si le service worker n'est pas actif, on précharge manuellement
      Promise.all(
        urls.map((url) => {
          return new Promise<void>((resolve) => {
            const img = new Image()
            img.onload = () => resolve()
            img.onerror = () => resolve() // Continuer même en cas d'erreur
            img.src = url
          })
        }),
      ).then(() => {
        setPrecached(true)
      })
    }
  }, [urls])

  return <>{children}</>
}
