"use client"
import { <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react"
import { loadThemeFromSettings, applyTheme } from "@/lib/theme-service"

export function ThemeToggle() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Wait for component to mount to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Re-apply theme when theme changes
  useEffect(() => {
    if (!mounted) return

    const applyCurrentTheme = async () => {
      try {
        const themeColors = await loadThemeFromSettings()
        const isDark = resolvedTheme === "dark"
        applyTheme(themeColors, isDark)

        // Ensure the theme is stored in sessionStorage for page navigations
        sessionStorage.setItem("current_theme", JSON.stringify(themeColors))
      } catch (error) {
        console.error("Failed to apply theme on toggle:", error)
      }
    }

    applyCurrentTheme()
  }, [resolvedTheme, mounted])

  const toggleTheme = () => {
    const newTheme = resolvedTheme === "dark" ? "light" : "dark"
    setTheme(newTheme)

    // Force reapply theme after a short delay to ensure the theme class has changed
    setTimeout(async () => {
      try {
        const themeColors = await loadThemeFromSettings()
        applyTheme(themeColors, newTheme === "dark")
      } catch (error) {
        console.error("Failed to reapply theme after toggle:", error)
      }
    }, 50)
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="rounded-full opacity-0" aria-hidden="true">
        <Sun className="h-[1.2rem] w-[1.2rem]" />
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="rounded-full transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
      aria-label={resolvedTheme === "dark" ? "Mode clair" : "Mode sombre"}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">{resolvedTheme === "dark" ? "Mode clair" : "Mode sombre"}</span>
    </Button>
  )
}
