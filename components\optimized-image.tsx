"use client"

import { useState, useEffect, useRef } from "react"
import { cn } from "@/lib/utils"
import { PlaceholderImage } from "@/components/placeholder-image"
import { isImageCached } from "@/lib/image-optimization"

// Cache global pour les images déjà chargées
const imageCache = new Map<string, string>()

interface OptimizedImageProps {
  src: string | null
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
  unoptimized?: boolean
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  objectFit = "cover",
  onLoad,
  onError,
  fallbackSrc = "/placeholder.svg",
  unoptimized = false,
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [imgSrc, setImgSrc] = useState<string | null>(null)
  const [isCached, setIsCached] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    if (!src) {
      setError(true)
      return
    }

    // Reset states when src changes
    setIsLoaded(false)
    setError(false)

    // Check if image is already in memory cache
    if (imageCache.has(src)) {
      setImgSrc(imageCache.get(src) || null)
      // Use requestAnimationFrame to ensure the DOM has updated
      requestAnimationFrame(() => {
        // If the image is already in cache and complete, mark as loaded immediately
        if (imgRef.current?.complete) {
          setIsLoaded(true)
          if (onLoad) onLoad()
        }
      })
      return
    }

    // For Firebase Storage URLs, we can add a size parameter if not unoptimized
    let finalSrc = src
    if (!unoptimized && src.includes("firebasestorage.googleapis.com")) {
      // Extract the token part
      const tokenMatch = src.match(/\?token=([^&]+)/)
      const token = tokenMatch ? tokenMatch[1] : null

      // Add width parameter if not already present
      if (width && !src.includes("&width=")) {
        finalSrc = `${src}${token ? "" : "?"}&width=${width * 2}` // 2x for retina displays
      }
    }

    // Check if the image is in the Service Worker cache
    isImageCached(finalSrc)
      .then((cached) => {
        setIsCached(cached)
        if (cached) {
          // Si l'image est déjà en cache, on peut la marquer comme chargée plus rapidement
          setImgSrc(finalSrc)
          imageCache.set(src, finalSrc) // Cache the processed URL
        } else {
          setImgSrc(finalSrc)
          imageCache.set(src, finalSrc) // Cache the processed URL
        }
      })
      .catch(() => {
        // En cas d'erreur, on continue normalement
        setImgSrc(finalSrc)
        imageCache.set(src, finalSrc) // Cache the processed URL
      })
  }, [src, width, onLoad])

  const handleLoad = () => {
    setIsLoaded(true)
    if (onLoad) onLoad()
  }

  const handleError = () => {
    // Si nous avons une image de secours, essayons de l'utiliser au lieu de montrer une erreur
    if (fallbackSrc && fallbackSrc !== src) {
      setImgSrc(fallbackSrc)
      // Ne pas marquer comme erreur pour permettre l'affichage de l'image de secours
    } else {
      setError(true)
      if (onError) onError()
    }

    // Remove from cache if there was an error
    if (src) imageCache.delete(src)
  }

  return (
    <div
      className={cn("relative overflow-hidden", className)}
      style={{ width: width ? `${width}px` : "100%", height: height ? `${height}px` : "100%" }}
    >
      {(!isLoaded || error) && (
        <div className="absolute inset-0 z-0">
          <PlaceholderImage />
        </div>
      )}

      {!error && imgSrc && (
        <img
          ref={imgRef}
          src={imgSrc || "/placeholder.svg"}
          alt={alt}
          width={width}
          height={height}
          loading={priority || isCached ? "eager" : "lazy"}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0",
            objectFit === "contain" && "object-contain",
            objectFit === "cover" && "object-cover",
            objectFit === "fill" && "object-fill",
            objectFit === "none" && "object-none",
            objectFit === "scale-down" && "object-scale-down",
            "w-full h-full",
          )}
        />
      )}
    </div>
  )
}
