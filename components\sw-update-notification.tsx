"use client"

import { useEffect, useState } from "react"
import { updateServiceWorker } from "@/app/sw-register"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, RefreshCw } from "lucide-react"

export function ServiceWorkerUpdateNotification() {
  const [updateAvailable, setUpdateAvailable] = useState(false)

  useEffect(() => {
    // Check if an update is already available
    if (typeof window !== "undefined") {
      setUpdateAvailable(!!window.swUpdateReady)

      // Listen for future updates
      const handleUpdateFound = () => setUpdateAvailable(true)
      window.addEventListener("swUpdateReady", handleUpdateFound)

      return () => {
        window.removeEventListener("swUpdateReady", handleUpdateFound)
      }
    }
  }, [])

  if (!updateAvailable) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 animate-in slide-in-from-bottom-5">
      <div className="flex items-start gap-3">
        <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
        <div className="flex-1">
          <h3 className="font-medium">Mise à jour disponible</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Une nouvelle version de l'application est disponible. Veuillez mettre à jour pour bénéficier des dernières
            fonctionnalités.
          </p>
          <div className="mt-3 flex justify-end">
            <Button size="sm" onClick={() => updateServiceWorker()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Mettre à jour
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
