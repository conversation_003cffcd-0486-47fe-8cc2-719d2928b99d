"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"

interface ImageDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (imageData: { file?: File; url?: string; alt: string; align: string; width: string }) => void
  isUploading: boolean
}

export function ImageDialog({ isOpen, onClose, onConfirm, isUploading }: ImageDialogProps) {
  const [activeTab, setActiveTab] = useState<string>("upload")
  const [file, setFile] = useState<File | null>(null)
  const [url, setUrl] = useState("")
  const [alt, setAlt] = useState("")
  const [align, setAlign] = useState("none")
  const [width, setWidth] = useState("100%")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
    }
  }

  const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Empêcher la propagation de l'événement pour éviter la soumission du formulaire parent
    e.preventDefault()
    e.stopPropagation()

    if (activeTab === "upload" && file) {
      onConfirm({ file, alt, align, width })
    } else if (activeTab === "url" && url) {
      onConfirm({ url, alt, align, width })
    }
  }

  const resetForm = () => {
    setFile(null)
    setUrl("")
    setAlt("")
    setAlign("none")
    setWidth("100%")
    setActiveTab("upload")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
          resetForm()
        }
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Insérer une image</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Tabs defaultValue="upload" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Télécharger</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
            </TabsList>
            <TabsContent value="upload" className="py-4">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="image">Fichier image</Label>
                  <Input id="image" type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} />
                  {file && (
                    <p className="text-sm text-muted-foreground">
                      {file.name} ({Math.round(file.size / 1024)} Ko)
                    </p>
                  )}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="url" className="py-4">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="url">URL de l'image</Label>
                  <Input
                    id="url"
                    placeholder="https://exemple.com/image.jpg"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="alt">Texte alternatif</Label>
              <Input
                id="alt"
                placeholder="Description de l'image"
                value={alt}
                onChange={(e) => setAlt(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="width">Taille</Label>
                <Select value={width} onValueChange={setWidth}>
                  <SelectTrigger id="width">
                    <SelectValue placeholder="Sélectionner une taille" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="25%">25%</SelectItem>
                    <SelectItem value="50%">50%</SelectItem>
                    <SelectItem value="75%">75%</SelectItem>
                    <SelectItem value="100%">100%</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="align">Alignement</Label>
                <Select value={align} onValueChange={setAlign}>
                  <SelectTrigger id="align">
                    <SelectValue placeholder="Sélectionner un alignement" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Aucun</SelectItem>
                    <SelectItem value="left">Gauche</SelectItem>
                    <SelectItem value="center">Centre</SelectItem>
                    <SelectItem value="right">Droite</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={isUploading}>
            Annuler
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={isUploading || (activeTab === "upload" && !file) || (activeTab === "url" && !url.trim())}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Téléchargement...
              </>
            ) : (
              "Insérer l'image"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
