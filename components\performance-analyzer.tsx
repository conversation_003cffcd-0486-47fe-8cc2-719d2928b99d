"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, BarChart, Clock, Zap, FileDown, RefreshCw, AlertTriangle } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  status: "good" | "warning" | "bad"
  description: string
}

interface ResourceMetric {
  url: string
  type: string
  size: number
  duration: number
  status: "good" | "warning" | "bad"
}

export function PerformanceAnalyzer() {
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false)
  const [analyzeProgress, setAnalyzeProgress] = useState<number>(0)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([])
  const [resourceMetrics, setResourceMetrics] = useState<ResourceMetric[]>([])
  const [activeTab, setActiveTab] = useState<string>("metrics")
  const [message, setMessage] = useState<{ type: "success" | "error" | "info"; text: string } | null>(null)

  // Analyser les performances
  const analyzePerformance = async () => {
    if (isAnalyzing) return

    setIsAnalyzing(true)
    setAnalyzeProgress(0)
    setMessage({ type: "info", text: "Analyse des performances en cours..." })

    try {
      // Étape 1: Vérifier si l'API Performance est disponible
      if (!("performance" in window)) {
        throw new Error("L'API Performance n'est pas disponible dans ce navigateur")
      }

      setAnalyzeProgress(10)

      // Étape 2: Récupérer les métriques de navigation
      const navigationTiming = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming

      if (!navigationTiming) {
        throw new Error("Impossible de récupérer les métriques de navigation")
      }

      setAnalyzeProgress(30)

      // Étape 3: Calculer les métriques de performance
      const metrics: PerformanceMetric[] = [
        {
          name: "Temps de chargement total",
          value: Math.round(navigationTiming.loadEventEnd - navigationTiming.startTime),
          unit: "ms",
          status: getLoadTimeStatus(navigationTiming.loadEventEnd - navigationTiming.startTime),
          description: "Temps total de chargement de la page, du début de la navigation jusqu'à l'événement load",
        },
        {
          name: "Temps de réponse du serveur (TTFB)",
          value: Math.round(navigationTiming.responseStart - navigationTiming.requestStart),
          unit: "ms",
          status: getTTFBStatus(navigationTiming.responseStart - navigationTiming.requestStart),
          description: "Temps écoulé entre la requête et le début de la réponse du serveur",
        },
        {
          name: "Temps de traitement DOM",
          value: Math.round(navigationTiming.domComplete - navigationTiming.domInteractive),
          unit: "ms",
          status: getDOMProcessingStatus(navigationTiming.domComplete - navigationTiming.domInteractive),
          description: "Temps nécessaire pour analyser et construire le DOM",
        },
        {
          name: "Temps d'interactivité",
          value: Math.round(navigationTiming.domInteractive - navigationTiming.startTime),
          unit: "ms",
          status: getInteractivityStatus(navigationTiming.domInteractive - navigationTiming.startTime),
          description: "Temps écoulé avant que la page ne devienne interactive",
        },
      ]

      setPerformanceMetrics(metrics)
      setAnalyzeProgress(60)

      // Étape 4: Récupérer les métriques de ressources
      const resourceEntries = performance.getEntriesByType("resource") as PerformanceResourceTiming[]

      const resources: ResourceMetric[] = resourceEntries.map((entry) => {
        // Déterminer le type de ressource
        let type = "other"
        const url = entry.name

        if (url.endsWith(".js")) type = "script"
        else if (url.endsWith(".css")) type = "style"
        else if (
          url.endsWith(".png") ||
          url.endsWith(".jpg") ||
          url.endsWith(".jpeg") ||
          url.endsWith(".gif") ||
          url.endsWith(".svg")
        )
          type = "image"
        else if (url.endsWith(".woff") || url.endsWith(".woff2") || url.endsWith(".ttf")) type = "font"
        else if (url.includes("api") || url.includes("firestore")) type = "api"

        // Calculer la taille (si disponible)
        const size = entry.encodedBodySize || 0

        // Calculer la durée
        const duration = entry.duration

        // Déterminer le statut
        let status: "good" | "warning" | "bad" = "good"

        if (type === "image" && size > 200 * 1024) {
          status = "warning" // Images de plus de 200 KB
        } else if (type === "script" && duration > 500) {
          status = "warning" // Scripts qui prennent plus de 500ms à charger
        } else if (duration > 1000) {
          status = "bad" // Toute ressource qui prend plus de 1s à charger
        }

        return {
          url,
          type,
          size,
          duration,
          status,
        }
      })

      // Trier par durée (les plus lentes en premier)
      resources.sort((a, b) => b.duration - a.duration)

      setResourceMetrics(resources)
      setAnalyzeProgress(100)
      setMessage({ type: "success", text: "Analyse des performances terminée" })
    } catch (error) {
      console.error("Erreur lors de l'analyse des performances:", error)
      setMessage({ type: "error", text: String(error) })
    } finally {
      setIsAnalyzing(false)
    }
  }

  // Fonctions pour déterminer le statut des métriques
  const getLoadTimeStatus = (time: number): "good" | "warning" | "bad" => {
    if (time < 2000) return "good"
    if (time < 5000) return "warning"
    return "bad"
  }

  const getTTFBStatus = (time: number): "good" | "warning" | "bad" => {
    if (time < 200) return "good"
    if (time < 500) return "warning"
    return "bad"
  }

  const getDOMProcessingStatus = (time: number): "good" | "warning" | "bad" => {
    if (time < 500) return "good"
    if (time < 1000) return "warning"
    return "bad"
  }

  const getInteractivityStatus = (time: number): "good" | "warning" | "bad" => {
    if (time < 1000) return "good"
    if (time < 3000) return "warning"
    return "bad"
  }

  // Formater la taille en KB, MB, etc.
  const formatSize = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes} B`
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
    }
  }

  // Exporter les résultats au format JSON
  const exportResults = () => {
    const data = {
      performanceMetrics,
      resourceMetrics,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = `performance-report-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Effacer les entrées de performance
  const clearPerformanceEntries = () => {
    if ("performance" in window && "clearResourceTimings" in performance) {
      performance.clearResourceTimings()
      setMessage({ type: "info", text: "Entrées de performance effacées" })
    }
  }

  // Analyser automatiquement les performances au chargement du composant
  useEffect(() => {
    analyzePerformance()

    // Nettoyer le message après 5 secondes
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Analyseur de performances</span>
        </CardTitle>
        <CardDescription>
          Analysez les performances de l'application et identifiez les goulots d'étranglement
        </CardDescription>
      </CardHeader>
      <CardContent>
        {message && (
          <Alert
            className={`mb-4 ${
              message.type === "success"
                ? "bg-green-50 border-green-200"
                : message.type === "error"
                  ? "bg-red-50 border-red-200"
                  : "bg-blue-50 border-blue-200"
            }`}
          >
            <AlertTitle>
              {message.type === "success" ? "Succès" : message.type === "error" ? "Erreur" : "Information"}
            </AlertTitle>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <Button onClick={analyzePerformance} disabled={isAnalyzing} className="flex-1">
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyse en cours...
              </>
            ) : (
              <>
                <BarChart className="mr-2 h-4 w-4" />
                Analyser les performances
              </>
            )}
          </Button>

          <Button
            onClick={exportResults}
            variant="outline"
            className="flex-1"
            disabled={performanceMetrics.length === 0}
          >
            <FileDown className="mr-2 h-4 w-4" />
            Exporter les résultats
          </Button>

          <Button onClick={clearPerformanceEntries} variant="outline" className="flex-1">
            <RefreshCw className="mr-2 h-4 w-4" />
            Réinitialiser
          </Button>
        </div>

        {isAnalyzing && (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span>Progression de l'analyse</span>
              <span>{analyzeProgress}%</span>
            </div>
            <Progress value={analyzeProgress} className="h-2" />
          </div>
        )}

        <Tabs defaultValue="metrics" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="metrics">Métriques</TabsTrigger>
            <TabsTrigger value="resources">Ressources</TabsTrigger>
          </TabsList>

          <TabsContent value="metrics" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Métriques de performance</h3>

              {performanceMetrics.length === 0 ? (
                <div className="flex justify-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Aucune métrique disponible. Lancez l'analyse pour voir les résultats.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {performanceMetrics.map((metric, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 ${
                        metric.status === "good"
                          ? "border-green-200 bg-green-50"
                          : metric.status === "warning"
                            ? "border-amber-200 bg-amber-50"
                            : "border-red-200 bg-red-50"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{metric.name}</h4>
                        <span
                          className={`text-sm font-bold ${
                            metric.status === "good"
                              ? "text-green-600"
                              : metric.status === "warning"
                                ? "text-amber-600"
                                : "text-red-600"
                          }`}
                        >
                          {metric.value} {metric.unit}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">{metric.description}</p>
                    </div>
                  ))}
                </div>
              )}

              <div className="mt-4 border rounded-lg p-4 bg-blue-50 border-blue-200">
                <h4 className="font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-blue-600" />
                  Interprétation des résultats
                </h4>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>
                    <strong>Temps de chargement total</strong> : Idéalement &lt; 2s, acceptable &lt; 5s
                  </li>
                  <li>
                    <strong>Temps de réponse du serveur (TTFB)</strong> : Idéalement &lt; 200ms, acceptable &lt; 500ms
                  </li>
                  <li>
                    <strong>Temps de traitement DOM</strong> : Idéalement &lt; 500ms, acceptable &lt; 1s
                  </li>
                  <li>
                    <strong>Temps d'interactivité</strong> : Idéalement &lt; 1s, acceptable &lt; 3s
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="resources" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Ressources chargées</h3>

              {resourceMetrics.length === 0 ? (
                <div className="flex justify-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Aucune ressource disponible. Lancez l'analyse pour voir les résultats.
                  </p>
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="text-left p-2 pl-4">URL</th>
                          <th className="text-center p-2">Type</th>
                          <th className="text-right p-2">Taille</th>
                          <th className="text-right p-2 pr-4">Durée</th>
                        </tr>
                      </thead>
                      <tbody>
                        {resourceMetrics.map((resource, index) => (
                          <tr
                            key={index}
                            className={`border-b last:border-0 ${
                              resource.status === "bad"
                                ? "bg-red-50"
                                : resource.status === "warning"
                                  ? "bg-amber-50"
                                  : ""
                            }`}
                          >
                            <td className="p-2 pl-4 text-xs truncate max-w-[200px]" title={resource.url}>
                              {resource.url.split("/").pop() || resource.url}
                            </td>
                            <td className="text-center p-2">
                              <span className={`text-xs px-2 py-1 rounded ${getTypeStyle(resource.type)}`}>
                                {resource.type}
                              </span>
                            </td>
                            <td className="text-right p-2 text-sm">{formatSize(resource.size)}</td>
                            <td
                              className={`text-right p-2 pr-4 text-sm font-medium ${
                                resource.status === "good"
                                  ? "text-green-600"
                                  : resource.status === "warning"
                                    ? "text-amber-600"
                                    : "text-red-600"
                              }`}
                            >
                              {Math.round(resource.duration)} ms
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {resourceMetrics.length > 0 && (
                <div className="mt-4 border rounded-lg p-4 bg-amber-50 border-amber-200">
                  <h4 className="font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2 text-amber-600" />
                    Ressources à optimiser
                  </h4>
                  <ul className="mt-2 space-y-1 text-sm">
                    {resourceMetrics
                      .filter((r) => r.status !== "good")
                      .slice(0, 5)
                      .map((resource, index) => (
                        <li key={index} className="flex items-center justify-between">
                          <span className="truncate max-w-[300px]" title={resource.url}>
                            {resource.url.split("/").pop() || resource.url}
                          </span>
                          <span className="text-xs">
                            {formatSize(resource.size)} - {Math.round(resource.duration)} ms
                          </span>
                        </li>
                      ))}
                  </ul>
                </div>
              )}

              <div className="mt-4 border rounded-lg p-4 bg-blue-50 border-blue-200">
                <h4 className="font-medium flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-blue-600" />
                  Recommandations d'optimisation
                </h4>
                <ul className="mt-2 space-y-1 text-sm list-disc pl-5">
                  <li>Compressez les images (WebP, compression optimisée)</li>
                  <li>Utilisez le lazy loading pour les images hors écran</li>
                  <li>Minimisez et compressez les fichiers JS et CSS</li>
                  <li>Utilisez la mise en cache du navigateur avec des en-têtes appropriés</li>
                  <li>Réduisez les requêtes réseau en regroupant les ressources</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-xs text-muted-foreground">Les métriques sont basées sur l'API Performance du navigateur</p>
      </CardFooter>
    </Card>
  )
}

// Fonction pour obtenir le style en fonction du type de ressource
function getTypeStyle(type: string): string {
  switch (type) {
    case "script":
      return "bg-blue-100 text-blue-800"
    case "style":
      return "bg-purple-100 text-purple-800"
    case "image":
      return "bg-green-100 text-green-800"
    case "font":
      return "bg-yellow-100 text-yellow-800"
    case "api":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default PerformanceAnalyzer
