"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db } from "@/lib/firebase"
import { collection, query, orderBy, getDocs, doc, deleteDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Pencil, Trash2, Eye, EyeOff, Save } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
// Ajouter l'import pour le bouton d'historique
import { HistoryButton } from "@/components/history-button"
import { ContentType } from "@/lib/history-types"

interface PageItem {
  id: string
  title: string
  slug: string
  content: string
  isPublished: boolean
  createdAt: any
  updatedAt: any
  targetGroups: string[]
  displayOrder?: number
}

export default function PagesManagementPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [pages, setPages] = useState<PageItem[]>([])
  const [filteredPages, setFilteredPages] = useState<PageItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [orderValues, setOrderValues] = useState<Record<string, number>>({})
  const [isSaving, setIsSaving] = useState(false)
  // Ajout d'un état pour suivre l'élément en cours de suppression
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  useEffect(() => {
    fetchPages()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredPages(pages)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredPages(
        pages.filter(
          (item) =>
            item.title.toLowerCase().includes(lowercasedSearch) || item.slug.toLowerCase().includes(lowercasedSearch),
        ),
      )
    }
  }, [searchTerm, pages])

  // Initialiser les valeurs d'ordre lorsque les pages sont chargées
  useEffect(() => {
    const initialOrderValues: Record<string, number> = {}
    pages.forEach((item) => {
      initialOrderValues[item.id] = item.displayOrder || 0
    })
    setOrderValues(initialOrderValues)
  }, [pages])

  const fetchPages = async () => {
    try {
      setIsLoading(true)
      // Simplifier la requête pour éviter l'erreur d'index composite
      // Utiliser uniquement un tri par date de création
      const pagesQuery = query(collection(db(), "menuItems"), orderBy("createdAt", "desc"))
      const querySnapshot = await getDocs(pagesQuery)

      const pageItems: PageItem[] = []
      querySnapshot.forEach((doc) => {
        pageItems.push({
          id: doc.id,
          ...doc.data(),
        } as PageItem)
      })

      // Effectuer le tri côté client avec JavaScript
      const sortedItems = sortPagesList(pageItems)
      setPages(sortedItems)
      setFilteredPages(sortedItems)
    } catch (error) {
      console.error("Erreur lors du chargement des pages:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les pages",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Modification de la fonction handleDelete pour améliorer la gestion des erreurs et l'expérience utilisateur
  const handleDelete = async (id: string) => {
    try {
      // Définir l'état de suppression au début de l'opération
      setIsDeleting(id)

      // Mise à jour optimiste de l'interface utilisateur
      setPages((prevPages) => prevPages.filter((item) => item.id !== id))
      setFilteredPages((prevFilteredPages) => prevFilteredPages.filter((item) => item.id !== id))

      // Effectuer la suppression dans Firestore
      await deleteDoc(doc(db(), "menuItems", id))

      toast({
        title: "Succès",
        description: "La page a été supprimée avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la suppression de la page:", error)

      // Restaurer les données en cas d'erreur
      fetchPages()

      // Message d'erreur plus spécifique
      const errorMessage = error instanceof Error ? error.message : "Une erreur inconnue s'est produite"
      toast({
        title: "Erreur de suppression",
        description: `Impossible de supprimer la page: ${errorMessage}`,
        variant: "destructive",
      })
    } finally {
      // Réinitialiser l'état de suppression
      setIsDeleting(null)
    }
  }

  const togglePublished = async (id: string, currentState: boolean) => {
    try {
      await updateDoc(doc(db(), "menuItems", id), {
        isPublished: !currentState,
      })

      setPages((prev) => prev.map((item) => (item.id === id ? { ...item, isPublished: !currentState } : item)))

      toast({
        title: "Succès",
        description: `La page a été ${!currentState ? "publiée" : "dépubliée"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification de l'état de publication:", error)
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'état de publication",
        variant: "destructive",
      })
    }
  }

  const handleOrderChange = (id: string, value: string) => {
    const numValue = Number.parseInt(value, 10) || 0
    setOrderValues((prev) => ({
      ...prev,
      [id]: numValue,
    }))
  }

  const saveAllOrders = async () => {
    try {
      setIsSaving(true)

      // Créer un tableau de promesses pour toutes les mises à jour
      const updatePromises = pages.map((item) => {
        const newOrder = orderValues[item.id]
        if (newOrder !== item.displayOrder) {
          return updateDoc(doc(db(), "menuItems", item.id), {
            displayOrder: newOrder,
            updatedAt: serverTimestamp(),
          })
        }
        return Promise.resolve()
      })

      // Attendre que toutes les mises à jour soient terminées
      await Promise.all(updatePromises)

      // Mettre à jour l'état local avec les nouvelles valeurs d'ordre
      const updatedPages = pages.map((item) => ({
        ...item,
        displayOrder: orderValues[item.id],
      }))

      // Trier et mettre à jour l'état
      const sortedPages = sortPagesList(updatedPages)
      setPages(sortedPages)
      setFilteredPages(
        sortPagesList(
          filteredPages.map((item) => ({
            ...item,
            displayOrder: orderValues[item.id],
          })),
        ),
      )

      toast({
        title: "Succès",
        description: "Les ordres d'affichage ont été mis à jour avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des ordres d'affichage:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour les ordres d'affichage",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Fonction pour trier la liste des pages
  const sortPagesList = (pagesList: PageItem[]): PageItem[] => {
    return [...pagesList].sort((a, b) => {
      // Trier par ordre d'affichage
      const orderA = a.displayOrder !== undefined ? a.displayOrder : 0
      const orderB = b.displayOrder !== undefined ? b.displayOrder : 0
      if (orderA !== orderB) return orderA - orderB

      // Ensuite par date de création (plus récent d'abord)
      const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
      const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt)
      return dateB.getTime() - dateA.getTime()
    })
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
        <h1 className="text-3xl font-bold mb-4">Gestion des pages</h1>
        <div className="flex gap-2 w-full sm:w-auto">
          <Button onClick={saveAllOrders} disabled={isSaving} variant="outline">
            {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
            Enregistrer l'ordre
          </Button>
          <Button onClick={() => router.push("/admin/pages/create")}>
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle page
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <Input
            placeholder="Rechercher une page..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Titre</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Date de création</TableHead>
                  <TableHead>Dernière modification</TableHead>
                  <TableHead className="text-center">Ordre d'affichage</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      Aucune page trouvée
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPages.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.title}</TableCell>
                      <TableCell>{item.slug}</TableCell>
                      <TableCell>
                        <Badge
                          variant={item.isPublished ? "default" : "secondary"}
                          className={cn(
                            "font-medium",
                            item.isPublished
                              ? "bg-green-600 hover:bg-green-700"
                              : "bg-amber-500 hover:bg-amber-600 text-white",
                          )}
                        >
                          {item.isPublished ? "Publiée" : "Brouillon"}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(item.createdAt)}</TableCell>
                      <TableCell>{formatDate(item.updatedAt)}</TableCell>
                      <TableCell className="text-center">
                        <Input
                          type="number"
                          value={orderValues[item.id] || 0}
                          onChange={(e) => handleOrderChange(item.id, e.target.value)}
                          className="w-20 mx-auto text-center"
                          min="0"
                        />
                      </TableCell>
                      {/* Modifier la section des actions dans le tableau pour inclure le bouton d'historique */}
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => togglePublished(item.id, item.isPublished)}
                            title={item.isPublished ? "Dépublier" : "Publier"}
                          >
                            {item.isPublished ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>

                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => router.push(`/admin/pages/edit/${item.id}`)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>

                          <HistoryButton contentId={item.id} contentType={ContentType.PAGE} size="icon" />

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Êtes-vous sûr de vouloir supprimer cette page ? Cette action est irréversible.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuler</AlertDialogCancel>
                                {/* Modification du bouton de suppression pour corriger le problème d'événement */}
                                <AlertDialogAction
                                  onClick={(e) => {
                                    e.preventDefault() // Empêcher le comportement par défaut
                                    handleDelete(item.id)
                                  }}
                                  disabled={isDeleting === item.id} // Désactiver pendant la suppression
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  {isDeleting === item.id ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Suppression...
                                    </>
                                  ) : (
                                    "Supprimer"
                                  )}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
