"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Pencil, Trash2, Plus } from "lucide-react"
import { getCommercialContacts, deleteCommercialContact } from "@/lib/commercial-utils"
import { getDepartmentName } from "@/lib/commercial-types"
import type { CommercialContact } from "@/lib/commercial-types"
import { useToast } from "@/components/ui/use-toast"

export default function CommerciauxPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [commercials, setCommercials] = useState<CommercialContact[]>([])
  const [loading, setLoading] = useState(true)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [commercialToDelete, setCommercialToDelete] = useState<CommercialContact | null>(null)

  useEffect(() => {
    fetchCommercials()
  }, [])

  const fetchCommercials = async () => {
    setLoading(true)
    try {
      const data = await getCommercialContacts()
      setCommercials(data)
    } catch (error) {
      console.error("Error fetching commercials:", error)
      toast({
        title: "Erreur",
        description: "Impossible de récupérer la liste des commerciaux.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteClick = (commercial: CommercialContact) => {
    setCommercialToDelete(commercial)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!commercialToDelete?.id) return

    try {
      await deleteCommercialContact(commercialToDelete.id)
      setCommercials(commercials.filter((c) => c.id !== commercialToDelete.id))
      toast({
        title: "Succès",
        description: "Le contact commercial a été supprimé.",
      })
    } catch (error) {
      console.error("Error deleting commercial:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le contact commercial.",
        variant: "destructive",
      })
    } finally {
      setDeleteDialogOpen(false)
      setCommercialToDelete(null)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des contacts commerciaux</h1>
        <Button onClick={() => router.push("/admin/commerciaux/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Ajouter un contact
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des contacts commerciaux</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-40 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : commercials.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucun contact commercial n'a été ajouté.</p>
              <Button variant="outline" className="mt-4" onClick={() => router.push("/admin/commerciaux/create")}>
                <Plus className="mr-2 h-4 w-4" />
                Ajouter un contact
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Photo</TableHead>
                  <TableHead>Nom</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Téléphone</TableHead>
                  <TableHead>Départements</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {commercials.map((commercial) => {
                  const initials = commercial.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()
                    .substring(0, 2)

                  return (
                    <TableRow key={commercial.id}>
                      <TableCell>
                        <Avatar className="h-10 w-10">
                          {commercial.photoURL ? (
                            <AvatarImage src={commercial.photoURL || "/placeholder.svg"} alt={commercial.name} />
                          ) : (
                            <AvatarFallback>{initials}</AvatarFallback>
                          )}
                        </Avatar>
                      </TableCell>
                      <TableCell className="font-medium">{commercial.name}</TableCell>
                      <TableCell>{commercial.email}</TableCell>
                      <TableCell>{commercial.phone}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {commercial.departments.slice(0, 3).map((dept) => (
                            <Badge key={dept} variant="outline" className="text-xs">
                              {dept} - {getDepartmentName(dept)}
                            </Badge>
                          ))}
                          {commercial.departments.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{commercial.departments.length - 3}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => router.push(`/admin/commerciaux/edit/${commercial.id}`)}
                          >
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Modifier</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                            onClick={() => handleDeleteClick(commercial)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Supprimer</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer ce contact ?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action est irréversible. Le contact commercial sera définitivement supprimé.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-500 hover:bg-red-600">
              Supprimer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
