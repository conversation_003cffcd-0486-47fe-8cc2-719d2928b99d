"use client"

import { useState, useEffect, useRef } from "react"

type BeforeInstallPromptEvent = Event & {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: "accepted" | "dismissed"; platform: string }>
}

export function usePWAInstall() {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isInstalled, setIsInstalled] = useState<boolean>(false)
  const [isIOS, setIsIOS] = useState<boolean>(false)
  const [isWindows, setIsWindows] = useState<boolean>(false)
  const [showIOSGuide, setShowIOSGuide] = useState<boolean>(false)
  const promptEventRef = useRef<BeforeInstallPromptEvent | null>(null)

  useEffect(() => {
    // Détection de la plateforme
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOSDevice = /ipad|iphone|ipod/.test(userAgent) && !(window as any).MSStream
    const isWindowsDevice = /windows/.test(userAgent)

    setIsIOS(isIOSDevice)
    setIsWindows(isWindowsDevice)

    console.log("Plateforme détectée:", {
      isIOS: isIOSDevice,
      isWindows: isWindowsDevice,
      userAgent,
    })

    // Vérifier si l'app est déjà installée
    const checkIfInstalled = () => {
      // Méthode 1: Vérifier le mode d'affichage
      if (
        window.matchMedia("(display-mode: standalone)").matches ||
        window.matchMedia("(display-mode: fullscreen)").matches ||
        window.matchMedia("(display-mode: minimal-ui)").matches ||
        (window.navigator as any).standalone === true
      ) {
        console.log("Application détectée comme installée")
        setIsInstalled(true)
        return true
      }
      console.log("Application non installée")
      return false
    }

    // Vérifier immédiatement
    const installed = checkIfInstalled()

    if (!installed) {
      // Vérifier si un événement a déjà été capturé (pour les rechargements de page)
      if (promptEventRef.current) {
        setInstallPrompt(promptEventRef.current)
        console.log("Utilisation d'un événement beforeinstallprompt précédemment capturé")
      }

      // Capturer l'événement beforeinstallprompt
      const handleBeforeInstallPrompt = (e: Event) => {
        e.preventDefault()
        const promptEvent = e as BeforeInstallPromptEvent
        setInstallPrompt(promptEvent)
        promptEventRef.current = promptEvent
        console.log("Événement beforeinstallprompt capturé avec succès")
      }

      console.log("Ajout de l'écouteur d'événement beforeinstallprompt")
      window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

      // Vérifier à nouveau après l'installation
      window.addEventListener("appinstalled", () => {
        setIsInstalled(true)
        setInstallPrompt(null)
        promptEventRef.current = null
        console.log("Application installée avec succès")
      })

      return () => {
        window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
      }
    }
  }, [])

  // Fonction pour déclencher l'installation
  const promptInstall = async () => {
    console.log("Tentative d'installation:", {
      isIOS,
      isWindows,
      hasInstallPrompt: !!installPrompt,
    })

    if (isIOS) {
      console.log("Affichage du guide iOS")
      setShowIOSGuide(true)
      return
    }

    if (installPrompt) {
      try {
        console.log("Déclenchement du prompt d'installation")
        await installPrompt.prompt()
        const choiceResult = await installPrompt.userChoice

        if (choiceResult.outcome === "accepted") {
          console.log("Utilisateur a accepté l'installation")
          setInstallPrompt(null)
          promptEventRef.current = null
        } else {
          console.log("Utilisateur a refusé l'installation")
        }
      } catch (error) {
        console.error("Erreur lors de l'installation:", error)
      }
    } else {
      console.log("Aucun prompt d'installation disponible")

      // Sur Windows, ouvrir une page d'aide si le prompt n'est pas disponible
      if (isWindows) {
        showWindowsInstallGuide()
      }

      // Sur iOS, montrer le guide même si on n'a pas détecté iOS plus tôt
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        setShowIOSGuide(true)
      }
    }
  }

  // Fonction pour afficher un guide d'installation pour Windows
  const showWindowsInstallGuide = () => {
    // Détecter le navigateur
    const isChrome =
      /chrome/.test(navigator.userAgent.toLowerCase()) && !/edge|edg/.test(navigator.userAgent.toLowerCase())
    const isEdge = /edge|edg/.test(navigator.userAgent.toLowerCase())
    const isFirefox = /firefox/.test(navigator.userAgent.toLowerCase())

    let message = "Pour installer cette application sur Windows:\n\n"

    if (isChrome) {
      message += "1. Cliquez sur les trois points (...) en haut à droite\n"
      message += "2. Sélectionnez 'Installer ACR Direct...'\n"
      message += "3. Suivez les instructions à l'écran"
    } else if (isEdge) {
      message += "1. Cliquez sur les trois points (...) en haut à droite\n"
      message += "2. Sélectionnez 'Applications' puis 'Installer cette application'\n"
      message += "3. Suivez les instructions à l'écran"
    } else if (isFirefox) {
      message += "Firefox ne prend pas encore en charge l'installation de PWA. Veuillez utiliser Chrome ou Edge."
    } else {
      message += "Veuillez utiliser Chrome ou Edge pour installer cette application."
    }

    alert(message)
  }

  // Fermer le guide iOS
  const closeIOSGuide = () => {
    setShowIOSGuide(false)
  }

  return {
    isInstallable: !!installPrompt || isIOS,
    isInstalled,
    promptInstall,
    isIOS,
    isWindows,
    showIOSGuide,
    closeIOSGuide,
  }
}
