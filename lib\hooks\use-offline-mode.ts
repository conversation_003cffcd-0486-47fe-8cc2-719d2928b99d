"use client"

import { useState, useEffect, useCallback } from "react"
import { getPendingOperations, syncPendingOperations, checkDatabaseStatus } from "../offline-sync"

export function useOfflineMode() {
  const [isOnline, setIsOnline] = useState<boolean>(typeof navigator !== "undefined" ? navigator.onLine : true)
  const [pendingOperations, setPendingOperations] = useState<number>(0)
  const [isSyncing, setIsSyncing] = useState<boolean>(false)
  const [dbInitialized, setDbInitialized] = useState<boolean>(false)

  // Fonction pour mettre à jour le nombre d'opérations en attente
  const updatePendingOperations = useCallback(async () => {
    try {
      const operations = await getPendingOperations()
      setPendingOperations(operations.length)
    } catch (error) {
      console.error("Erreur lors de la récupération des opérations en attente:", error)
    }
  }, [])

  // Fonction pour synchroniser manuellement
  const syncNow = useCallback(async () => {
    if (isSyncing || !isOnline) return

    setIsSyncing(true)
    try {
      await syncPendingOperations()
      await updatePendingOperations()
    } catch (error) {
      console.error("Erreur lors de la synchronisation:", error)
    } finally {
      setIsSyncing(false)
    }
  }, [isSyncing, isOnline, updatePendingOperations])

  // Vérifier l'état de la base de données
  useEffect(() => {
    const checkDb = async () => {
      try {
        const status = await checkDatabaseStatus()
        setDbInitialized(status.initialized)
        setPendingOperations(status.pendingOperationsCount)
      } catch (error) {
        console.error("Erreur lors de la vérification de l'état de la base de données:", error)
      }
    }

    checkDb()
  }, [])

  // Mettre à jour l'état de la connexion
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      syncNow()
    }

    const handleOffline = () => {
      setIsOnline(false)
    }

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    // Vérifier périodiquement les opérations en attente
    const interval = setInterval(updatePendingOperations, 30000) // Toutes les 30 secondes

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
      clearInterval(interval)
    }
  }, [syncNow, updatePendingOperations])

  return {
    isOnline,
    pendingOperations,
    isSyncing,
    syncNow,
    dbInitialized,
  }
}
