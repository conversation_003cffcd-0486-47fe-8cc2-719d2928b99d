import { useSync } from "@/lib/hooks/use-sync"

export function SyncIndicator() {
  const { isSyncing } = useSync()

  // Ne rendre que l'indicateur de chargement sans texte si nécessaire
  if (isSyncing) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return null
}
