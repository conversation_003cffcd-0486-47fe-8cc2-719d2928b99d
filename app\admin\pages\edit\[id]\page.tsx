"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { db, storage } from "@/lib/firebase"
import { doc, getDoc, updateDoc, serverTimestamp, collection, getDocs } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, ImagePlus } from "lucide-react"
import dynamic from "next/dynamic"
import { Switch } from "@/components/ui/switch"
import { useGroups } from "@/lib/hooks"
import { Skeleton } from "@/components/ui/skeleton"

// Ajouter l'import pour les fonctions d'historique
import { capturePageVersionBeforeUpdate } from "@/lib/history-utils"
// Ajouter l'import pour le bouton d'historique
import { HistoryButton } from "@/components/history-button"
import { ContentType } from "@/lib/history-types"

// Ajouter ces imports en haut du fichier
import { ContentTypeSelector, type ContentModelType } from "@/components/content-models/content-type-selector"
import { SingleImageModel } from "@/components/content-models/single-image-model"
import { GalleryModel, type GalleryImage } from "@/components/content-models/gallery-model"
import { CarouselModel, type CarouselSlide } from "@/components/content-models/carousel-model"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ContentModelRenderer } from "@/components/content-models/content-model-renderer"
// Add this import at the top with the other imports
import { GridModel } from "@/components/content-models/grid-model"

// Dynamically import the RichTextEditor with no SSR
const RichTextEditor = dynamic(() => import("@/components/rich-text-editor"), {
  ssr: false,
  loading: () => <Skeleton className="w-full h-64" />,
})

interface PageEditProps {
  params: {
    id: string
  }
}

export default function EditPagePage({ params }: PageEditProps) {
  const id = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [title, setTitle] = useState("")
  const [slug, setSlug] = useState("")
  const [originalSlug, setOriginalSlug] = useState("")
  const [content, setContent] = useState("")
  const [isPublished, setIsPublished] = useState(true)
  const [showFrame, setShowFrame] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])
  const [existingSlugs, setExistingSlugs] = useState<string[]>([])
  const { groups, loading: loadingGroups } = useGroups()

  // Ajouter ces états dans le composant, après les états existants
  const [contentType, setContentType] = useState<ContentModelType>("richtext")

  // Pour le modèle image unique
  const [singleImage, setSingleImage] = useState<File | null>(null)
  const [singleImageUrl, setSingleImageUrl] = useState<string | null>(null)
  const [singleImageCaption, setSingleImageCaption] = useState("")
  const [singleImageAltText, setSingleImageAltText] = useState("")

  // Pour le modèle galerie
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([])
  // Add this state in the component, after the other state declarations
  const [galleryColumns, setGalleryColumns] = useState<number>(4)

  // Pour le modèle carousel
  const [carouselSlides, setCarouselSlides] = useState<CarouselSlide[]>([])
  const [carouselAutoplay, setCarouselAutoplay] = useState(true)
  const [carouselInterval, setCarouselInterval] = useState(5)

  // Pour le modèle grille
  const [gridItems, setGridItems] = useState<any[]>([])

  // Icon upload
  const [icon, setIcon] = useState<File | null>(null)
  const [currentIconUrl, setCurrentIconUrl] = useState<string | null>(null)
  const [iconPreview, setIconPreview] = useState<string | null>(null)
  const iconInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    fetchExistingSlugs()
    fetchPageData()
  }, [id])

  const fetchExistingSlugs = async () => {
    try {
      const querySnapshot = await getDocs(collection(db(), "menuItems"))
      const slugs: string[] = []
      querySnapshot.forEach((doc) => {
        const data = doc.data()
        if (data.slug) {
          slugs.push(data.slug)
        }
      })
      setExistingSlugs(slugs)
    } catch (error) {
      console.error("Erreur lors du chargement des slugs existants:", error)
    }
  }

  const fetchPageData = async () => {
    try {
      const docRef = doc(db(), "menuItems", id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        setTitle(data.title || "")
        setSlug(data.slug || "")
        setOriginalSlug(data.slug || "")
        setContent(data.content || "")
        setIsPublished(data.isPublished !== false)
        setShowFrame(data.showFrame !== false)
        setCurrentIconUrl(data.iconUrl || null)
        setIconPreview(data.iconUrl || null)

        // Charger le type de contenu et les données du modèle
        setContentType(data.contentType || "richtext")

        if (data.contentType === "single-image" && data.modelData) {
          setSingleImageUrl(data.modelData.imageUrl || null)
          setSingleImageCaption(data.modelData.caption || "")
          setSingleImageAltText(data.modelData.altText || "")
        } else if (data.contentType === "gallery" && data.modelData && data.modelData.images) {
          setGalleryImages(
            data.modelData.images.map((img: any) => ({
              id: `existing-${Math.random().toString(36).substr(2, 9)}`,
              url: img.url,
              altText: img.altText || "",
            })),
          )
          // Add this line to load the columns setting
          setGalleryColumns(data.modelData.columns || 4)
        }
        // Add this in the else if block for grid content type
        else if (data.contentType === "grid" && data.modelData && data.modelData.items) {
          setGridItems(
            data.modelData.items.map((item: any) => ({
              id: `existing-${Math.random().toString(36).substr(2, 9)}`,
              title: item.title || "",
              description: item.description || "",
              imageUrl: item.imageUrl || "",
              link: item.link || "",
            })),
          )
        } else if (data.contentType === "carousel" && data.modelData) {
          if (data.modelData.slides) {
            setCarouselSlides(
              data.modelData.slides.map((slide: any) => ({
                id: `existing-${Math.random().toString(36).substr(2, 9)}`,
                url: slide.url,
                title: slide.title || "",
                description: slide.description || "",
              })),
            )
          }
          setCarouselAutoplay(data.modelData.autoplay !== false)
          setCarouselInterval(data.modelData.interval || 5)
        }
        // Handle target groups
        if (data.targetGroups && Array.isArray(data.targetGroups)) {
          if (data.targetGroups.includes("all") || data.targetGroups.length === 0) {
            setSelectedGroups([])
          } else {
            setSelectedGroups(data.targetGroups)
          }
        }
      } else {
        toast({
          title: "Erreur",
          description: "Page introuvable",
          variant: "destructive",
        })
        router.push("/admin/pages")
      }
    } catch (error) {
      console.error("Erreur lors du chargement de la page:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de la page",
        variant: "destructive",
      })
    } finally {
      setIsLoadingData(false)
    }
  }

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups((prev) => (prev.includes(groupId) ? prev.filter((id) => id !== groupId) : [...prev, groupId]))
  }

  const handleIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setIcon(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setIconPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Modifier la fonction handleSubmit pour capturer la version avant la mise à jour
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title || !slug) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    // Validate content only if content type is richtext
    if (contentType === "richtext" && !content) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir le contenu",
        variant: "destructive",
      })
      return
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
    if (!slugRegex.test(slug)) {
      toast({
        title: "Erreur",
        description: "Le slug doit contenir uniquement des lettres minuscules, des chiffres et des tirets",
        variant: "destructive",
      })
      return
    }

    // Check if slug already exists (only if changed)
    if (slug !== originalSlug && existingSlugs.includes(slug)) {
      toast({
        title: "Erreur",
        description: "Ce slug est déjà utilisé. Veuillez en choisir un autre",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Capturer la version actuelle avant modification
      await capturePageVersionBeforeUpdate(id, "Sauvegarde avant modification", ContentType.PAGE)

      // Prepare page data
      const pageData: any = {
        title,
        slug,
        content,
        isPublished,
        updatedAt: serverTimestamp(),
        targetGroups: selectedGroups.length > 0 ? selectedGroups : ["all"],
        showFrame: showFrame,
        iconUrl: currentIconUrl,
      }

      // Ajouter ce code dans la fonction handleSubmit, juste avant la mise à jour du document
      // Après la ligne "const pageData: any = {"
      let modelData = {}

      if (contentType === "single-image") {
        if (singleImage) {
          try {
            // Upload new image
            const fileExtension = singleImage.name.split(".").pop()
            const contentId = `single-image-${Date.now()}`
            const imageRef = ref(storage(), `pages/${params.id}/content/${contentId}.${fileExtension}`)
            await uploadBytes(imageRef, singleImage)
            const imageUrl = await getDownloadURL(imageRef)

            modelData = {
              imageUrl,
              caption: singleImageCaption,
              altText: singleImageAltText,
            }
          } catch (error) {
            console.error("Erreur lors du téléchargement de l'image:", error)
            toast({
              title: "Erreur",
              description: "Impossible de télécharger l'image du modèle",
              variant: "destructive",
            })
          }
        } else if (singleImageUrl) {
          // Keep existing image
          modelData = {
            imageUrl: singleImageUrl,
            caption: singleImageCaption,
            altText: singleImageAltText,
          }
        }
      } else if (contentType === "gallery" && galleryImages.length > 0) {
        try {
          // Upload all gallery images
          const uploadedImages = []

          for (const [index, image] of galleryImages.entries()) {
            if (image.file) {
              const fileExtension = image.file.name.split(".").pop()
              const contentId = `gallery-${Date.now()}`
              const imageRef = ref(storage(), `pages/${params.id}/content/${contentId}/${index}.${fileExtension}`)
              await uploadBytes(imageRef, image.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedImages.push({
                url: imageUrl,
                altText: image.altText,
              })
            } else {
              // Image already has a URL
              uploadedImages.push({
                url: image.url,
                altText: image.altText,
              })
            }
          }

          modelData = {
            images: uploadedImages,
            columns: galleryColumns, // Add the columns setting
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la galerie:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la galerie",
            variant: "destructive",
          })
        }
      }
      // Add this else if block for grid content type
      else if (contentType === "grid" && gridItems.length > 0) {
        try {
          // Upload all grid item images
          const processedItems = []

          for (const item of gridItems) {
            const processedItem: any = {
              title: item.title,
              description: item.description,
              link: item.link,
            }

            if (item.file) {
              const fileExtension = item.file.name.split(".").pop()
              const contentId = `grid-${Date.now()}`
              const imageRef = ref(storage(), `pages/${params.id}/content/${contentId}/${item.id}.${fileExtension}`)
              await uploadBytes(imageRef, item.file)
              const imageUrl = await getDownloadURL(imageRef)
              processedItem.imageUrl = imageUrl
            } else if (item.imageUrl) {
              processedItem.imageUrl = item.imageUrl
            }

            processedItems.push(processedItem)
          }

          modelData = {
            items: processedItems,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la grille:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la grille",
            variant: "destructive",
          })
        }
      } else if (contentType === "carousel" && carouselSlides.length > 0) {
        try {
          // Upload all carousel slides
          const uploadedSlides = []

          for (const [index, slide] of carouselSlides.entries()) {
            if (slide.file) {
              const fileExtension = slide.file.name.split(".").pop()
              const contentId = `carousel-${Date.now()}`
              const imageRef = ref(storage(), `pages/${params.id}/content/${contentId}/${index}.${fileExtension}`)
              await uploadBytes(imageRef, slide.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedSlides.push({
                url: imageUrl,
                title: slide.title,
                description: slide.description,
              })
            } else {
              // Slide already has a URL
              uploadedSlides.push({
                url: slide.url,
                title: slide.title,
                description: slide.description,
              })
            }
          }

          modelData = {
            slides: uploadedSlides,
            autoplay: carouselAutoplay,
            interval: carouselInterval,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images du carousel:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images du carousel",
            variant: "destructive",
          })
        }
      }

      // Ajouter ces propriétés à pageData
      pageData.contentType = contentType
      pageData.modelData = modelData

      // Upload new icon if exists
      if (icon) {
        // Delete old icon if exists
        if (currentIconUrl) {
          try {
            // Extract the path from the URL
            const urlPath = currentIconUrl.split("page-icons%2F")[1]?.split("?")[0]
            if (urlPath) {
              const oldIconRef = ref(storage(), `page-icons/${decodeURIComponent(urlPath)}`)
              await deleteObject(oldIconRef)
            }
          } catch (error) {
            console.error("Erreur lors de la suppression de l'ancienne icône:", error)
          }
        }

        // Upload new icon
        const iconRef = ref(storage(), `page-icons/${Date.now()}_${icon.name}`)
        await uploadBytes(iconRef, icon)
        const iconUrl = await getDownloadURL(iconRef)
        pageData.iconUrl = iconUrl
      }

      // Update document in Firestore
      const docRef = doc(db(), "menuItems", id)
      await updateDoc(docRef, pageData)

      toast({
        title: "Succès",
        description: "La page a été mise à jour avec succès",
      })

      router.push("/admin/pages")
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la page:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de la page",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="container mx-auto py-6 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const formData = {
    isPublished: isPublished,
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Modifier la page</h1>
        <HistoryButton contentId={id} contentType={ContentType.PAGE} />
      </div>

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Informations générales</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre *</Label>
              <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug *</Label>
              <div className="flex gap-2">
                <Input
                  id="slug"
                  value={slug}
                  onChange={(e) =>
                    setSlug(
                      e.target.value
                        .toLowerCase()
                        .replace(/\s+/g, "-")
                        .replace(/[^\w-]/g, ""),
                    )
                  }
                  required
                  placeholder="exemple-de-slug"
                />
              </div>
              <p className="text-sm text-muted-foreground">L'URL de la page sera: /pages/{slug}</p>
            </div>

            <div className="space-y-2">
              <Label>Icône de menu</Label>
              <div className="flex items-center gap-4">
                <Button type="button" variant="outline" onClick={() => iconInputRef.current?.click()}>
                  <ImagePlus className="mr-2 h-4 w-4" />
                  {currentIconUrl ? "Changer l'icône" : "Ajouter une icône"}
                </Button>
                <input ref={iconInputRef} type="file" accept="image/*" onChange={handleIconChange} className="hidden" />

                {iconPreview && (
                  <div className="relative w-16 h-16">
                    <img
                      src={iconPreview || "/placeholder.svg"}
                      alt="Aperçu de l'icône"
                      className="w-full h-full object-contain border rounded p-1"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                      onClick={() => {
                        setIcon(null)
                        setIconPreview(null)
                        setCurrentIconUrl(null)
                      }}
                    >
                      ×
                    </Button>
                  </div>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Cette icône apparaîtra à côté du titre dans le menu de navigation
              </p>
            </div>

            {/* Remplacer la section du contenu dans le formulaire par ceci */}
            <div className="space-y-4">
              <ContentTypeSelector value={contentType} onChange={setContentType} />

              <div className="border-t pt-4 mt-4">
                <Tabs defaultValue="content" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="content">Contenu</TabsTrigger>
                    <TabsTrigger value="preview">Aperçu</TabsTrigger>
                  </TabsList>
                  <TabsContent value="content">
                    {contentType === "richtext" && (
                      <div className="space-y-2">
                        <Label htmlFor="content">Contenu *</Label>
                        <RichTextEditor value={content} onChange={setContent} />
                      </div>
                    )}

                    {contentType === "single-image" && (
                      <SingleImageModel
                        imageUrl={singleImageUrl}
                        caption={singleImageCaption}
                        altText={singleImageAltText}
                        onImageChange={(file) => {
                          setSingleImage(file)
                          if (file) {
                            const url = URL.createObjectURL(file)
                            setSingleImageUrl(url)
                          }
                        }}
                        onCaptionChange={setSingleImageCaption}
                        onAltTextChange={setSingleImageAltText}
                      />
                    )}

                    {contentType === "gallery" && (
                      <GalleryModel
                        images={galleryImages}
                        onImagesChange={setGalleryImages}
                        columns={galleryColumns}
                        onColumnsChange={setGalleryColumns}
                      />
                    )}

                    {contentType === "carousel" && (
                      <CarouselModel
                        slides={carouselSlides}
                        onSlidesChange={setCarouselSlides}
                        autoplay={carouselAutoplay}
                        onAutoplayChange={setCarouselAutoplay}
                        interval={carouselInterval}
                        onIntervalChange={setCarouselInterval}
                      />
                    )}
                    {contentType === "grid" && <GridModel items={gridItems} onItemsChange={setGridItems} />}
                  </TabsContent>
                  <TabsContent value="preview">
                    <div className="border rounded-lg p-4">
                      <p className="text-sm text-muted-foreground mb-4">Aperçu du contenu</p>
                      {contentType === "richtext" ? (
                        <div
                          className="prose max-w-none prose-sm sm:prose dark:prose-invert"
                          dangerouslySetInnerHTML={{ __html: content }}
                        />
                      ) : contentType === "single-image" && singleImageUrl ? (
                        <ContentModelRenderer
                          contentType="single-image"
                          content={{
                            imageUrl: singleImageUrl,
                            caption: singleImageCaption,
                            altText: singleImageAltText,
                          }}
                        />
                      ) : contentType === "gallery" && galleryImages.length > 0 ? (
                        <ContentModelRenderer
                          contentType="gallery"
                          content={{
                            images: galleryImages,
                            columns: galleryColumns,
                          }}
                        />
                      ) : contentType === "carousel" && carouselSlides.length > 0 ? (
                        <ContentModelRenderer
                          contentType="carousel"
                          content={{
                            slides: carouselSlides,
                            autoplay: carouselAutoplay,
                            interval: carouselInterval,
                          }}
                        />
                      ) : contentType === "grid" && gridItems.length > 0 ? (
                        <ContentModelRenderer contentType="grid" content={{ items: gridItems }} />
                      ) : (
                        <div className="text-center p-8 text-muted-foreground">
                          Aucun aperçu disponible pour ce type de contenu
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Paramètres de publication</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="published">Publié</Label>
              <Switch id="published" checked={isPublished} onCheckedChange={setIsPublished} />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="showFrame">Afficher avec un cadre</Label>
              <Switch id="showFrame" checked={showFrame} onCheckedChange={setShowFrame} />
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Visibilité par groupe</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingGroups ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center space-x-2 mb-4">
                  <Switch
                    id="all-groups"
                    checked={selectedGroups.length === 0}
                    onCheckedChange={(checked) => {
                      if (checked) setSelectedGroups([])
                    }}
                  />
                  <Label htmlFor="all-groups">Tous les groupes</Label>
                </div>

                {groups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <Switch
                      id={`group-${group.id}`}
                      checked={selectedGroups.includes(group.id)}
                      onCheckedChange={() => handleGroupToggle(group.id)}
                      disabled={selectedGroups.length === 0}
                    />
                    <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.push("/admin/pages")} disabled={isLoading}>
            Annuler
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Enregistrer les modifications
          </Button>
        </div>
      </form>
    </div>
  )
}
