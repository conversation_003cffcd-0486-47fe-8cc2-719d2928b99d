"use client"

import { useEffect, useState } from "react"
import { LoginForm } from "@/components/login-form"
import { registerServiceWorker } from "./sw-register"
import Link from "next/link"
import { ThemeToggle } from "@/components/theme-toggle"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { FallbackLogo } from "@/components/fallback-logo"
import { auth } from "@/lib/firebase"

export default function Home() {
  const [logoError, setLogoError] = useState(false)

  useEffect(() => {
    console.log("Page d'accueil: Initialisation...")

    // Enregistrer le service worker
    try {
      registerServiceWorker()
    } catch (error) {
      console.warn("Impossible d'enregistrer le Service Worker:", error)
    }

    // Vérifier si l'utilisateur est déjà connecté
    const checkAuthStatus = async () => {
      try {
        const currentUser = auth().currentUser
        if (currentUser) {
          console.log("Utilisateur déjà connecté, redirection vers le dashboard...")
          window.location.href = "/dashboard"
        } else {
          console.log("Page d'accueil affichée - utilisateur non connecté")
        }
      } catch (error) {
        console.error("Erreur lors de la vérification de l'authentification:", error)
      }
    }

    checkAuthStatus()
  }, [])

  // Afficher directement le formulaire de connexion
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="w-full max-w-md space-y-6 z-10 -mt-8">
        <div className="text-center">
          <div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm inline-block">
              {!logoError ? (
                <Image
                  src="/logo-acr-direct.png"
                  alt="ACR Direct"
                  width={240}
                  height={80}
                  className="h-28 md:h-36 w-auto mx-auto"
                  onError={() => setLogoError(true)}
                  priority
                />
              ) : (
                <FallbackLogo className="h-28 md:h-36 w-auto mx-auto" />
              )}
            </div>
            <p className="text-muted-foreground mt-2 text-lg">Connectez-vous</p>
          </div>
        </div>

        <div className="bg-card border border-border shadow-lg rounded-xl p-6">
          <LoginForm />
        </div>

        <div className="bg-card/80 border border-border/70 rounded-xl p-5 shadow-sm w-full max-w-xs mx-auto text-center">
          <p className="text-muted-foreground text-sm mb-3">Vous n'avez pas de compte ?</p>
          <Button asChild variant="secondary" className="w-full hover:bg-secondary/90 transition-colors">
            <Link href="/register">Inscrivez-vous</Link>
          </Button>
        </div>
      </div>
    </main>
  )
}
