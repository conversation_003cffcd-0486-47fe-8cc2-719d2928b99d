"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db, storage } from "@/lib/firebase"
import { doc, getDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, ImagePlus, Calendar } from "lucide-react"
import dynamic from "next/dynamic"
import { Switch } from "@/components/ui/switch"
import { useGroups } from "@/lib/hooks"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// Ajouter l'import pour les fonctions d'historique
import { captureNewsVersionBeforeUpdate } from "@/lib/history-utils"
// Ajouter l'import pour le bouton d'historique
import { HistoryButton } from "@/components/history-button"
import { ContentType } from "@/lib/history-types"

// Add these imports at the top with the other imports
import { GridModel, type GridItem } from "@/components/content-models/grid-model"
import { ContentTypeSelector, type ContentModelType } from "@/components/content-models/content-type-selector"
import { SingleImageModel } from "@/components/content-models/single-image-model"
import { GalleryModel, type GalleryImage } from "@/components/content-models/gallery-model"
import { CarouselModel, type CarouselSlide } from "@/components/content-models/carousel-model"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ContentModelRenderer } from "@/components/content-models/content-model-renderer"

// Dynamically import the RichTextEditor with no SSR
const RichTextEditor = dynamic(() => import("@/components/rich-text-editor"), {
  ssr: false,
  loading: () => <Skeleton className="w-full h-64" />,
})

interface NewsEditPageProps {
  params: {
    id: string
  }
}

export default function NewsEditPage({ params }: NewsEditPageProps) {
  const id = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [title, setTitle] = useState("")
  const [summary, setSummary] = useState("")
  const [content, setContent] = useState("")
  const [image, setImage] = useState<File | null>(null)
  const [currentImageUrl, setCurrentImageUrl] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublished, setIsPublished] = useState(true)
  const [isPinned, setIsPinned] = useState(false)
  const [showFrame, setShowFrame] = useState(true)
  // Add new state variables for image visibility
  const [showThumbnail, setShowThumbnail] = useState(true)
  const [showContentImage, setShowContentImage] = useState(true)
  const [showPublicationDate, setShowPublicationDate] = useState(true)
  // Add new state variables for custom date
  const [useCustomDate, setUseCustomDate] = useState(false)
  const [customDate, setCustomDate] = useState<Date>(new Date())
  const [createdAt, setCreatedAt] = useState<Date | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { groups, loading: loadingGroups } = useGroups()

  // Add these states in the component, after the other state declarations
  const [contentType, setContentType] = useState<ContentModelType>("richtext")
  const [singleImage, setSingleImage] = useState<File | null>(null)
  const [singleImageUrl, setSingleImageUrl] = useState<string | null>(null)
  const [singleImageCaption, setSingleImageCaption] = useState("")
  const [singleImageAltText, setSingleImageAltText] = useState("")
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([])
  const [galleryColumns, setGalleryColumns] = useState<number>(4)
  const [carouselSlides, setCarouselSlides] = useState<CarouselSlide[]>([])
  const [carouselAutoplay, setCarouselAutoplay] = useState(true)
  const [carouselInterval, setCarouselInterval] = useState(5)
  const [gridItems, setGridItems] = useState<GridItem[]>([])

  useEffect(() => {
    const fetchNewsData = async () => {
      try {
        const docRef = doc(db(), "news", id)
        const docSnap = await getDoc(docRef)

        if (docSnap.exists()) {
          const data = docSnap.data()
          setTitle(data.title || "")
          setSummary(data.summary || "")
          setContent(data.content || "")
          setIsPublished(data.isPublished !== false)
          setIsPinned(data.isPinned || false)
          setShowFrame(data.showFrame !== false)
          setCurrentImageUrl(data.imageUrl || null)
          setImagePreview(data.imageUrl || null)
          // Set image visibility states from data or default to true if not set
          setShowThumbnail(data.showThumbnail !== false)
          setShowContentImage(data.showContentImage !== false)
          setShowPublicationDate(data.showPublicationDate !== false)

          // Set custom date states
          setUseCustomDate(data.useCustomDate || false)
          if (data.customDate) {
            // If it's a Firestore timestamp, convert to Date
            if (data.customDate.toDate) {
              setCustomDate(data.customDate.toDate())
            } else if (data.customDate instanceof Date) {
              setCustomDate(data.customDate)
            } else if (typeof data.customDate === "string") {
              setCustomDate(new Date(data.customDate))
            }
          }

          // Store the original creation date
          if (data.createdAt) {
            setCreatedAt(data.createdAt.toDate())
          }

          // Handle target groups
          if (data.targetGroups && Array.isArray(data.targetGroups)) {
            if (data.targetGroups.includes("all") || data.targetGroups.length === 0) {
              setSelectedGroups([])
            } else {
              setSelectedGroups(data.targetGroups)
            }
          }
          // Add this in the fetchNewsData function, after loading the content
          // Load content type and model data
          setContentType(data.contentType || "richtext")

          if (data.contentType === "single-image" && data.modelData) {
            setSingleImageUrl(data.modelData.imageUrl || null)
            setSingleImageCaption(data.modelData.caption || "")
            setSingleImageAltText(data.modelData.altText || "")
          } else if (data.contentType === "gallery" && data.modelData && data.modelData.images) {
            setGalleryImages(
              data.modelData.images.map((img: any) => ({
                id: `existing-${Math.random().toString(36).substr(2, 9)}`,
                url: img.url,
                altText: img.altText || "",
              })),
            )
            setGalleryColumns(data.modelData.columns || 4)
          } else if (data.contentType === "carousel" && data.modelData) {
            if (data.modelData.slides) {
              setCarouselSlides(
                data.modelData.slides.map((slide: any) => ({
                  id: `existing-${Math.random().toString(36).substr(2, 9)}`,
                  url: slide.url,
                  title: slide.title || "",
                  description: slide.description || "",
                })),
              )
            }
            setCarouselAutoplay(data.modelData.autoplay !== false)
            setCarouselInterval(data.modelData.interval || 5)
          } else if (data.contentType === "grid" && data.modelData && data.modelData.items) {
            setGridItems(
              data.modelData.items.map((item: any) => ({
                id: `existing-${Math.random().toString(36).substr(2, 9)}`,
                title: item.title || "",
                description: item.description || "",
                imageUrl: item.imageUrl || "",
                link: item.link || "",
              })),
            )
          }
        } else {
          toast({
            title: "Erreur",
            description: "Article introuvable",
            variant: "destructive",
          })
          router.push("/admin/news")
        }
      } catch (error) {
        console.error("Erreur lors du chargement de l'article:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les données de l'article",
          variant: "destructive",
        })
      } finally {
        setIsLoadingData(false)
      }
    }

    fetchNewsData()
  }, [id, router, toast])

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setImage(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups((prev) => (prev.includes(groupId) ? prev.filter((id) => id !== groupId) : [...prev, groupId]))
  }

  // Modifier la fonction handleSubmit pour capturer la version avant la mise à jour
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir le titre",
        variant: "destructive",
      })
      return
    }

    // Validate content only if content type is richtext
    if (contentType === "richtext" && !content) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir le contenu",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Capturer la version actuelle avant modification
      await captureNewsVersionBeforeUpdate(id, "Sauvegarde avant modification", ContentType.NEWS)

      // Prepare news data
      const newsData: any = {
        title,
        summary,
        content,
        isPublished,
        isPinned,
        updatedAt: serverTimestamp(),
        targetGroups: selectedGroups.length > 0 ? selectedGroups : ["all"],
        showFrame,
        // Add image visibility fields
        showThumbnail,
        showContentImage,
        showPublicationDate,
        imageUrl: currentImageUrl,
      }

      // Add custom date if enabled
      if (useCustomDate) {
        newsData.customDate = customDate
        newsData.useCustomDate = true
      } else {
        // If custom date was previously enabled but now disabled, remove it
        newsData.useCustomDate = false
        // We don't need to delete the customDate field, just mark it as not in use
      }

      // Upload new image if exists
      if (image) {
        // Delete old image if exists
        if (currentImageUrl) {
          try {
            // Extract the path from the URL
            const urlPath = currentImageUrl.split("news%2F")[1].split("?")[0]
            const oldImageRef = ref(storage(), `news/${decodeURIComponent(urlPath)}`)
            await deleteObject(oldImageRef)
          } catch (error) {
            console.error("Erreur lors de la suppression de l'ancienne image:", error)
          }
        }

        // Upload new image
        const newsImageRef = ref(storage(), `news/${Date.now()}_${image.name}`)
        await uploadBytes(newsImageRef, image)
        const imageUrl = await getDownloadURL(newsImageRef)
        newsData.imageUrl = imageUrl
      }

      // Add this in the handleSubmit function, before updating the document
      // Prepare model data based on content type
      let modelData = {}

      if (contentType === "single-image") {
        if (singleImage) {
          try {
            // Upload new image
            const fileExtension = singleImage.name.split(".").pop()
            const contentId = `single-image-${Date.now()}`
            const imageRef = ref(storage(), `news/${params.id}/content/${contentId}.${fileExtension}`)
            await uploadBytes(imageRef, singleImage)
            const imageUrl = await getDownloadURL(imageRef)

            modelData = {
              imageUrl,
              caption: singleImageCaption,
              altText: singleImageAltText,
            }
          } catch (error) {
            console.error("Erreur lors du téléchargement de l'image:", error)
            toast({
              title: "Erreur",
              description: "Impossible de télécharger l'image du modèle",
              variant: "destructive",
            })
          }
        } else if (singleImageUrl) {
          // Keep existing image
          modelData = {
            imageUrl: singleImageUrl,
            caption: singleImageCaption,
            altText: singleImageAltText,
          }
        }
      } else if (contentType === "gallery" && galleryImages.length > 0) {
        try {
          // Upload all gallery images
          const uploadedImages = []

          for (const [index, image] of galleryImages.entries()) {
            if (image.file) {
              const fileExtension = image.file.name.split(".").pop()
              const contentId = `gallery-${Date.now()}`
              const imageRef = ref(storage(), `news/${params.id}/content/${contentId}/${index}.${fileExtension}`)
              await uploadBytes(imageRef, image.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedImages.push({
                url: imageUrl,
                altText: image.altText,
              })
            } else {
              // Image already has a URL
              uploadedImages.push({
                url: image.url,
                altText: image.altText,
              })
            }
          }

          modelData = {
            images: uploadedImages,
            columns: galleryColumns,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la galerie:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la galerie",
            variant: "destructive",
          })
        }
      } else if (contentType === "carousel" && carouselSlides.length > 0) {
        try {
          // Upload all carousel slides
          const uploadedSlides = []

          for (const [index, slide] of carouselSlides.entries()) {
            if (slide.file) {
              const fileExtension = slide.file.name.split(".").pop()
              const contentId = `carousel-${Date.now()}`
              const imageRef = ref(storage(), `news/${params.id}/content/${contentId}/${index}.${fileExtension}`)
              await uploadBytes(imageRef, slide.file)
              const imageUrl = await getDownloadURL(imageRef)

              uploadedSlides.push({
                url: imageUrl,
                title: slide.title,
                description: slide.description,
              })
            } else {
              // Slide already has a URL
              uploadedSlides.push({
                url: slide.url,
                title: slide.title,
                description: slide.description,
              })
            }
          }

          modelData = {
            slides: uploadedSlides,
            autoplay: carouselAutoplay,
            interval: carouselInterval,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images du carousel:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images du carousel",
            variant: "destructive",
          })
        }
      } else if (contentType === "grid" && gridItems.length > 0) {
        try {
          // Upload all grid item images
          const processedItems = []

          for (const item of gridItems) {
            const processedItem: any = {
              title: item.title,
              description: item.description,
              link: item.link,
            }

            if (item.file) {
              const fileExtension = item.file.name.split(".").pop()
              const contentId = `grid-${Date.now()}`
              const imageRef = ref(storage(), `news/${params.id}/content/${contentId}/${item.id}.${fileExtension}`)
              await uploadBytes(imageRef, item.file)
              const imageUrl = await getDownloadURL(imageRef)
              processedItem.imageUrl = imageUrl
            } else if (item.imageUrl) {
              processedItem.imageUrl = item.imageUrl
            }

            processedItems.push(processedItem)
          }

          modelData = {
            items: processedItems,
          }
        } catch (error) {
          console.error("Erreur lors du téléchargement des images de la grille:", error)
          toast({
            title: "Erreur",
            description: "Impossible de télécharger les images de la grille",
            variant: "destructive",
          })
        }
      }

      // Add these properties to newsData
      newsData.contentType = contentType
      newsData.modelData = modelData

      // Update document in Firestore
      const docRef = doc(db(), "news", id)
      await updateDoc(docRef, newsData)

      toast({
        title: "Succès",
        description: "L'article a été mis à jour avec succès",
      })

      router.push("/admin/news")
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'article:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de l'article",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="container mx-auto py-6 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // Modifier la section du titre pour inclure le bouton d'historique
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Modifier l'article</h1>
        <HistoryButton contentId={id} contentType={ContentType.NEWS} />
      </div>

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Informations générales</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre *</Label>
              <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="summary">Résumé</Label>
              <Textarea id="summary" value={summary} onChange={(e) => setSummary(e.target.value)} rows={3} />
            </div>

            {/* Replace the content section in the form with this */}
            <div className="space-y-4">
              <ContentTypeSelector value={contentType} onChange={setContentType} />

              <div className="border-t pt-4 mt-4">
                <Tabs defaultValue="content" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="content">Contenu</TabsTrigger>
                    <TabsTrigger value="preview">Aperçu</TabsTrigger>
                  </TabsList>
                  <TabsContent value="content">
                    {contentType === "richtext" && (
                      <div className="space-y-2">
                        <Label htmlFor="content">Contenu *</Label>
                        <RichTextEditor value={content} onChange={setContent} />
                      </div>
                    )}

                    {contentType === "single-image" && (
                      <SingleImageModel
                        imageUrl={singleImageUrl}
                        caption={singleImageCaption}
                        altText={singleImageAltText}
                        onImageChange={(file) => {
                          setSingleImage(file)
                          if (file) {
                            const url = URL.createObjectURL(file)
                            setSingleImageUrl(url)
                          }
                        }}
                        onCaptionChange={setSingleImageCaption}
                        onAltTextChange={setSingleImageAltText}
                      />
                    )}

                    {contentType === "gallery" && (
                      <GalleryModel
                        images={galleryImages}
                        onImagesChange={setGalleryImages}
                        columns={galleryColumns}
                        onColumnsChange={setGalleryColumns}
                      />
                    )}

                    {contentType === "carousel" && (
                      <CarouselModel
                        slides={carouselSlides}
                        onSlidesChange={setCarouselSlides}
                        autoplay={carouselAutoplay}
                        onAutoplayChange={setCarouselAutoplay}
                        interval={carouselInterval}
                        onIntervalChange={setCarouselInterval}
                      />
                    )}

                    {contentType === "grid" && <GridModel items={gridItems} onItemsChange={setGridItems} />}
                  </TabsContent>
                  <TabsContent value="preview">
                    <div className="border rounded-lg p-4">
                      <p className="text-sm text-muted-foreground mb-4">Aperçu du contenu</p>
                      {contentType === "richtext" ? (
                        <div
                          className="prose max-w-none prose-sm sm:prose dark:prose-invert"
                          dangerouslySetInnerHTML={{ __html: content }}
                        />
                      ) : contentType === "single-image" && singleImageUrl ? (
                        <ContentModelRenderer
                          contentType="single-image"
                          content={{
                            imageUrl: singleImageUrl,
                            caption: singleImageCaption,
                            altText: singleImageAltText,
                          }}
                        />
                      ) : contentType === "gallery" && galleryImages.length > 0 ? (
                        <ContentModelRenderer
                          contentType="gallery"
                          content={{
                            images: galleryImages,
                            columns: galleryColumns,
                          }}
                        />
                      ) : contentType === "carousel" && carouselSlides.length > 0 ? (
                        <ContentModelRenderer
                          contentType="carousel"
                          content={{
                            slides: carouselSlides,
                            autoplay: carouselAutoplay,
                            interval: carouselInterval,
                          }}
                        />
                      ) : contentType === "grid" && gridItems.length > 0 ? (
                        <ContentModelRenderer contentType="grid" content={{ items: gridItems }} />
                      ) : (
                        <div className="text-center p-8 text-muted-foreground">
                          Aucun aperçu disponible pour ce type de contenu
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Image</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>
                <ImagePlus className="mr-2 h-4 w-4" />
                {currentImageUrl ? "Changer l'image" : "Ajouter une image"}
              </Button>
              <input ref={fileInputRef} type="file" accept="image/*" onChange={handleImageChange} className="hidden" />

              {imagePreview && (
                <div className="relative w-32 h-32">
                  <img
                    src={imagePreview || "/placeholder.svg"}
                    alt="Aperçu"
                    className="w-full h-full object-cover rounded-md"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 rounded-full w-6 h-6 p-0"
                    onClick={() => {
                      setImage(null)
                      setImagePreview(null)
                      setCurrentImageUrl(null)
                    }}
                  >
                    ×
                  </Button>
                </div>
              )}
            </div>

            {/* Add image visibility controls */}
            {(currentImageUrl || imagePreview) && (
              <div className="space-y-3 mt-4 border-t pt-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="showThumbnail" className="flex-1">
                    Afficher la miniature dans la liste des actualités
                    <p className="text-sm text-muted-foreground mt-1">
                      Si désactivé, l'image ne sera pas affichée dans la liste des actualités
                    </p>
                  </Label>
                  <Switch id="showThumbnail" checked={showThumbnail} onCheckedChange={setShowThumbnail} />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="showContentImage" className="flex-1">
                    Afficher l'image dans le contenu de l'article
                    <p className="text-sm text-muted-foreground mt-1">
                      Si désactivé, l'image ne sera pas affichée dans le contenu de l'article
                    </p>
                  </Label>
                  <Switch id="showContentImage" checked={showContentImage} onCheckedChange={setShowContentImage} />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="showPublicationDate" className="flex-1">
                    Afficher la date de publication
                    <p className="text-sm text-muted-foreground mt-1">
                      Si désactivé, la date de publication ne sera pas affichée sur la carte d'actualité
                    </p>
                  </Label>
                  <Switch
                    id="showPublicationDate"
                    checked={showPublicationDate}
                    onCheckedChange={setShowPublicationDate}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Paramètres de publication</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="published">Publié</Label>
              <Switch id="published" checked={isPublished} onCheckedChange={setIsPublished} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="pinned">Épinglé</Label>
              <Switch id="pinned" checked={isPinned} onCheckedChange={setIsPinned} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="showFrame">Afficher avec un cadre</Label>
              <Switch id="showFrame" checked={showFrame} onCheckedChange={setShowFrame} />
            </div>

            <Badge
              className={cn(
                "font-medium",
                isPublished ? "bg-green-600 hover:bg-green-700" : "bg-amber-500 hover:bg-amber-600 text-white",
              )}
            >
              {isPublished ? "Publié" : "Brouillon"}
            </Badge>

            {/* Add custom date section */}
            <div className="border-t pt-4 mt-4">
              <div className="flex items-center justify-between mb-4">
                <Label htmlFor="useCustomDate" className="flex-1">
                  Utiliser une date personnalisée
                  <p className="text-sm text-muted-foreground mt-1">
                    Remplacer la date de publication automatique par une date personnalisée
                  </p>
                </Label>
                <Switch id="useCustomDate" checked={useCustomDate} onCheckedChange={setUseCustomDate} />
              </div>

              {useCustomDate && (
                <div className="flex flex-col space-y-2">
                  <Label htmlFor="customDate">Date personnalisée</Label>
                  <div className="flex items-center gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !customDate && "text-muted-foreground",
                          )}
                        >
                          <Calendar className="mr-2 h-4 w-4" />
                          {customDate ? format(customDate, "dd MMMM yyyy", { locale: fr }) : "Sélectionner une date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={customDate}
                          onSelect={(date) => date && setCustomDate(date)}
                          initialFocus
                          locale={fr}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  {createdAt && (
                    <p className="text-sm text-muted-foreground">
                      Date originale: {format(createdAt, "dd MMMM yyyy", { locale: fr })}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground italic">
                    Cette date sera affichée à la place de la date de création automatique
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Visibilité par groupe</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingGroups ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center space-x-2 mb-4">
                  <Switch
                    id="all-groups"
                    checked={selectedGroups.length === 0}
                    onCheckedChange={(checked) => {
                      if (checked) setSelectedGroups([])
                    }}
                  />
                  <Label htmlFor="all-groups">Tous les groupes</Label>
                </div>

                {groups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <Switch
                      id={`group-${group.id}`}
                      checked={selectedGroups.includes(group.id)}
                      onCheckedChange={() => handleGroupToggle(group.id)}
                      disabled={selectedGroups.length === 0}
                    />
                    <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.push("/admin/news")} disabled={isLoading}>
            Annuler
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Enregistrer les modifications
          </Button>
        </div>
      </form>
    </div>
  )
}
