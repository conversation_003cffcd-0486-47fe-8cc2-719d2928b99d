/**
 * Wrapper pour les opérations Firestore avec mise en cache automatique
 * Ce module fournit des fonctions qui encapsulent les opérations Firestore
 * et gèrent automatiquement la mise en cache des résultats
 */

import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  getDoc,
  doc,
  setDoc,
  deleteDoc,
  onSnapshot,
  type DocumentData,
  type QueryConstraint,
  type WhereFilterOp,
  type OrderByDirection,
  type Unsubscribe,
  serverTimestamp,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { cacheService, CACHE_DURATIONS, type CacheOptions } from "@/lib/cache-service"
import { startMeasure, endMeasure } from "@/lib/performance"

// Types pour les paramètres de requête
export interface QueryParams {
  where?: [string, WhereFilterOp, any][]
  orderBy?: [string, OrderByDirection][]
  limit?: number
}

/**
 * Récupère une collection Firestore avec mise en cache
 * @param path Chemin de la collection
 * @param queryParams Paramètres de requête (where, orderBy, limit)
 * @param cacheOptions Options de cache
 * @returns Documents de la collection
 */
export async function getCollectionWithCache<T = DocumentData>(
  path: string,
  queryParams: QueryParams = {},
  cacheOptions: CacheOptions = {},
): Promise<T[]> {
  try {
    startMeasure(`getCollectionWithCache-${path}`)

    // Générer la clé de cache
    const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)

    // Essayer de récupérer depuis le cache
    const cachedData = await cacheService.get<T[]>(cacheKey, cacheOptions)

    if (cachedData) {
      endMeasure(`getCollectionWithCache-${path}`)
      return cachedData
    }

    // Si pas en cache ou forceRefresh, récupérer depuis Firestore
    const constraints: QueryConstraint[] = []

    // Ajouter les conditions where
    if (queryParams.where) {
      queryParams.where.forEach(([field, operator, value]) => {
        constraints.push(where(field, operator, value))
      })
    }

    // Ajouter les conditions orderBy
    if (queryParams.orderBy) {
      queryParams.orderBy.forEach(([field, direction]) => {
        constraints.push(orderBy(field, direction))
      })
    }

    // Ajouter la limite
    if (queryParams.limit) {
      constraints.push(limit(queryParams.limit))
    }

    try {
      // Créer la requête
      const q = constraints.length > 0 ? query(collection(db(), path), ...constraints) : collection(db(), path)

      // Exécuter la requête
      const querySnapshot = await getDocs(q)

      // Traiter les résultats
      const documents = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as T[]

      // Mettre en cache les résultats
      await cacheService.set<T[]>(cacheKey, documents, {
        duration: cacheOptions.duration || CACHE_DURATIONS.MEDIUM,
        version: cacheOptions.version,
        source: "firestore",
      })

      endMeasure(`getCollectionWithCache-${path}`)
      return documents
    } catch (indexError: any) {
      // Si l'erreur est liée à un index manquant, essayer une requête simplifiée
      if (indexError.message && indexError.message.includes("index")) {
        console.warn(`Index error for ${path}, using simplified query:`, indexError.message)

        // Créer une requête simplifiée sans orderBy complexes
        const simpleConstraints: QueryConstraint[] = []

        // Garder uniquement les conditions where
        if (queryParams.where) {
          queryParams.where.forEach(([field, operator, value]) => {
            simpleConstraints.push(where(field, operator, value))
          })
        }

        // Ajouter un seul orderBy si nécessaire (généralement sur le nom)
        if (queryParams.orderBy && queryParams.orderBy.length > 0) {
          simpleConstraints.push(orderBy("__name__"))
        }

        // Ajouter la limite
        if (queryParams.limit) {
          simpleConstraints.push(limit(queryParams.limit))
        }

        const simpleQuery =
          simpleConstraints.length > 0 ? query(collection(db(), path), ...simpleConstraints) : collection(db(), path)

        const simpleSnapshot = await getDocs(simpleQuery)

        const simpleDocuments = simpleSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as T[]

        // Mettre en cache les résultats
        await cacheService.set<T[]>(cacheKey, simpleDocuments, {
          duration: cacheOptions.duration || CACHE_DURATIONS.MEDIUM,
          version: cacheOptions.version,
          source: "firestore-simplified",
        })

        endMeasure(`getCollectionWithCache-${path}`)
        return simpleDocuments
      } else {
        // Si ce n'est pas une erreur d'index, propager l'erreur
        throw indexError
      }
    }
  } catch (error) {
    console.error(`Erreur lors de la récupération de la collection ${path}:`, error)
    endMeasure(`getCollectionWithCache-${path}`)

    // En cas d'erreur, essayer de récupérer depuis le cache même si forceRefresh
    if (cacheOptions.forceRefresh) {
      const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)
      const cachedData = await cacheService.get<T[]>(cacheKey, { ...cacheOptions, forceRefresh: false })

      if (cachedData) {
        return cachedData
      }
    }

    // Si tout échoue, retourner un tableau vide
    return [] as T[]
  }
}

/**
 * Récupère un document Firestore avec mise en cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @param cacheOptions Options de cache
 * @returns Document ou null s'il n'existe pas
 */
export async function getDocumentWithCache<T = DocumentData>(
  path: string,
  id: string,
  cacheOptions: CacheOptions = {},
): Promise<T | null> {
  try {
    startMeasure(`getDocumentWithCache-${path}-${id}`)

    // Générer la clé de cache
    const cacheKey = cacheService.getDocumentCacheKey(path, id)

    // Essayer de récupérer depuis le cache
    const cachedData = await cacheService.get<T | null>(cacheKey, cacheOptions)

    if (cachedData !== null && cachedData !== undefined) {
      endMeasure(`getDocumentWithCache-${path}-${id}`)
      return cachedData
    }

    // Si pas en cache ou forceRefresh, récupérer depuis Firestore
    const docRef = doc(db(), path, id)
    const docSnap = await getDoc(docRef)

    let result: T | null = null

    if (docSnap.exists()) {
      result = {
        id: docSnap.id,
        ...docSnap.data(),
      } as T
    }

    // Mettre en cache le résultat (même si null)
    await cacheService.set<T | null>(cacheKey, result, {
      duration: cacheOptions.duration || CACHE_DURATIONS.MEDIUM,
      version: cacheOptions.version,
      source: "firestore",
    })

    endMeasure(`getDocumentWithCache-${path}-${id}`)
    return result
  } catch (error) {
    console.error(`Erreur lors de la récupération du document ${path}/${id}:`, error)
    endMeasure(`getDocumentWithCache-${path}-${id}`)

    // En cas d'erreur, essayer de récupérer depuis le cache même si forceRefresh
    if (cacheOptions.forceRefresh) {
      const cacheKey = cacheService.getDocumentCacheKey(path, id)
      const cachedData = await cacheService.get<T | null>(cacheKey, { ...cacheOptions, forceRefresh: false })

      if (cachedData !== null && cachedData !== undefined) {
        return cachedData
      }
    }

    return null
  }
}

/**
 * Crée ou met à jour un document Firestore avec invalidation du cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @param data Données du document
 * @param merge Fusionner avec les données existantes (true) ou remplacer (false)
 * @returns Promise<void>
 */
export async function setDocumentWithCache(path: string, id: string, data: any, merge = true): Promise<void> {
  try {
    startMeasure(`setDocumentWithCache-${path}-${id}`)

    // Ajouter un timestamp de modification
    const dataWithTimestamp = {
      ...data,
      updatedAt: serverTimestamp(),
    }

    // Si c'est une création, ajouter un timestamp de création
    if (!merge) {
      dataWithTimestamp.createdAt = serverTimestamp()
    }

    // Mettre à jour Firestore
    const docRef = doc(db(), path, id)
    await setDoc(docRef, dataWithTimestamp, { merge })

    // Invalider le cache pour ce document
    const docCacheKey = cacheService.getDocumentCacheKey(path, id)
    await cacheService.invalidate(docCacheKey)

    // Invalider également les caches de collection qui pourraient contenir ce document
    const collectionCachePrefix = cacheService.getCollectionCacheKey(path, {})
    await cacheService.invalidateByPrefix(collectionCachePrefix.split(":").slice(0, 2).join(":"))

    // Mettre à jour le cache avec les nouvelles données
    const newData = {
      id,
      ...dataWithTimestamp,
      // Convertir les timestamps serveur en timestamps JS pour le cache
      updatedAt: new Date(),
      ...(!merge ? { createdAt: new Date() } : {}),
    }

    await cacheService.set(docCacheKey, newData, { duration: CACHE_DURATIONS.MEDIUM })

    endMeasure(`setDocumentWithCache-${path}-${id}`)
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du document ${path}/${id}:`, error)
    endMeasure(`setDocumentWithCache-${path}-${id}`)
    throw error
  }
}

/**
 * Supprime un document Firestore avec invalidation du cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @returns Promise<void>
 */
export async function deleteDocumentWithCache(path: string, id: string): Promise<void> {
  try {
    startMeasure(`deleteDocumentWithCache-${path}-${id}`)

    // Supprimer de Firestore
    const docRef = doc(db(), path, id)
    await deleteDoc(docRef)

    // Invalider le cache pour ce document
    const docCacheKey = cacheService.getDocumentCacheKey(path, id)
    await cacheService.invalidate(docCacheKey)

    // Invalider également les caches de collection qui pourraient contenir ce document
    const collectionCachePrefix = cacheService.getCollectionCacheKey(path, {})
    await cacheService.invalidateByPrefix(collectionCachePrefix.split(":").slice(0, 2).join(":"))

    endMeasure(`deleteDocumentWithCache-${path}-${id}`)
  } catch (error) {
    console.error(`Erreur lors de la suppression du document ${path}/${id}:`, error)
    endMeasure(`deleteDocumentWithCache-${path}-${id}`)
    throw error
  }
}

/**
 * Écoute les changements d'un document avec mise à jour du cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @param callback Fonction appelée lors des changements
 * @returns Fonction pour arrêter l'écoute
 */
export function listenToDocumentWithCache<T = DocumentData>(
  path: string,
  id: string,
  callback: (data: T | null) => void,
): Unsubscribe {
  const docRef = doc(db(), path, id)

  return onSnapshot(
    docRef,
    async (docSnap) => {
      let result: T | null = null

      if (docSnap.exists()) {
        result = {
          id: docSnap.id,
          ...docSnap.data(),
        } as T
      }

      // Mettre à jour le cache
      const cacheKey = cacheService.getDocumentCacheKey(path, id)
      await cacheService.set<T | null>(cacheKey, result, {
        duration: CACHE_DURATIONS.MEDIUM,
        source: "firestore-listener",
      })

      // Appeler le callback
      callback(result)
    },
    (error) => {
      console.error(`Erreur dans l'écouteur du document ${path}/${id}:`, error)

      // En cas d'erreur, essayer de récupérer depuis le cache
      const cacheKey = cacheService.getDocumentCacheKey(path, id)
      cacheService.get<T | null>(cacheKey).then((cachedData) => {
        if (cachedData !== null && cachedData !== undefined) {
          callback(cachedData)
        }
      })
    },
  )
}

/**
 * Écoute les changements d'une collection avec mise à jour du cache
 * @param path Chemin de la collection
 * @param queryParams Paramètres de requête (where, orderBy, limit)
 * @param callback Fonction appelée lors des changements
 * @returns Fonction pour arrêter l'écoute
 */
export function listenToCollectionWithCache<T = DocumentData>(
  path: string,
  queryParams: QueryParams = {},
  callback: (data: T[]) => void,
): Unsubscribe {
  const constraints: QueryConstraint[] = []

  // Ajouter les conditions where
  if (queryParams.where) {
    queryParams.where.forEach(([field, operator, value]) => {
      constraints.push(where(field, operator, value))
    })
  }

  // Ajouter les conditions orderBy
  if (queryParams.orderBy) {
    try {
      queryParams.orderBy.forEach(([field, direction]) => {
        constraints.push(orderBy(field, direction))
      })
    } catch (error) {
      console.warn("Error adding orderBy constraints, using simplified query:", error)
      // Ajouter un seul orderBy sur le nom du document
      constraints.push(orderBy("__name__"))
    }
  }

  // Ajouter la limite
  if (queryParams.limit) {
    constraints.push(limit(queryParams.limit))
  }

  // Créer la requête
  const q = constraints.length > 0 ? query(collection(db(), path), ...constraints) : collection(db(), path)

  return onSnapshot(
    q,
    async (querySnapshot) => {
      // Traiter les résultats
      const documents = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as T[]

      // Mettre à jour le cache
      const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)
      await cacheService.set<T[]>(cacheKey, documents, {
        duration: CACHE_DURATIONS.MEDIUM,
        source: "firestore-listener",
      })

      // Appeler le callback
      callback(documents)
    },
    (error) => {
      console.error(`Erreur dans l'écouteur de la collection ${path}:`, error)

      // En cas d'erreur, essayer une requête simplifiée
      if (error.message && error.message.includes("index")) {
        console.warn(`Index error for ${path}, using simplified listener query`)

        // Annuler l'écouteur actuel
        const unsubscribe = () => {}

        // Créer une requête simplifiée sans orderBy complexes
        const simpleConstraints: QueryConstraint[] = []

        // Garder uniquement les conditions where
        if (queryParams.where) {
          queryParams.where.forEach(([field, operator, value]) => {
            simpleConstraints.push(where(field, operator, value))
          })
        }

        // Ajouter un seul orderBy sur le nom du document
        simpleConstraints.push(orderBy("__name__"))

        // Ajouter la limite
        if (queryParams.limit) {
          simpleConstraints.push(limit(queryParams.limit))
        }

        const simpleQuery = query(collection(db(), path), ...simpleConstraints)

        // Créer un nouvel écouteur avec la requête simplifiée
        onSnapshot(
          simpleQuery,
          async (simpleSnapshot) => {
            const simpleDocuments = simpleSnapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            })) as T[]

            // Mettre à jour le cache
            const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)
            await cacheService.set<T[]>(cacheKey, simpleDocuments, {
              duration: CACHE_DURATIONS.MEDIUM,
              source: "firestore-listener-simplified",
            })

            // Appeler le callback
            callback(simpleDocuments)
          },
          (simpleError) => {
            console.error(`Erreur dans l'écouteur simplifié de la collection ${path}:`, simpleError)

            // En cas d'erreur, essayer de récupérer depuis le cache
            const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)
            cacheService.get<T[]>(cacheKey).then((cachedData) => {
              if (cachedData) {
                callback(cachedData)
              } else {
                // Si pas de cache, retourner un tableau vide
                callback([] as T[])
              }
            })
          },
        )

        return unsubscribe
      }

      // En cas d'erreur, essayer de récupérer depuis le cache
      const cacheKey = cacheService.getCollectionCacheKey(path, queryParams)
      cacheService.get<T[]>(cacheKey).then((cachedData) => {
        if (cachedData) {
          callback(cachedData)
        } else {
          // Si pas de cache, retourner un tableau vide
          callback([] as T[])
        }
      })
    },
  )
}

/**
 * Précharge une collection dans le cache
 * @param path Chemin de la collection
 * @param queryParams Paramètres de requête (where, orderBy, limit)
 * @param cacheOptions Options de cache
 * @returns Promise<void>
 */
export async function preloadCollection(
  path: string,
  queryParams: QueryParams = {},
  cacheOptions: CacheOptions = {},
): Promise<void> {
  try {
    await getCollectionWithCache(path, queryParams, {
      ...cacheOptions,
      duration: cacheOptions.duration || CACHE_DURATIONS.LONG,
    })
  } catch (error) {
    console.error(`Erreur lors du préchargement de la collection ${path}:`, error)
    // Ne pas propager l'erreur pour éviter de bloquer le chargement de l'application
  }
}

/**
 * Précharge un document dans le cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @param cacheOptions Options de cache
 * @returns Promise<void>
 */
export async function preloadDocument(path: string, id: string, cacheOptions: CacheOptions = {}): Promise<void> {
  try {
    await getDocumentWithCache(path, id, {
      ...cacheOptions,
      duration: cacheOptions.duration || CACHE_DURATIONS.LONG,
    })
  } catch (error) {
    console.error(`Erreur lors du préchargement du document ${path}/${id}:`, error)
    // Ne pas propager l'erreur pour éviter de bloquer le chargement de l'application
  }
}
