"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import { LoadingScreen } from "./loading-screen"

interface SessionRedirectProps {
  children: React.ReactNode
}

export function SessionRedirect({ children }: SessionRedirectProps) {
  const { user, initialized } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [showLoginForm, setShowLoginForm] = useState(false)

  // Effet simple pour vérifier l'authentification et rediriger si nécessaire
  useEffect(() => {
    // Fonction pour rediriger vers le dashboard
    const redirectToDashboard = () => {
      console.log("SessionRedirect: Redirection vers le dashboard...")
      window.location.href = "/dashboard"
    }

    // Fonction pour afficher le formulaire de connexion
    const showForm = () => {
      console.log("SessionRedirect: Affichage du formulaire de connexion...")
      setIsLoading(false)
      setShowLoginForm(true)
    }

    // Vérification simple de la session côté serveur
    const checkSession = async () => {
      try {
        console.log("SessionRedirect: Vérification de la session...")

        // Vérifier d'abord si l'utilisateur est connecté côté client
        if (user) {
          console.log("SessionRedirect: Utilisateur connecté côté client, redirection...")
          redirectToDashboard()
          return
        }

        // Vérifier la session côté serveur
        try {
          const response = await fetch("/api/auth/check-session", {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "same-origin",
          })

          if (response.ok) {
            const data = await response.json()
            if (data.authenticated) {
              console.log("SessionRedirect: Session valide côté serveur, redirection...")
              redirectToDashboard()
              return
            }
          }
        } catch (error) {
          console.error("SessionRedirect: Erreur lors de la vérification de session côté serveur:", error)
        }

        // Sinon, afficher le formulaire de connexion
        showForm()
      } catch (error) {
        console.error("SessionRedirect: Erreur lors de la vérification de session:", error)
        showForm()
      }
    }

    // Définir un timeout de secours pour éviter de bloquer l'utilisateur
    const fallbackTimeout = setTimeout(() => {
      console.log("SessionRedirect: Timeout de secours déclenché")
      setIsLoading(false)
      setShowLoginForm(true)
    }, 2000)

    // Vérifier la session
    checkSession()

    // Nettoyage
    return () => {
      clearTimeout(fallbackTimeout)
    }
  }, [user, initialized])

  // Afficher un écran de chargement pendant la vérification
  if (isLoading) {
    return <LoadingScreen minimal={true} />
  }

  // Afficher le formulaire de connexion si nécessaire
  return showLoginForm ? <>{children}</> : null
}
