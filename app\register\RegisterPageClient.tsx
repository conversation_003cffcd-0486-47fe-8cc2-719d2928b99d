"use client"
import { useEffect, useState } from "react"
import { RegisterForm } from "@/components/register-form"
import Link from "next/link"
import { ThemeToggle } from "@/components/theme-toggle"
import { Button } from "@/components/ui/button"
import { SessionRedirect } from "@/components/session-redirect"

export default function RegisterPageClient() {
  // Effet pour vérifier la session côté serveur dès le chargement de la page
  useEffect(() => {
    // Vérifier immédiatement si une session existe côté serveur
    const checkServerSession = async () => {
      try {
        const response = await fetch("/api/auth/check-session")
        const data = await response.json()

        if (data.authenticated) {
          console.log("Page d'inscription: Session valide détectée, redirection...")
          window.location.href = "/dashboard"
        }
      } catch (error) {
        console.error("Page d'inscription: Erreur lors de la vérification de session:", error)
      }
    }

    checkServerSession()

    // Ajouter un script de secours pour la redirection
    const script = document.createElement("script")
    script.innerHTML = `
      // Script de secours pour la redirection
      setTimeout(async function() {
        try {
          const response = await fetch('/api/auth/check-session');
          const data = await response.json();
          if (data.authenticated) {
            console.log("Script de secours: Session valide détectée, redirection...");
            window.location.href = "/dashboard";
          }
        } catch (e) {
          console.error("Script de secours: Erreur:", e);
        }
      }, 1500);
    `
    document.body.appendChild(script)
  }, [])

  // État pour contrôler l'affichage du formulaire d'inscription
  const [showRegisterForm, setShowRegisterForm] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)

  // Effet pour vérifier si nous devons afficher le formulaire d'inscription
  useEffect(() => {
    let isMounted = true

    // Vérifier si une session existe déjà en localStorage
    const checkLocalSession = () => {
      try {
        // Vérifier si un token existe dans le localStorage
        const token = localStorage.getItem("auth_token")
        const tokenExpiry = localStorage.getItem("auth_token_expiry")

        if (token && tokenExpiry) {
          const expiryTime = Number.parseInt(tokenExpiry)

          // Si le token n'est pas expiré, rediriger immédiatement
          if (expiryTime > Date.now()) {
            console.log("Page d'inscription: Token local valide détecté, redirection...")
            setIsRedirecting(true)
            window.location.href = "/dashboard"
            return true
          }
        }

        return false
      } catch (error) {
        console.error("Erreur lors de la vérification du token local:", error)
        return false
      }
    }

    // Vérifier immédiatement si une session existe côté serveur
    const checkSession = async () => {
      try {
        // Vérifier d'abord le localStorage pour une redirection immédiate
        if (checkLocalSession()) {
          return
        }

        // Vérifier côté serveur
        const response = await fetch("/api/auth/check-session")
        const data = await response.json()

        if (data.authenticated) {
          // Session valide détectée, rediriger vers le dashboard
          console.log("Page d'inscription: Session valide détectée, redirection...")
          setIsRedirecting(true)
          window.location.href = "/dashboard"
          return
        }

        // Aucune session valide, afficher le formulaire immédiatement
        if (isMounted) {
          setShowRegisterForm(true)
        }
      } catch (error) {
        console.error("Erreur lors de la vérification de session:", error)
        // En cas d'erreur, afficher le formulaire
        if (isMounted) {
          setShowRegisterForm(true)
        }
      }
    }

    checkSession()

    return () => {
      isMounted = false
    }
  }, [])

  // Afficher un écran de chargement pendant la vérification
  if (!showRegisterForm) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
        <div className="absolute inset-0 overflow-hidden z-0">
          <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <div className="text-center z-10">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm inline-block">
            <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-28 md:h-36 w-auto mx-auto rounded-lg" />
          </div>

          {isRedirecting && (
            <div className="mt-6 flex flex-col items-center space-y-4">
              <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              <p className="text-muted-foreground">Connexion en cours...</p>
            </div>
          )}
        </div>
      </main>
    )
  }

  // Utiliser le composant SessionRedirect pour gérer la redirection automatique
  return (
    <SessionRedirect>
      <main className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
        <div className="absolute top-4 right-4 z-10">
          <ThemeToggle />
        </div>

        <div className="absolute inset-0 overflow-hidden z-0">
          <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <div className="w-full max-w-md space-y-6 z-10 -mt-8">
          <div className="text-center">
            <div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm inline-block">
                <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-28 md:h-36 w-auto mx-auto rounded-lg" />
              </div>
              <p className="text-muted-foreground mt-2 text-lg">Créez votre compte</p>
            </div>
          </div>

          <div className="bg-card border border-border shadow-lg rounded-xl p-6">
            <RegisterForm />
          </div>

          <div className="bg-card/80 border border-border/70 rounded-xl p-5 shadow-sm w-full max-w-xs mx-auto text-center">
            <p className="text-muted-foreground text-sm mb-3">Vous avez déjà un compte ?</p>
            <Button asChild variant="secondary" className="w-full hover:bg-secondary/90 transition-colors">
              <Link href="/">Connectez-vous</Link>
            </Button>
          </div>
        </div>
      </main>
    </SessionRedirect>
  )
}
