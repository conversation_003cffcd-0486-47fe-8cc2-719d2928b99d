"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"

interface GridItem {
  id: string
  title: string
  description?: string
  imageUrl?: string
  link?: string
}

interface GridViewerProps {
  items: GridItem[]
}

export function GridViewer({ items }: GridViewerProps) {
  if (items.length === 0) {
    return <div className="text-center p-8 text-muted-foreground">Aucun élément à afficher</div>
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item) => (
        <Card key={item.id} className="overflow-hidden">
          {item.imageUrl && (
            <div className="aspect-[16/9] overflow-hidden">
              <img
                src={item.imageUrl || "/placeholder.svg"}
                alt={item.title}
                className="w-full h-full object-cover transition-transform hover:scale-105"
              />
            </div>
          )}
          <CardContent className="p-4">
            <h3 className="text-lg font-medium mb-2">{item.title}</h3>
            {item.description && <p className="text-sm text-muted-foreground">{item.description}</p>}
          </CardContent>
          {item.link && (
            <CardFooter className="p-4 pt-0">
              <Button variant="outline" size="sm" asChild>
                <a href={item.link} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Voir plus
                </a>
              </Button>
            </CardFooter>
          )}
        </Card>
      ))}
    </div>
  )
}
