"use client"

/**
 * Hook React pour utiliser les documents Firestore avec cache
 * Ce hook gère automatiquement la mise en cache et la récupération des données
 */

import { useState, useEffect, useCallback } from "react"
import type { DocumentData } from "firebase/firestore"
import { getDocumentWithCache, listenToDocumentWithCache } from "@/lib/firestore-cache"
import { CACHE_DURATIONS, type CacheOptions } from "@/lib/cache-service"
import { useAuth } from "@/components/auth-provider"

interface UseCachedDocumentOptions extends CacheOptions {
  /** Écouter les changements en temps réel */
  listen?: boolean
  /** Recharger automatiquement les données lors des changements de dépendances */
  autoRefresh?: boolean
}

interface UseCachedDocumentResult<T> {
  /** Données du document */
  data: T | null
  /** Indique si les données sont en cours de chargement */
  loading: boolean
  /** Erreur éventuelle */
  error: Error | null
  /** Fonction pour rafraîchir les données */
  refresh: (forceRefresh?: boolean) => Promise<void>
  /** Indique si les données proviennent du cache */
  fromCache: boolean
}

/**
 * Hook pour utiliser un document Firestore avec cache
 * @param path Chemin de la collection
 * @param id ID du document
 * @param options Options de cache et d'écoute
 * @param dependencies Dépendances pour le rechargement automatique
 * @returns Résultat contenant les données, l'état de chargement, etc.
 */
export function useCachedDocument<T = DocumentData>(
  path: string,
  id: string | null | undefined,
  options: UseCachedDocumentOptions = {},
  dependencies: any[] = [],
): UseCachedDocumentResult<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)
  const [fromCache, setFromCache] = useState<boolean>(false)
  const { user } = useAuth()

  // Options par défaut
  const { listen = false, autoRefresh = true, duration = CACHE_DURATIONS.MEDIUM, forceRefresh = false } = options

  // Fonction pour charger les données
  const loadData = useCallback(
    async (force = false) => {
      if (!path || !id) {
        setLoading(false)
        return
      }

      setLoading(true)
      setError(null)

      try {
        const result = await getDocumentWithCache<T>(path, id, {
          duration,
          forceRefresh: force || forceRefresh,
        })

        setData(result)
        setFromCache(!force && !forceRefresh)
        setLoading(false)
      } catch (err) {
        console.error(`Error fetching document ${path}/${id}:`, err)
        setError(err as Error)
        setLoading(false)
      }
    },
    [path, id, duration, forceRefresh],
  )

  // Fonction de rafraîchissement exposée
  const refresh = useCallback(
    async (force = true) => {
      await loadData(force)
    },
    [loadData],
  )

  // Effet pour le chargement initial et l'écoute des changements
  useEffect(() => {
    // Ne pas charger si le chemin ou l'ID est vide ou si l'utilisateur n'est pas connecté
    if (!path || !id || !user) {
      setLoading(false)
      return () => {}
    }

    // Si on écoute les changements en temps réel
    if (listen) {
      setLoading(true)

      // Charger d'abord depuis le cache pour un affichage rapide
      getDocumentWithCache<T>(path, id, { forceRefresh: false })
        .then((cachedData) => {
          setData(cachedData)
          setFromCache(true)
          setLoading(false)
        })
        .catch((err) => {
          console.error(`Error fetching cached document ${path}/${id}:`, err)
        })

      // Puis configurer l'écouteur pour les mises à jour en temps réel
      const unsubscribe = listenToDocumentWithCache<T>(path, id, (newData) => {
        setData(newData)
        setFromCache(false)
        setLoading(false)
      })

      return () => {
        unsubscribe()
      }
    }
    // Sinon, charger une seule fois
    else {
      loadData(forceRefresh)
      return () => {}
    }
  }, [path, id, listen, user?.uid, ...(autoRefresh ? dependencies : [])])

  return {
    data,
    loading,
    error,
    refresh,
    fromCache,
  }
}
