import { auth } from "firebase-admin"
import { getFirebaseAdminApp } from "@/lib/server-auth"

export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET(request: Request) {
  try {
    // Initialiser Firebase Admin si ce n'est pas déjà fait
    getFirebaseAdminApp()

    // Extraire le token d'authentification de la requête
    const authHeader = request.headers.get("Authorization")
    const token = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : null

    // Si pas de token dans l'en-tête, vérifier les cookies
    const cookieHeader = request.headers.get("Cookie")
    const sessionCookie = cookieHeader
      ?.split(";")
      .find((cookie) => cookie.trim().startsWith("session="))
      ?.split("=")[1]

    let authenticated = false
    let userId = null

    // Vérifier le token si disponible
    if (token) {
      try {
        const decodedToken = await auth().verifyIdToken(token)
        authenticated = true
        userId = decodedToken.uid
      } catch (error) {
        console.error("Erreur de vérification du token:", error)
      }
    }

    // Vérifier le cookie de session si disponible et si le token n'a pas fonctionné
    if (!authenticated && sessionCookie) {
      try {
        const decodedCookie = await auth().verifySessionCookie(sessionCookie)
        authenticated = true
        userId = decodedCookie.uid
      } catch (error) {
        console.error("Erreur de vérification du cookie de session:", error)
      }
    }

    return new Response(JSON.stringify({ authenticated, userId }), {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-store, max-age=0, must-revalidate",
      },
    })
  } catch (error) {
    console.error("Erreur lors de la vérification de session:", error)
    return new Response(JSON.stringify({ authenticated: false, error: "Erreur serveur" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-store, max-age=0, must-revalidate",
      },
    })
  }
}
