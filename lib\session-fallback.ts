import { cookies } from "next/headers"
import { decodeJwt } from "jose"

// Interface pour représenter un utilisateur Firebase Auth
interface FirebaseUser {
  uid: string
  email?: string | null
  displayName?: string | null
  photoURL?: string | null
  emailVerified?: boolean
}

// Fonction de secours pour obtenir la session utilisateur
export async function getFallbackUserSession() {
  try {
    // Récupérer le cookie de session
    const sessionCookie = cookies().get("__session")

    if (!sessionCookie?.value) {
      return { user: null }
    }

    try {
      // Tenter de décoder le JWT sans vérifier la signature
      // Ceci est uniquement pour la récupération d'urgence
      const decodedToken = decodeJwt(sessionCookie.value)

      if (decodedToken && typeof decodedToken === "object" && "user_id" in decodedToken) {
        const user: FirebaseUser = {
          uid: decodedToken.user_id as string,
          email: (decodedToken.email as string) || null,
          displayName: (decodedToken.name as string) || null,
          emailVerified: (decodedToken.email_verified as boolean) || false,
        }

        return { user }
      }
    } catch (decodeError) {
      console.error("[Fallback Session] Erreur de décodage du token:", decodeError)
    }

    return { user: null }
  } catch (error) {
    console.error("[Fallback Session] Erreur:", error)
    return { user: null }
  }
}
