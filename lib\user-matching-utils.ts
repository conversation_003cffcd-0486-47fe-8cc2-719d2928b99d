/**
 * Normalise un code client en supprimant les caractères non numériques
 * et en formatant à 6 chiffres avec des zéros à gauche si nécessaire
 */
export function normalizeClientCode(clientCode: string | null | undefined): string {
  if (!clientCode) return ""
  const digitsOnly = clientCode.toString().replace(/\D/g, "")
  return digitsOnly.padStart(6, "0").slice(-6)
}

/**
 * Normalise les 5 derniers chiffres du SIRET en supprimant les caractères non numériques
 * et en formatant à 5 chiffres avec des zéros à gauche si nécessaire
 */
export function normalizeSiret(siret: string | null | undefined): string {
  if (!siret) return ""
  const digitsOnly = siret.toString().replace(/\D/g, "")
  return digitsOnly.padStart(5, "0").slice(-5)
}

/**
 * Compare deux SIRET normalisés
 */
export function siretMatch(siret1: string | null | undefined, siret2: string | null | undefined): boolean {
  return normalizeSiret(siret1) === normalizeSiret(siret2)
}

/**
 * Crée une clé de correspondance unique basée sur le code client et les 5 derniers chiffres du SIRET
 */
export function createMatchingKey(
  clientCode: string | null | undefined,
  siretLastDigits: string | null | undefined,
): string {
  const normalizedClientCode = normalizeClientCode(clientCode)
  const normalizedSiret = normalizeSiret(siretLastDigits)
  return `${normalizedClientCode}-${normalizedSiret}`
}
