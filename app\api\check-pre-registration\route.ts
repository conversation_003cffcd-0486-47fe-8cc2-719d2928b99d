import { NextResponse } from "next/server"
import { getFirestore, collection, getDocs } from "firebase/firestore"
import { app } from "@/lib/firebase"
import { normalizeClientCode, normalizeSiret } from "@/lib/user-matching-utils"

export async function POST(request: Request) {
  try {
    // Extraire les données de la requête
    const body = await request.json()
    const { clientCode, siretLastDigits } = body

    console.log("Requête reçue:", { clientCode, siretLastDigits })

    // Normaliser les entrées
    const normalizedClientCode = normalizeClientCode(clientCode)
    const normalizedSiret = normalizeSiret(siretLastDigits)

    try {
      // Utiliser getFirestore directement au lieu de db()
      const firestore = getFirestore(app)

      // Rechercher dans la collection importedUsers
      const importedUsersRef = collection(firestore, "importedUsers")
      const importedUsersSnapshot = await getDocs(importedUsersRef)

      let matchFound = false
      let matchedUser = null
      let matchType = "none"
      let matchCount = 0

      // Parcourir tous les utilisateurs importés
      importedUsersSnapshot.forEach((doc) => {
        const userData = doc.data()

        // Vérifier si le code client correspond
        const userClientCode = normalizeClientCode(userData.clientCode)
        const clientCodeMatch = userClientCode === normalizedClientCode

        // Vérifier si les 5 derniers chiffres du SIRET correspondent
        const userSiret = normalizeSiret(userData.siretLastDigits)
        const siretDigitsMatch = userSiret === normalizedSiret

        // Si les deux correspondent, c'est une correspondance exacte
        if (clientCodeMatch && siretDigitsMatch) {
          matchFound = true
          matchedUser = userData
          matchType = "exact"
          matchCount++
        }
        // Si seulement le code client correspond, c'est une correspondance partielle
        else if (clientCodeMatch) {
          if (!matchFound) {
            matchedUser = userData
            matchType = "partial_client"
            matchCount++
          }
        }
        // Si seulement le SIRET correspond, c'est une correspondance partielle
        else if (siretDigitsMatch) {
          if (!matchFound && matchType !== "partial_client") {
            matchedUser = userData
            matchType = "partial_siret"
            matchCount++
          }
        }
      })

      // Si aucune correspondance n'est trouvée, retourner une réponse négative
      if (!matchFound && matchType === "none") {
        return NextResponse.json({
          isConfirmed: false,
          groups: ["default"],
          clientType: "default",
          matchDetails: {
            matchType,
            matchCount,
          },
        })
      }

      // Si une correspondance est trouvée, retourner les informations de l'utilisateur
      const groups = matchedUser.additionalGroups
        ? typeof matchedUser.additionalGroups === "string"
          ? matchedUser.additionalGroups.split(",").map((g) => g.trim())
          : matchedUser.additionalGroups
        : []

      if (matchedUser.clientType && !groups.includes(matchedUser.clientType)) {
        groups.unshift(matchedUser.clientType)
      }

      return NextResponse.json({
        isConfirmed: matchType === "exact",
        groups: groups.length > 0 ? groups : ["default"],
        clientType: matchedUser.clientType || "default",
        matchDetails: {
          matchType,
          matchCount,
        },
      })
    } catch (firestoreError) {
      console.error("Erreur Firestore:", firestoreError)

      // En cas d'erreur Firestore, utiliser une solution de secours
      return NextResponse.json({
        isConfirmed: true, // Autoriser l'inscription par défaut en cas d'erreur
        groups: ["default"],
        clientType: "default",
        matchDetails: {
          matchType: "error",
          matchCount: 0,
          error: "Erreur de connexion à la base de données",
        },
      })
    }
  } catch (error) {
    console.error("Erreur dans la route API:", error)

    // En cas d'erreur, retourner une réponse d'erreur
    return NextResponse.json(
      {
        isConfirmed: false,
        groups: ["default"],
        clientType: "default",
        error: "Erreur de traitement de la requête",
        matchDetails: {
          matchType: "error",
          matchCount: 0,
        },
      },
      { status: 500 },
    )
  }
}
