"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Wifi, WifiOff, Check, X, AlertTriangle, Loader2, RefreshCw, Download } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { cacheService } from "@/lib/cache-service"
import { preloadAllData } from "@/lib/preload-service"
import { useAuth } from "@/lib/hooks/use-auth"

interface TestResult {
  name: string
  status: "success" | "error" | "warning" | "pending"
  message: string
  details?: string
}

export function OfflineTester() {
  const { user } = useAuth()
  const [isOnline, setIsOnline] = useState<boolean>(true)
  const [isSimulatingOffline, setIsSimulatingOffline] = useState<boolean>(false)
  const [isRunningTests, setIsRunningTests] = useState<boolean>(false)
  const [testProgress, setTestProgress] = useState<number>(0)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [activeTab, setActiveTab] = useState<string>("tester")
  const [cacheStats, setCacheStats] = useState<any>(null)

  // Vérifier l'état de la connexion
  useEffect(() => {
    const checkOnlineStatus = () => {
      setIsOnline(navigator.onLine)
    }

    checkOnlineStatus()
    window.addEventListener("online", checkOnlineStatus)
    window.addEventListener("offline", checkOnlineStatus)

    return () => {
      window.removeEventListener("online", checkOnlineStatus)
      window.removeEventListener("offline", checkOnlineStatus)
    }
  }, [])

  // Simuler le mode hors ligne
  const simulateOffline = () => {
    setIsSimulatingOffline(true)
    // Utiliser l'API Service Worker pour simuler le mode hors ligne
    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: "SIMULATE_OFFLINE",
        value: true,
      })
    }
  }

  // Arrêter la simulation du mode hors ligne
  const stopSimulatingOffline = () => {
    setIsSimulatingOffline(false)
    // Utiliser l'API Service Worker pour arrêter la simulation du mode hors ligne
    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: "SIMULATE_OFFLINE",
        value: false,
      })
    }
  }

  // Précharger les données
  const handlePrefetch = async () => {
    if (!user) {
      addTestResult({
        name: "Préchargement des données",
        status: "error",
        message: "Utilisateur non connecté",
      })
      return
    }

    addTestResult({
      name: "Préchargement des données",
      status: "pending",
      message: "Démarrage du préchargement...",
    })

    try {
      await preloadAllData(user.uid)

      updateTestResult("Préchargement des données", {
        status: "success",
        message: "Données préchargées avec succès",
      })

      // Mettre à jour les statistiques du cache
      loadCacheStats()
    } catch (error) {
      updateTestResult("Préchargement des données", {
        status: "error",
        message: "Erreur lors du préchargement des données",
        details: String(error),
      })
    }
  }

  // Charger les statistiques du cache
  const loadCacheStats = async () => {
    try {
      const stats = await cacheService.getStats()
      setCacheStats(stats)
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques du cache:", error)
    }
  }

  // Exécuter les tests hors ligne
  const runOfflineTests = async () => {
    if (isRunningTests) return

    setIsRunningTests(true)
    setTestProgress(0)
    setTestResults([])

    // Test 1: Vérifier si le Service Worker est actif
    addTestResult({
      name: "Service Worker",
      status: "pending",
      message: "Vérification du Service Worker...",
    })

    setTestProgress(5)

    if ("serviceWorker" in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()

      if (registration && registration.active) {
        updateTestResult("Service Worker", {
          status: "success",
          message: "Service Worker actif",
          details: `Scope: ${registration.scope}`,
        })
      } else {
        updateTestResult("Service Worker", {
          status: "error",
          message: "Service Worker non actif",
          details: "L'application ne fonctionnera pas correctement hors ligne",
        })
      }
    } else {
      updateTestResult("Service Worker", {
        status: "error",
        message: "Service Worker non supporté",
        details: "Votre navigateur ne supporte pas les Service Workers",
      })
    }

    setTestProgress(15)

    // Test 2: Vérifier si IndexedDB est disponible
    addTestResult({
      name: "IndexedDB",
      status: "pending",
      message: "Vérification d'IndexedDB...",
    })

    if ("indexedDB" in window) {
      try {
        const db = await new Promise<IDBDatabase>((resolve, reject) => {
          const request = indexedDB.open("test-db", 1)
          request.onerror = () => reject(new Error("Erreur lors de l'ouverture d'IndexedDB"))
          request.onsuccess = () => resolve(request.result)
        })

        db.close()
        indexedDB.deleteDatabase("test-db")

        updateTestResult("IndexedDB", {
          status: "success",
          message: "IndexedDB disponible",
        })
      } catch (error) {
        updateTestResult("IndexedDB", {
          status: "error",
          message: "Erreur avec IndexedDB",
          details: String(error),
        })
      }
    } else {
      updateTestResult("IndexedDB", {
        status: "error",
        message: "IndexedDB non supporté",
        details: "Votre navigateur ne supporte pas IndexedDB",
      })
    }

    setTestProgress(25)

    // Test 3: Vérifier le cache du Service Worker
    addTestResult({
      name: "Cache API",
      status: "pending",
      message: "Vérification du Cache API...",
    })

    if ("caches" in window) {
      try {
        const cacheNames = await caches.keys()
        const cacheCount = cacheNames.length

        if (cacheCount > 0) {
          updateTestResult("Cache API", {
            status: "success",
            message: `${cacheCount} caches trouvés`,
            details: cacheNames.join(", "),
          })
        } else {
          updateTestResult("Cache API", {
            status: "warning",
            message: "Aucun cache trouvé",
            details: "L'application n'a pas encore mis en cache de ressources",
          })
        }
      } catch (error) {
        updateTestResult("Cache API", {
          status: "error",
          message: "Erreur avec Cache API",
          details: String(error),
        })
      }
    } else {
      updateTestResult("Cache API", {
        status: "error",
        message: "Cache API non supporté",
        details: "Votre navigateur ne supporte pas Cache API",
      })
    }

    setTestProgress(40)

    // Test 4: Vérifier les données en cache
    addTestResult({
      name: "Données en cache",
      status: "pending",
      message: "Vérification des données en cache...",
    })

    try {
      const stats = await cacheService.getStats()
      setCacheStats(stats)

      const totalItems = Object.values(stats).reduce((acc: number, curr: any) => acc + curr.count, 0)

      if (totalItems > 0) {
        updateTestResult("Données en cache", {
          status: "success",
          message: `${totalItems} éléments en cache`,
          details: Object.entries(stats)
            .map(([key, value]: [string, any]) => `${key}: ${value.count} éléments`)
            .join(", "),
        })
      } else {
        updateTestResult("Données en cache", {
          status: "warning",
          message: "Aucune donnée en cache",
          details: "Exécutez le préchargement des données pour remplir le cache",
        })
      }
    } catch (error) {
      updateTestResult("Données en cache", {
        status: "error",
        message: "Erreur lors de la vérification des données en cache",
        details: String(error),
      })
    }

    setTestProgress(60)

    // Test 5: Vérifier la simulation du mode hors ligne
    addTestResult({
      name: "Simulation hors ligne",
      status: "pending",
      message: "Vérification de la simulation du mode hors ligne...",
    })

    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
      try {
        // Créer un canal de communication avec le Service Worker
        const messageChannel = new MessageChannel()

        const response = await new Promise<any>((resolve) => {
          messageChannel.port1.onmessage = (event) => {
            resolve(event.data)
          }

          navigator.serviceWorker.controller.postMessage(
            {
              type: "CHECK_OFFLINE_SIMULATION",
            },
            [messageChannel.port2],
          )

          // Timeout après 2 secondes
          setTimeout(() => resolve({ supported: false }), 2000)
        })

        if (response.supported) {
          updateTestResult("Simulation hors ligne", {
            status: "success",
            message: "Simulation du mode hors ligne supportée",
          })
        } else {
          updateTestResult("Simulation hors ligne", {
            status: "warning",
            message: "Simulation du mode hors ligne non supportée",
            details: "Utilisez les outils de développement du navigateur pour simuler le mode hors ligne",
          })
        }
      } catch (error) {
        updateTestResult("Simulation hors ligne", {
          status: "warning",
          message: "Erreur lors de la vérification de la simulation du mode hors ligne",
          details: String(error),
        })
      }
    } else {
      updateTestResult("Simulation hors ligne", {
        status: "warning",
        message: "Service Worker non actif",
        details: "Impossible de vérifier la simulation du mode hors ligne",
      })
    }

    setTestProgress(80)

    // Test 6: Vérifier la détection de l'état de connexion
    addTestResult({
      name: "Détection de connexion",
      status: "pending",
      message: "Vérification de la détection de l'état de connexion...",
    })

    const currentOnlineStatus = navigator.onLine

    updateTestResult("Détection de connexion", {
      status: "success",
      message: `État de connexion détecté: ${currentOnlineStatus ? "En ligne" : "Hors ligne"}`,
      details: "L'application peut détecter l'état de connexion",
    })

    setTestProgress(100)
    setIsRunningTests(false)
  }

  // Ajouter un résultat de test
  const addTestResult = (result: TestResult) => {
    setTestResults((prev) => [...prev, result])
  }

  // Mettre à jour un résultat de test
  const updateTestResult = (name: string, update: Partial<TestResult>) => {
    setTestResults((prev) => prev.map((result) => (result.name === name ? { ...result, ...update } : result)))
  }

  // Afficher l'icône de statut
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-5 w-5 text-green-500" />
      case "error":
        return <X className="h-5 w-5 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-amber-500" />
      case "pending":
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
      default:
        return null
    }
  }

  // Exporter les logs de test
  const exportTestLogs = () => {
    try {
      // Créer un objet contenant toutes les informations de test
      const exportData = {
        timestamp: new Date().toISOString(),
        user: user ? { uid: user.uid, email: user.email } : null,
        isOnline,
        isSimulatingOffline,
        testResults,
        cacheStats,
        browserInfo: navigator.userAgent,
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      }

      // Convertir en JSON
      const jsonData = JSON.stringify(exportData, null, 2)

      // Créer un blob et un lien de téléchargement
      const blob = new Blob([jsonData], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `acr-direct-offline-tests-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`
      document.body.appendChild(a)
      a.click()

      // Nettoyer
      setTimeout(() => {
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }, 100)
    } catch (error) {
      console.error("Erreur lors de l'exportation des logs:", error)
    }
  }

  // Charger les statistiques du cache au chargement du composant
  useEffect(() => {
    loadCacheStats()
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Testeur de fonctionnement hors ligne</span>
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <div className="flex items-center text-green-600 text-sm">
                <Wifi className="h-4 w-4 mr-1" />
                <span>En ligne</span>
              </div>
            ) : (
              <div className="flex items-center text-amber-600 text-sm">
                <WifiOff className="h-4 w-4 mr-1" />
                <span>Hors ligne</span>
              </div>
            )}
          </div>
        </CardTitle>
        <CardDescription>
          Testez le fonctionnement hors ligne de l'application et préchargez les données
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="tester" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tester">Testeur</TabsTrigger>
            <TabsTrigger value="simulator">Simulateur</TabsTrigger>
            <TabsTrigger value="results">Résultats</TabsTrigger>
          </TabsList>

          <TabsContent value="tester" className="space-y-4 mt-4">
            <div className="flex flex-col space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <Button onClick={runOfflineTests} disabled={isRunningTests} className="flex-1">
                  {isRunningTests ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Tests en cours...
                    </>
                  ) : (
                    "Exécuter les tests hors ligne"
                  )}
                </Button>

                <Button onClick={handlePrefetch} variant="outline" className="flex-1" disabled={!user}>
                  Précharger les données
                </Button>
              </div>

              {isRunningTests && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progression des tests</span>
                    <span>{testProgress}%</span>
                  </div>
                  <Progress value={testProgress} className="h-2" />
                </div>
              )}

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Résultats des tests</h3>

                {testResults.length === 0 ? (
                  <p className="text-sm text-muted-foreground">Exécutez les tests pour voir les résultats</p>
                ) : (
                  <div className="space-y-2">
                    {testResults.map((result, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {renderStatusIcon(result.status)}
                            <span className="ml-2 font-medium">{result.name}</span>
                          </div>
                          <span
                            className={`text-sm ${
                              result.status === "success"
                                ? "text-green-600"
                                : result.status === "error"
                                  ? "text-red-600"
                                  : result.status === "warning"
                                    ? "text-amber-600"
                                    : "text-blue-600"
                            }`}
                          >
                            {result.status === "pending"
                              ? "En cours..."
                              : result.status === "success"
                                ? "Succès"
                                : result.status === "error"
                                  ? "Échec"
                                  : "Avertissement"}
                          </span>
                        </div>
                        <p className="text-sm mt-1">{result.message}</p>
                        {result.details && <p className="text-xs text-muted-foreground mt-1">{result.details}</p>}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="simulator" className="space-y-4 mt-4">
            <div className="space-y-4">
              <Alert className="bg-blue-50 border-blue-200">
                <AlertTitle>Simulation du mode hors ligne</AlertTitle>
                <AlertDescription>
                  Cette fonctionnalité vous permet de simuler le mode hors ligne sans désactiver votre connexion
                  Internet. Cela est utile pour tester le comportement de l'application en mode hors ligne.
                </AlertDescription>
              </Alert>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={simulateOffline}
                  disabled={isSimulatingOffline || !isOnline}
                  className="flex-1"
                  variant={isSimulatingOffline ? "outline" : "default"}
                >
                  <WifiOff className="mr-2 h-4 w-4" />
                  Simuler le mode hors ligne
                </Button>

                <Button
                  onClick={stopSimulatingOffline}
                  disabled={!isSimulatingOffline}
                  className="flex-1"
                  variant={!isSimulatingOffline ? "outline" : "default"}
                >
                  <Wifi className="mr-2 h-4 w-4" />
                  Arrêter la simulation
                </Button>
              </div>

              {!isOnline && !isSimulatingOffline && (
                <Alert variant="warning">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Vous êtes actuellement hors ligne</AlertTitle>
                  <AlertDescription>
                    Votre appareil n'est pas connecté à Internet. Ce n'est pas une simulation.
                  </AlertDescription>
                </Alert>
              )}

              {isSimulatingOffline && (
                <Alert variant="warning">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Mode hors ligne simulé</AlertTitle>
                  <AlertDescription>
                    L'application fonctionne actuellement en mode hors ligne simulé. Toutes les requêtes réseau sont
                    interceptées et servies depuis le cache.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="results" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Statistiques du cache</h3>

              {!cacheStats ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : Object.keys(cacheStats).length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  Aucune donnée en cache. Exécutez le préchargement des données pour remplir le cache.
                </p>
              ) : (
                <div className="space-y-2">
                  {Object.entries(cacheStats).map(([key, value]: [string, any], index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{key}</span>
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                          {value.count} éléments
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Taille approximative: {Math.round(value.size / 1024)} KB
                      </p>
                    </div>
                  ))}
                </div>
              )}

              <div className="mt-4">
                <Button onClick={loadCacheStats} variant="outline" size="sm">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Actualiser les statistiques
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between items-center border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Utilisez les outils de développement du navigateur pour des tests plus avancés
        </p>
        <Button variant="outline" size="sm" onClick={exportTestLogs} disabled={testResults.length === 0}>
          <Download className="h-4 w-4 mr-2" />
          Exporter les logs
        </Button>
      </CardFooter>
    </Card>
  )
}

export default OfflineTester
