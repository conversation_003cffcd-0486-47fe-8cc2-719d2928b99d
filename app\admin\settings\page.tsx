"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import {
  Loader2,
  Save,
  Upload,
  Download,
  Shield,
  Mail,
  Globe,
  Palette,
  Database,
  Users,
  FileText,
  Eye,
  Moon,
  Sun,
  RefreshCw,
} from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import dynamic from "next/dynamic"
import { Skeleton } from "@/components/ui/skeleton"
import { isLightColor } from "@/lib/color-utils"
import { applyTheme, loadThemeFromSettings } from "@/lib/theme-service"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"

// Styles pour la gestion du débordement
const containerStyles = {
  maxWidth: "100%",
  overflowX: "hidden",
}

// Dynamically import the RichTextEditor with no SSR
const RichTextEditor = dynamic(() => import("@/components/rich-text-editor"), {
  ssr: false,
  loading: () => <Skeleton className="w-full h-64" />,
})

export default function SettingsPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("general")

  // Mode actif (clair/sombre) pour l'édition
  const [activeMode, setActiveMode] = useState("light")

  // Couleurs de l'interface - mode clair
  const [backgroundColor, setBackgroundColor] = useState("#ffffff")
  const [foregroundColor, setForegroundColor] = useState("#1f2937")
  const [mutedColor, setMutedColor] = useState("#f3f4f6")
  const [mutedForegroundColor, setMutedForegroundColor] = useState("#6b7280")
  const [cardColor, setCardColor] = useState("#ffffff")
  const [cardForegroundColor, setCardForegroundColor] = useState("#1f2937")
  const [borderColor, setBorderColor] = useState("#e5e7eb")
  const [inputColor, setInputColor] = useState("#f9fafb")

  // Couleurs de l'interface - mode sombre
  const [darkBackgroundColor, setDarkBackgroundColor] = useState("#0f172a")
  const [darkForegroundColor, setDarkForegroundColor] = useState("#f8fafc")
  const [darkMutedColor, setDarkMutedColor] = useState("#1e293b")
  const [darkMutedForegroundColor, setDarkMutedForegroundColor] = useState("#94a3b8")
  const [darkCardColor, setDarkCardColor] = useState("#1e293b")
  const [darkCardForegroundColor, setDarkCardForegroundColor] = useState("#f8fafc")
  const [darkBorderColor, setDarkBorderColor] = useState("#334155")
  const [darkInputColor, setDarkInputColor] = useState("#1e293b")

  // Couleurs de base pour le mode sombre
  const [darkPrimaryColor, setDarkPrimaryColor] = useState("#3b82f6")
  const [darkSecondaryColor, setDarkSecondaryColor] = useState("#1e293b")
  const [darkAccentColor, setDarkAccentColor] = useState("#8b5cf6")
  const [darkSuccessColor, setDarkSuccessColor] = useState("#10b981")
  const [darkWarningColor, setDarkWarningColor] = useState("#f59e0b")
  const [darkErrorColor, setDarkErrorColor] = useState("#ef4444")
  const [darkInfoColor, setDarkInfoColor] = useState("#06b6d4")

  // Site settings
  const [siteName, setSiteName] = useState("ACR Direct")
  const [siteDescription, setSiteDescription] = useState("Portail d'information B2B ACR Direct")
  const [logoUrl, setLogoUrl] = useState("")
  const [primaryColor, setPrimaryColor] = useState("#1e40af") // Bleu sombre
  const [secondaryColor, setSecondaryColor] = useState("#f3f4f6") // Gris clair

  // Couleurs supplémentaires
  const [accentColor, setAccentColor] = useState("#8b5cf6") // Violet
  const [successColor, setSuccessColor] = useState("#10b981") // Vert
  const [warningColor, setWarningColor] = useState("#f59e0b") // Ambre
  const [errorColor, setErrorColor] = useState("#ef4444") // Rouge
  const [infoColor, setInfoColor] = useState("#06b6d4") // Cyan

  // Content display settings
  const [defaultShowFrameNews, setDefaultShowFrameNews] = useState(true)
  const [defaultShowFramePages, setDefaultShowFramePages] = useState(true)
  // In the useState section, add a new state variable for showing publication dates
  const [showPublicationDates, setShowPublicationDates] = useState(true)
  // Add a new state variable for the default publication date visibility (around line 60)
  const [defaultShowPublicationDate, setDefaultShowPublicationDate] = useState(false)

  // Email settings
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [welcomeEmail, setWelcomeEmail] = useState(true)
  const [newsEmail, setNewsEmail] = useState(true)
  const [emailSender, setEmailSender] = useState("<EMAIL>")
  const [emailTemplate, setEmailTemplate] = useState(
    "<p>Bonjour {{name}},</p><p>{{message}}</p><p>L'équipe ACR Direct</p>",
  )

  // Security settings
  const [requireStrongPasswords, setRequireStrongPasswords] = useState(true)
  const [sessionTimeout, setSessionTimeout] = useState("30")
  const [twoFactorAuth, setTwoFactorAuth] = useState(false)
  const [maxLoginAttempts, setMaxLoginAttempts] = useState("5")

  // Data settings
  const [autoBackup, setAutoBackup] = useState(true)
  const [backupFrequency, setBackupFrequency] = useState("daily")
  const [dataRetention, setDataRetention] = useState("90")

  // User settings
  const [allowUserRegistration, setAllowUserRegistration] = useState(false)
  const [defaultUserGroup, setDefaultUserGroup] = useState("clients")
  const [userApproval, setUserApproval] = useState(true)

  // Legal settings
  const [termsContent, setTermsContent] = useState("")
  const [privacyContent, setPrivacyContent] = useState("")

  // Définition des onglets
  const tabs = [
    { id: "general", label: "Général", icon: <Globe className="h-4 w-4" /> },
    { id: "appearance", label: "Personnalisation du thème", icon: <Palette className="h-4 w-4" /> },
    { id: "display", label: "Affichage", icon: <Eye className="h-4 w-4" /> },
    { id: "email", label: "Notifications", icon: <Mail className="h-4 w-4" /> },
    { id: "security", label: "Sécurité", icon: <Shield className="h-4 w-4" /> },
    { id: "data", label: "Données", icon: <Database className="h-4 w-4" /> },
    { id: "users", label: "Utilisateurs", icon: <Users className="h-4 w-4" /> },
    { id: "legal", label: "Mentions légales", icon: <FileText className="h-4 w-4" /> },
  ]

  // Charger les paramètres existants
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Charger les conditions d'utilisation
        const termsDoc = await getDoc(doc(db(), "settings", "terms"))
        if (termsDoc.exists()) {
          setTermsContent(termsDoc.data().content || "")
        }

        // Charger la politique de confidentialité
        const privacyDoc = await getDoc(doc(db(), "settings", "privacy"))
        if (privacyDoc.exists()) {
          setPrivacyContent(privacyDoc.data().content || "")
        }

        // Charger les paramètres d'affichage
        const displaySettingsDoc = await getDoc(doc(db(), "settings", "display"))
        if (displaySettingsDoc.exists()) {
          const displayData = displaySettingsDoc.data()
          setDefaultShowFrameNews(displayData.defaultShowFrameNews !== false)
          setDefaultShowFramePages(displayData.defaultShowFramePages !== false)
          setShowPublicationDates(displayData.showPublicationDates !== false)
          setDefaultShowPublicationDate(displayData.defaultShowPublicationDate === true)
        }

        // Charger les paramètres du site
        const siteSettingsDoc = await getDoc(doc(db(), "settings", "site"))
        if (siteSettingsDoc.exists()) {
          const siteData = siteSettingsDoc.data()
          setSiteName(siteData.name || "ACR Direct")
          setSiteDescription(siteData.description || "Portail d'information B2B ACR Direct")
          setLogoUrl(siteData.logoUrl || "")

          // Mode clair
          setPrimaryColor(siteData.primary || "#1e40af")
          setSecondaryColor(siteData.secondary || "#f3f4f6")
          setAccentColor(siteData.accent || "#8b5cf6")
          setSuccessColor(siteData.success || "#10b981")
          setWarningColor(siteData.warning || "#f59e0b")
          setErrorColor(siteData.error || "#ef4444")
          setInfoColor(siteData.info || "#06b6d4")
          setBackgroundColor(siteData.background || "#ffffff")
          setForegroundColor(siteData.foreground || "#1f2937")
          setMutedColor(siteData.muted || "#f3f4f6")
          setMutedForegroundColor(siteData.mutedForeground || "#6b7280")
          setCardColor(siteData.background || "#ffffff")
          setCardForegroundColor(siteData.foreground || "#1f2937")
          setBorderColor(siteData.muted || "#e5e7eb")
          setInputColor(siteData.muted || "#f9fafb")

          // Mode sombre
          setDarkPrimaryColor(siteData.darkPrimary || siteData.primary || "#3b82f6")
          setDarkSecondaryColor(siteData.darkSecondary || "#1e293b")
          setDarkAccentColor(siteData.darkAccent || siteData.accent || "#8b5cf6")
          setDarkSuccessColor(siteData.darkSuccess || siteData.success || "#10b981")
          setDarkWarningColor(siteData.darkWarning || siteData.warning || "#f59e0b")
          setDarkErrorColor(siteData.darkError || siteData.error || "#ef4444")
          setDarkInfoColor(siteData.darkInfo || siteData.info || "#06b6d4")
          setDarkBackgroundColor(siteData.darkBackground || "#0f172a")
          setDarkForegroundColor(siteData.darkForeground || "#f8fafc")
          setDarkMutedColor(siteData.darkMuted || "#1e293b")
          setDarkMutedForegroundColor(siteData.darkMutedForeground || "#94a3b8")
          setDarkCardColor(siteData.darkCard || "#1e293b")
          setDarkCardForegroundColor(siteData.darkCardForeground || "#f8fafc")
          setDarkBorderColor(siteData.darkBorder || "#334155")
          setDarkInputColor(siteData.darkInput || "#1e293b")
        }
      } catch (error) {
        console.error("Erreur lors du chargement des paramètres:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les paramètres",
          variant: "destructive",
        })
      }
    }

    fetchSettings()
  }, [toast])

  // Update the previewTheme function to properly apply the theme
  const previewTheme = () => {
    try {
      // Créer deux objets thèmes - un pour le mode clair, un pour le mode sombre
      const lightTheme = {
        primary: primaryColor,
        secondary: secondaryColor,
        accent: accentColor,
        success: successColor,
        warning: warningColor,
        error: errorColor,
        info: infoColor,
        background: backgroundColor,
        foreground: foregroundColor,
        muted: mutedColor,
        mutedForeground: mutedForegroundColor,
        card: cardColor,
        cardForeground: cardForegroundColor,
        border: borderColor,
        input: inputColor,
        // Inclure aussi les couleurs du mode sombre
        darkPrimary: darkPrimaryColor,
        darkSecondary: darkSecondaryColor,
        darkAccent: darkAccentColor,
        darkSuccess: darkSuccessColor,
        darkWarning: darkWarningColor,
        darkError: darkErrorColor,
        darkInfo: darkInfoColor,
        darkBackground: darkBackgroundColor,
        darkForeground: darkForegroundColor,
        darkMuted: darkMutedColor,
        darkMutedForeground: darkMutedForegroundColor,
        darkCard: darkCardColor,
        darkCardForeground: darkCardForegroundColor,
        darkBorder: darkBorderColor,
        darkInput: darkInputColor,
      }

      // Appliquer le thème en fonction du mode actuel du système
      const isDark = document.documentElement.classList.contains("dark")
      applyTheme(lightTheme, isDark)

      toast({
        title: "Aperçu du thème",
        description: "Le thème a été appliqué temporairement. Enregistrez pour le conserver.",
      })
    } catch (error) {
      console.error("Erreur lors de la prévisualisation du thème:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'appliquer le thème",
        variant: "destructive",
      })
    }
  }

  // Update the forceReloadTheme function to ensure it reloads for all users
  const forceReloadTheme = async () => {
    try {
      // Supprimer le thème en cache
      localStorage.removeItem("cached_theme")
      sessionStorage.removeItem("current_theme")

      // Recharger le thème depuis Firestore
      const theme = await loadThemeFromSettings()

      // Appliquer le thème
      const isDark = document.documentElement.classList.contains("dark")
      applyTheme(theme, isDark)

      toast({
        title: "Thème rechargé",
        description: "Le thème a été rechargé depuis les paramètres enregistrés.",
      })
    } catch (error) {
      console.error("Erreur lors du rechargement du thème:", error)
      toast({
        title: "Erreur",
        description: "Impossible de recharger le thème",
        variant: "destructive",
      })
    }
  }

  // Update the handleSaveSettings function to ensure theme changes are properly saved and applied
  const handleSaveSettings = async () => {
    setIsLoading(true)

    try {
      // Enregistrer les conditions d'utilisation
      await setDoc(doc(db(), "settings", "terms"), {
        content: termsContent,
        updatedAt: serverTimestamp(),
        isPublic: true, // S'assurer que ce champ est défini à true
      })

      // Enregistrer la politique de confidentialité
      await setDoc(doc(db(), "settings", "privacy"), {
        content: privacyContent,
        updatedAt: serverTimestamp(),
        isPublic: true, // S'assurer que ce champ est défini à true
      })

      // Enregistrer les paramètres d'affichage
      await setDoc(doc(db(), "settings", "display"), {
        defaultShowFrameNews,
        defaultShowFramePages,
        showPublicationDates,
        defaultShowPublicationDate,
        updatedAt: serverTimestamp(),
      })

      // Créer un objet thème complet
      const themeData = {
        name: siteName,
        description: siteDescription,
        logoUrl,
        // Mode clair
        primary: primaryColor,
        secondary: secondaryColor,
        accent: accentColor,
        success: successColor,
        warning: warningColor,
        error: errorColor,
        info: infoColor,
        background: backgroundColor,
        foreground: foregroundColor,
        muted: mutedColor,
        mutedForeground: mutedForegroundColor,
        card: cardColor,
        cardForeground: cardForegroundColor,
        border: borderColor,
        input: inputColor,
        // Mode sombre
        darkPrimary: darkPrimaryColor,
        darkSecondary: darkSecondaryColor,
        darkAccent: darkAccentColor,
        darkSuccess: darkSuccessColor,
        darkWarning: darkWarningColor,
        darkError: darkErrorColor,
        darkInfo: darkInfoColor,
        darkBackground: darkBackgroundColor,
        darkForeground: darkForegroundColor,
        darkMuted: darkMutedColor,
        darkMutedForeground: darkMutedForegroundColor,
        darkCard: darkCardColor,
        darkCardForeground: darkCardForegroundColor,
        darkBorder: darkBorderColor,
        darkInput: darkInputColor,
        updatedAt: serverTimestamp(),
        lastUpdated: new Date().toISOString(), // Add a timestamp for cache busting
      }

      // Enregistrer les paramètres du site et du thème
      await setDoc(doc(db(), "settings", "site"), themeData)

      // Appliquer le thème immédiatement
      const theme = {
        primary: primaryColor,
        secondary: secondaryColor,
        accent: accentColor,
        success: successColor,
        warning: warningColor,
        error: errorColor,
        info: infoColor,
        background: backgroundColor,
        foreground: foregroundColor,
        muted: mutedColor,
        mutedForeground: mutedForegroundColor,
        card: cardColor,
        cardForeground: cardForegroundColor,
        border: borderColor,
        input: inputColor,
        // Mode sombre
        darkPrimary: darkPrimaryColor,
        darkSecondary: darkSecondaryColor,
        darkAccent: darkAccentColor,
        darkSuccess: darkSuccessColor,
        darkWarning: darkWarningColor,
        darkError: darkErrorColor,
        darkInfo: darkInfoColor,
        darkBackground: darkBackgroundColor,
        darkForeground: darkForegroundColor,
        darkMuted: darkMutedColor,
        darkMutedForeground: darkMutedForegroundColor,
        darkCard: darkCardColor,
        darkCardForeground: darkCardForegroundColor,
        darkBorder: darkBorderColor,
        darkInput: darkInputColor,
      }

      const isDark = document.documentElement.classList.contains("dark")
      applyTheme(theme, isDark)

      // Forcer la mise à jour du cache
      localStorage.setItem("cached_theme", JSON.stringify(theme))
      sessionStorage.setItem("current_theme", JSON.stringify(theme))

      toast({
        title: "Paramètres enregistrés",
        description: "Les paramètres ont été mis à jour avec succès pour tous les utilisateurs.",
      })
    } catch (error) {
      console.error("Erreur lors de l'enregistrement des paramètres:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement des paramètres",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Rendu du contenu de l'onglet actif
  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Paramètres généraux</CardTitle>
              <CardDescription>Configurez les informations générales de votre portail</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="space-y-2">
                <Label htmlFor="site-name">Nom du site</Label>
                <Input id="site-name" value={siteName} onChange={(e) => setSiteName(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="site-description">Description</Label>
                <Textarea
                  id="site-description"
                  value={siteDescription}
                  onChange={(e) => setSiteDescription(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="logo-url">URL du logo</Label>
                <Input
                  id="logo-url"
                  value={logoUrl}
                  onChange={(e) => setLogoUrl(e.target.value)}
                  placeholder="https://exemple.com/logo.png"
                />
                <p className="text-sm text-gray-500">Laissez vide pour utiliser le nom du site comme logo</p>
              </div>
            </CardContent>
          </Card>
        )
      case "appearance":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-card-header border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Personnalisation du thème</CardTitle>
              <CardDescription>Personnalisez toutes les couleurs de votre interface</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-4 sm:pt-6 p-3 sm:p-6">
              {/* Sélecteur de mode pour éditer soit le mode clair soit le mode sombre */}
              <div className="flex justify-center mb-4">
                <div className="inline-flex items-center p-1 border rounded-lg bg-muted">
                  <Button
                    variant={activeMode === "light" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveMode("light")}
                    className="flex items-center gap-2"
                  >
                    <Sun className="h-4 w-4" />
                    Mode clair
                  </Button>
                  <Button
                    variant={activeMode === "dark" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveMode("dark")}
                    className="flex items-center gap-2"
                  >
                    <Moon className="h-4 w-4" />
                    Mode sombre
                  </Button>
                </div>
              </div>

              {/* Groupes de couleurs */}
              <div className="grid md:grid-cols-2 gap-6">
                {/* Couleurs principales */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium border-b pb-2">Couleurs principales</h3>

                  <div className="space-y-4">
                    <ColorPicker
                      label="Principale"
                      description="Utilisée pour les boutons, liens et éléments importants"
                      value={activeMode === "light" ? primaryColor : darkPrimaryColor}
                      onChange={(value) =>
                        activeMode === "light" ? setPrimaryColor(value) : setDarkPrimaryColor(value)
                      }
                      presets={["#1e40af", "#047857", "#7c3aed", "#dc2626", "#0284c7"]}
                    />

                    <ColorPicker
                      label="Secondaire"
                      description="Utilisée pour les arrière-plans secondaires et éléments discrets"
                      value={activeMode === "light" ? secondaryColor : darkSecondaryColor}
                      onChange={(value) =>
                        activeMode === "light" ? setSecondaryColor(value) : setDarkSecondaryColor(value)
                      }
                      presets={["#f3f4f6", "#f0fdf4", "#f5f3ff", "#fef2f2", "#f0f9ff"]}
                    />

                    <ColorPicker
                      label="Accent"
                      description="Utilisée pour les éléments qui nécessitent une attention particulière"
                      value={activeMode === "light" ? accentColor : darkAccentColor}
                      onChange={(value) => (activeMode === "light" ? setAccentColor(value) : setDarkAccentColor(value))}
                      presets={["#8b5cf6", "#ec4899", "#06b6d4", "#14b8a6", "#f97316"]}
                    />
                  </div>
                </div>

                {/* Couleurs de statut */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium border-b pb-2">Couleurs de statut</h3>

                  <div className="space-y-4">
                    <ColorPicker
                      label="Succès"
                      description="Pour les messages de réussite et confirmations"
                      value={activeMode === "light" ? successColor : darkSuccessColor}
                      onChange={(value) =>
                        activeMode === "light" ? setSuccessColor(value) : setDarkSuccessColor(value)
                      }
                      presets={["#10b981", "#16a34a", "#22c55e", "#059669", "#15803d"]}
                    />

                    <ColorPicker
                      label="Avertissement"
                      description="Pour les alertes et avertissements"
                      value={activeMode === "light" ? warningColor : darkWarningColor}
                      onChange={(value) =>
                        activeMode === "light" ? setWarningColor(value) : setDarkWarningColor(value)
                      }
                      presets={["#f59e0b", "#eab308", "#fbbf24", "#f97316", "#d97706"]}
                    />

                    <ColorPicker
                      label="Erreur"
                      description="Pour les messages d'erreur et actions critiques"
                      value={activeMode === "light" ? errorColor : darkErrorColor}
                      onChange={(value) => (activeMode === "light" ? setErrorColor(value) : setDarkErrorColor(value))}
                      presets={["#ef4444", "#dc2626", "#f87171", "#b91c1c", "#991b1b"]}
                    />

                    <ColorPicker
                      label="Information"
                      description="Pour les informations générales et conseils"
                      value={activeMode === "light" ? infoColor : darkInfoColor}
                      onChange={(value) => (activeMode === "light" ? setInfoColor(value) : setDarkInfoColor(value))}
                      presets={["#06b6d4", "#0284c7", "#0ea5e9", "#0891b2", "#0369a1"]}
                    />
                  </div>
                </div>
              </div>

              {/* Interface et fond */}
              <div className="mt-6 pt-6 border-t">
                <h3 className="text-lg font-medium mb-4">Interface et fond</h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <ColorPicker
                      label="Arrière-plan"
                      description="Couleur principale de fond"
                      value={activeMode === "light" ? backgroundColor : darkBackgroundColor}
                      onChange={(value) =>
                        activeMode === "light" ? setBackgroundColor(value) : setDarkBackgroundColor(value)
                      }
                      presets={
                        activeMode === "light"
                          ? ["#ffffff", "#f8fafc", "#f9fafb", "#f8f9fa", "#fafafa"]
                          : ["#0f172a", "#0c1b17", "#1a103d", "#0c0a0b", "#000000"]
                      }
                    />

                    <ColorPicker
                      label="Texte principal"
                      description="Couleur principale du texte"
                      value={activeMode === "light" ? foregroundColor : darkForegroundColor}
                      onChange={(value) =>
                        activeMode === "light" ? setForegroundColor(value) : setDarkForegroundColor(value)
                      }
                      presets={
                        activeMode === "light"
                          ? ["#1f2937", "#111827", "#0f172a", "#18181b", "#000000"]
                          : ["#f8fafc", "#f0fdf4", "#f5f3ff", "#fef2f2", "#ffffff"]
                      }
                    />
                  </div>

                  <div className="space-y-4">
                    <ColorPicker
                      label="Fond atténué"
                      description="Utilisé pour les éléments légèrement distincts du fond"
                      value={activeMode === "light" ? mutedColor : darkMutedColor}
                      onChange={(value) => (activeMode === "light" ? setMutedColor(value) : setDarkMutedColor(value))}
                      presets={
                        activeMode === "light"
                          ? ["#f3f4f6", "#f1f5f9", "#f5f5f4", "#f4f4f5", "#f5f5f5"]
                          : ["#1e293b", "#14342a", "#2e1065", "#292524", "#27272a"]
                      }
                    />

                    <ColorPicker
                      label="Texte atténué"
                      description="Utilisé pour les textes secondaires"
                      value={activeMode === "light" ? mutedForegroundColor : darkMutedForegroundColor}
                      onChange={(value) =>
                        activeMode === "light" ? setMutedForegroundColor(value) : setDarkMutedForegroundColor(value)
                      }
                      presets={
                        activeMode === "light"
                          ? ["#6b7280", "#64748b", "#737373", "#71717a", "#78716c"]
                          : ["#94a3b8", "#86efac", "#c4b5fd", "#fca5a5", "#bae6fd"]
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Cartes et bordures */}
              <div className="mt-6 pt-6 border-t">
                <h3 className="text-lg font-medium mb-4">Cartes et bordures</h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <ColorPicker
                      label="Fond des cartes"
                      description="Utilisé pour les cartes et éléments surélevés"
                      value={activeMode === "light" ? cardColor : darkCardColor}
                      onChange={(value) => (activeMode === "light" ? setCardColor(value) : setDarkCardColor(value))}
                      presets={
                        activeMode === "light"
                          ? ["#ffffff", "#f8fafc", "#f9fafb", "#f8f9fa", "#fafafa"]
                          : ["#1e293b", "#14342a", "#2e1065", "#292524", "#27272a"]
                      }
                    />

                    <ColorPicker
                      label="Texte des cartes"
                      description="Texte utilisé dans les cartes"
                      value={activeMode === "light" ? cardForegroundColor : darkCardForegroundColor}
                      onChange={(value) =>
                        activeMode === "light" ? setCardForegroundColor(value) : setDarkCardForegroundColor(value)
                      }
                      presets={
                        activeMode === "light"
                          ? ["#1f2937", "#111827", "#0f172a", "#18181b", "#000000"]
                          : ["#f8fafc", "#f0fdf4", "#f5f3ff", "#fef2f2", "#ffffff"]
                      }
                    />
                  </div>

                  <div className="space-y-4">
                    <ColorPicker
                      label="Bordures"
                      description="Utilisé pour les séparateurs et bordures"
                      value={activeMode === "light" ? borderColor : darkBorderColor}
                      onChange={(value) => (activeMode === "light" ? setBorderColor(value) : setDarkBorderColor(value))}
                      presets={
                        activeMode === "light"
                          ? ["#e5e7eb", "#e2e8f0", "#e7e5e4", "#e4e4e7", "#d4d4d8"]
                          : ["#334155", "#1e3a31", "#3b0764", "#3f3f46", "#44403c"]
                      }
                    />

                    <ColorPicker
                      label="Entrées de formulaire"
                      description="Fond des champs de formulaire"
                      value={activeMode === "light" ? inputColor : darkInputColor}
                      onChange={(value) => (activeMode === "light" ? setInputColor(value) : setDarkInputColor(value))}
                      presets={
                        activeMode === "light"
                          ? ["#f9fafb", "#f8fafc", "#fafaf9", "#fafafa", "#f5f5f5"]
                          : ["#1e293b", "#14342a", "#2e1065", "#292524", "#27272a"]
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Aperçu du thème */}
              <div className="mt-8 pt-4 border-t">
                <h3 className="text-lg font-medium mb-4">Aperçu du thème</h3>

                <div
                  className={cn(
                    "border rounded-lg overflow-hidden",
                    activeMode === "dark" ? "bg-slate-900" : "bg-white",
                  )}
                >
                  <div className="flex justify-end p-2 border-b">
                    <Button variant="outline" size="sm" className="h-8 gap-1 mr-2" onClick={() => forceReloadTheme()}>
                      <RefreshCw className="h-4 w-4" /> Recharger le thème
                    </Button>
                    <Button variant="outline" size="sm" className="h-8 gap-1" onClick={() => previewTheme()}>
                      <Eye className="h-4 w-4" /> Prévisualiser
                    </Button>
                  </div>

                  <div
                    className="p-4 border-b"
                    style={{
                      backgroundColor: activeMode === "dark" ? darkCardColor : cardColor,
                      color: activeMode === "dark" ? darkCardForegroundColor : cardForegroundColor,
                    }}
                  >
                    <h4
                      className="text-lg font-semibold"
                      style={{
                        color: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                      }}
                    >
                      Aperçu du thème
                    </h4>
                    <p
                      className="text-sm"
                      style={{
                        color: activeMode === "dark" ? darkMutedForegroundColor : mutedForegroundColor,
                      }}
                    >
                      Visualisez comment les couleurs s'appliquent aux différents éléments
                    </p>
                  </div>

                  <div
                    className="p-4 space-y-4"
                    style={{
                      backgroundColor: activeMode === "dark" ? darkBackgroundColor : backgroundColor,
                      color: activeMode === "dark" ? darkForegroundColor : foregroundColor,
                    }}
                  >
                    <div className="flex flex-wrap gap-2">
                      <button
                        className="px-3 py-1.5 rounded-md font-medium"
                        style={{
                          backgroundColor: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                          color: "#ffffff",
                        }}
                      >
                        Bouton principal
                      </button>
                      <button
                        className="px-3 py-1.5 rounded-md font-medium border"
                        style={{
                          borderColor: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                          color: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                          backgroundColor: "transparent",
                        }}
                      >
                        Bouton contour
                      </button>
                      <button
                        className="px-3 py-1.5 rounded-md font-medium"
                        style={{
                          color: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                          backgroundColor: "transparent",
                        }}
                      >
                        Lien
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                      <div
                        className="rounded-md p-3"
                        style={{
                          backgroundColor: activeMode === "dark" ? darkMutedColor : mutedColor,
                          color: activeMode === "dark" ? darkForegroundColor : foregroundColor,
                        }}
                      >
                        <h5
                          className="font-medium mb-2"
                          style={{
                            color: activeMode === "dark" ? darkPrimaryColor : primaryColor,
                          }}
                        >
                          Élément atténué
                        </h5>
                        <p
                          className="text-sm"
                          style={{
                            color: activeMode === "dark" ? darkMutedForegroundColor : mutedForegroundColor,
                          }}
                        >
                          Texte sur fond atténué
                        </p>
                      </div>

                      <div
                        className="rounded-md p-3"
                        style={{
                          backgroundColor: activeMode === "dark" ? darkSuccessColor : successColor,
                          color: "#ffffff",
                        }}
                      >
                        <h5 className="font-medium mb-2">Succès</h5>
                        <p className="text-sm opacity-90">Message de confirmation</p>
                      </div>

                      <div
                        className="rounded-md p-3"
                        style={{
                          backgroundColor: activeMode === "dark" ? darkErrorColor : errorColor,
                          color: "#ffffff",
                        }}
                      >
                        <h5 className="font-medium mb-2">Erreur</h5>
                        <p className="text-sm opacity-90">Message d'erreur</p>
                      </div>
                    </div>

                    <div
                      className="p-3 rounded-md border mt-4"
                      style={{
                        backgroundColor: activeMode === "dark" ? darkCardColor : cardColor,
                        borderColor: activeMode === "dark" ? darkBorderColor : borderColor,
                        color: activeMode === "dark" ? darkCardForegroundColor : cardForegroundColor,
                      }}
                    >
                      <h5 className="font-medium mb-2">Carte</h5>
                      <p
                        className="text-sm"
                        style={{
                          color: activeMode === "dark" ? darkMutedForegroundColor : mutedForegroundColor,
                        }}
                      >
                        Contenu dans une carte avec{" "}
                        <span style={{ color: activeMode === "dark" ? darkPrimaryColor : primaryColor }}>un lien</span>{" "}
                        et{" "}
                        <span style={{ color: activeMode === "dark" ? darkAccentColor : accentColor }}>un accent</span>.
                      </p>
                      <div className="mt-3">
                        <input
                          type="text"
                          placeholder="Champ de saisie"
                          className="w-full px-3 py-2 rounded-md border"
                          style={{
                            backgroundColor: activeMode === "dark" ? darkInputColor : inputColor,
                            borderColor: activeMode === "dark" ? darkBorderColor : borderColor,
                            color: activeMode === "dark" ? darkForegroundColor : foregroundColor,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )
      case "presets":
        return null
      case "display":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Paramètres d'affichage</CardTitle>
              <CardDescription>Configurez l'apparence par défaut du contenu</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="default-frame-news">Cadre par défaut pour les actualités</Label>
                  <p className="text-sm text-muted-foreground">Afficher par défaut les actualités avec un cadre</p>
                </div>
                <Switch
                  id="default-frame-news"
                  checked={defaultShowFrameNews}
                  onCheckedChange={setDefaultShowFrameNews}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="default-frame-pages">Cadre par défaut pour les pages</Label>
                  <p className="text-sm text-muted-foreground">Afficher par défaut les pages avec un cadre</p>
                </div>
                <Switch
                  id="default-frame-pages"
                  checked={defaultShowFramePages}
                  onCheckedChange={setDefaultShowFramePages}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="show-publication-dates">Dates de publication</Label>
                  <p className="text-sm text-muted-foreground">Afficher les dates de publication des actualités</p>
                </div>
                <Switch
                  id="show-publication-dates"
                  checked={showPublicationDates}
                  onCheckedChange={setShowPublicationDates}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="default-show-publication-date">Visibilité par défaut des dates de publication</Label>
                  <p className="text-sm text-muted-foreground">
                    Définir si les dates de publication sont visibles par défaut pour les nouvelles actualités
                  </p>
                </div>
                <Switch
                  id="default-show-publication-date"
                  checked={defaultShowPublicationDate}
                  onCheckedChange={setDefaultShowPublicationDate}
                />
              </div>
            </CardContent>
          </Card>
        )
      case "email":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Paramètres de notification</CardTitle>
              <CardDescription>Configurez les notifications envoyées aux utilisateurs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications">Notifications par email</Label>
                  <p className="text-sm text-gray-500">
                    Activer les notifications par email pour tous les utilisateurs
                  </p>
                </div>
                <Switch id="email-notifications" checked={emailNotifications} onCheckedChange={setEmailNotifications} />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="welcome-email">Email de bienvenue</Label>
                  <p className="text-sm text-gray-500">Envoyer un email de bienvenue aux nouveaux utilisateurs</p>
                </div>
                <Switch
                  id="welcome-email"
                  checked={welcomeEmail}
                  onCheckedChange={setWelcomeEmail}
                  disabled={!emailNotifications}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="news-email">Notifications d'actualités</Label>
                  <p className="text-sm text-gray-500">
                    Envoyer des notifications lors de la publication de nouvelles actualités
                  </p>
                </div>
                <Switch
                  id="news-email"
                  checked={newsEmail}
                  onCheckedChange={setNewsEmail}
                  disabled={!emailNotifications}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="email-sender">Adresse d'expédition</Label>
                <Input
                  id="email-sender"
                  type="email"
                  value={emailSender}
                  onChange={(e) => setEmailSender(e.target.value)}
                  disabled={!emailNotifications}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-template">Modèle d'email</Label>
                <Textarea
                  id="email-template"
                  value={emailTemplate}
                  onChange={(e) => setEmailTemplate(e.target.value)}
                  rows={5}
                  disabled={!emailNotifications}
                />
                <p className="text-sm text-gray-500">
                  Utilisez {"{{name}}"} pour le nom du destinataire et {"{{message}}"} pour le contenu de l'email
                </p>
              </div>
            </CardContent>
          </Card>
        )
      case "security":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Paramètres de sécurité</CardTitle>
              <CardDescription>Configurez les options de sécurité de votre portail</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="strong-passwords">Mots de passe forts</Label>
                  <p className="text-sm text-gray-500">Exiger des mots de passe forts pour tous les utilisateurs</p>
                </div>
                <Switch
                  id="strong-passwords"
                  checked={requireStrongPasswords}
                  onCheckedChange={setRequireStrongPasswords}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="two-factor">Authentification à deux facteurs</Label>
                  <p className="text-sm text-gray-500">
                    Activer l'authentification à deux facteurs pour les comptes administrateurs
                  </p>
                </div>
                <Switch id="two-factor" checked={twoFactorAuth} onCheckedChange={setTwoFactorAuth} />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="session-timeout">Délai d'expiration de session (minutes)</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={sessionTimeout}
                  onChange={(e) => setSessionTimeout(e.target.value)}
                  min="5"
                  max="120"
                />
                <p className="text-sm text-gray-500">
                  Durée d'inactivité avant déconnexion automatique (5-120 minutes)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-attempts">Tentatives de connexion maximales</Label>
                <Input
                  id="max-attempts"
                  type="number"
                  value={maxLoginAttempts}
                  onChange={(e) => setMaxLoginAttempts(e.target.value)}
                  min="3"
                  max="10"
                />
                <p className="text-sm text-gray-500">Nombre de tentatives avant verrouillage temporaire du compte</p>
              </div>
            </CardContent>
          </Card>
        )
      case "data":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Gestion des données</CardTitle>
              <CardDescription>Configurez les options de sauvegarde et de conservation des données</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-backup">Sauvegarde automatique</Label>
                  <p className="text-sm text-gray-500">Activer les sauvegardes automatiques de la base de données</p>
                </div>
                <Switch id="auto-backup" checked={autoBackup} onCheckedChange={setAutoBackup} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="backup-frequency">Fréquence des sauvegardes</Label>
                <Select value={backupFrequency} onValueChange={setBackupFrequency} disabled={!autoBackup}>
                  <SelectTrigger id="backup-frequency">
                    <SelectValue placeholder="Sélectionner une fréquence" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Toutes les heures</SelectItem>
                    <SelectItem value="daily">Quotidienne</SelectItem>
                    <SelectItem value="weekly">Hebdomadaire</SelectItem>
                    <SelectItem value="monthly">Mensuelle</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="data-retention">Conservation des données (jours)</Label>
                <Input
                  id="data-retention"
                  type="number"
                  value={dataRetention}
                  onChange={(e) => setDataRetention(e.target.value)}
                  min="30"
                  max="365"
                />
                <p className="text-sm text-gray-500">Durée de conservation des données historiques et des journaux</p>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                <Button variant="outline" className="flex-1">
                  <Download className="mr-2 h-4 w-4" />
                  Sauvegarder maintenant
                </Button>
                <Button variant="outline" className="flex-1">
                  <Upload className="mr-2 h-4 w-4" />
                  Restaurer une sauvegarde
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      case "users":
        return (
          <Card className="border-gray-200">
            <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
              <CardTitle className="text-blue-900">Gestion des utilisateurs</CardTitle>
              <CardDescription>Configurez les options d'inscription et de gestion des utilisateurs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="user-registration">Inscription publique</Label>
                  <p className="text-sm text-gray-500">Permettre aux utilisateurs de s'inscrire eux-mêmes</p>
                </div>
                <Switch
                  id="user-registration"
                  checked={allowUserRegistration}
                  onCheckedChange={setAllowUserRegistration}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="user-approval">Approbation requise</Label>
                  <p className="text-sm text-gray-500">
                    Les nouveaux comptes doivent être approuvés par un administrateur
                  </p>
                </div>
                <Switch
                  id="user-approval"
                  checked={userApproval}
                  onCheckedChange={setUserApproval}
                  disabled={!allowUserRegistration}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-group">Groupe par défaut</Label>
                <Select value={defaultUserGroup} onValueChange={setDefaultUserGroup}>
                  <SelectTrigger id="default-group">
                    <SelectValue placeholder="Sélectionner un groupe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="clients">Clients</SelectItem>
                    <SelectItem value="prospects">Prospects</SelectItem>
                    <SelectItem value="partenaires">Partenaires</SelectItem>
                    <SelectItem value="fournisseurs">Fournisseurs</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">Groupe attribué automatiquement aux nouveaux utilisateurs</p>
              </div>
            </CardContent>
          </Card>
        )
      case "legal":
        return (
          <>
            <Card className="border-gray-200">
              <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
                <CardTitle className="text-blue-900">Conditions d'utilisation</CardTitle>
                <CardDescription>Personnalisez les conditions d'utilisation de votre portail</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
                <div className="space-y-2">
                  <Label htmlFor="terms-content">Contenu des conditions d'utilisation</Label>
                  <RichTextEditor value={termsContent} onChange={setTermsContent} />
                  <p className="text-sm text-gray-500">
                    Ce texte sera affiché lorsqu'un utilisateur clique sur "conditions d'utilisation" lors de
                    l'inscription
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="border-gray-200 mt-6">
              <CardHeader className="bg-gray-50 border-b border-gray-200 p-3 sm:p-4">
                <CardTitle className="text-blue-900">Politique de confidentialité</CardTitle>
                <CardDescription>Personnalisez la politique de confidentialité de votre portail</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-4 sm:pt-6 p-3 sm:p-6">
                <div className="space-y-2">
                  <Label htmlFor="privacy-content">Contenu de la politique de confidentialité</Label>
                  <RichTextEditor value={privacyContent} onChange={setPrivacyContent} />
                  <p className="text-sm text-gray-500">
                    Ce texte sera affiché lorsqu'un utilisateur clique sur "politique de confidentialité" lors de
                    l'inscription
                  </p>
                </div>
              </CardContent>
            </Card>
          </>
        )
      default:
        return null
    }
  }

  function ColorPicker({
    label,
    description,
    value,
    onChange,
    presets = [],
  }: {
    label: string
    description: string
    value: string
    onChange: (value: string) => void
    presets?: string[]
  }) {
    return (
      <div>
        <Label htmlFor={`color-${label}`} className="text-base font-medium">
          {label}
        </Label>
        <p className="text-sm text-muted-foreground mb-2">{description}</p>
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-12 h-10 p-1 rounded-md border"
                style={{
                  backgroundColor: value,
                  borderColor: isLightColor(value) ? "#00000020" : "#ffffff20",
                }}
              >
                <span className="sr-only">Choisir une couleur</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-3">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{label}</h4>
                <Input type="color" value={value} onChange={(e) => onChange(e.target.value)} className="w-full h-8" />
                <div className="grid grid-cols-5 gap-1 mt-2">
                  {presets.map((color) => (
                    <Button
                      key={color}
                      variant="outline"
                      className="w-full h-6 p-0 rounded-sm"
                      style={{ backgroundColor: color }}
                      onClick={() => onChange(color)}
                    />
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <Input id={`color-${label}`} value={value} onChange={(e) => onChange(e.target.value)} className="flex-1" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-4 sm:py-6 px-2 sm:px-4 max-w-full overflow-hidden">
      <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 text-blue-900">Paramètres</h1>

      {/* Navigation par onglets responsive */}
      <div className="mb-6">
        {/* Version pour écrans très petits - menu déroulant */}
        <div className="sm:hidden mb-2">
          <Select value={activeTab} onValueChange={setActiveTab}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Sélectionner une section" />
            </SelectTrigger>
            <SelectContent>
              {tabs.map((tab) => (
                <SelectItem key={tab.id} value={tab.id}>
                  <div className="flex items-center">
                    {tab.icon}
                    <span className="ml-2">{tab.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Version mobile avec défilement horizontal - maintenant pour sm et au-dessus, mais pas xs */}
        <div className="hidden sm:block md:hidden">
          <ScrollArea className="w-full pb-3">
            <div className="flex space-x-1 p-1 bg-gray-100 rounded-md min-w-max">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center justify-center px-2 py-1.5 rounded-md text-xs font-medium transition-colors whitespace-nowrap",
                    activeTab === tab.id
                      ? "bg-blue-700 text-white"
                      : "text-gray-700 hover:bg-gray-200 hover:text-gray-900",
                  )}
                >
                  {tab.icon}
                  <span className="ml-1.5">{tab.label}</span>
                </button>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Version desktop avec onglets fixes et meilleure gestion de l'espace */}
        <div className="hidden md:block">
          <div className="flex flex-wrap gap-1 p-1 bg-gray-100 rounded-md">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  activeTab === tab.id
                    ? "bg-blue-700 text-white"
                    : "text-gray-700 hover:bg-gray-200 hover:text-gray-900",
                )}
              >
                {tab.icon}
                <span className="ml-2">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contenu de l'onglet actif */}
      <div className="w-full overflow-x-hidden">{renderTabContent()}</div>

      <div className="mt-6 flex justify-end">
        <Button
          onClick={handleSaveSettings}
          disabled={isLoading}
          className="bg-blue-700 hover:bg-blue-800 w-full sm:w-auto"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span className="sm:inline hidden">Enregistrement...</span>
              <span className="sm:hidden">Enregistrer</span>
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              <span className="sm:inline hidden">Enregistrer les paramètres</span>
              <span className="sm:hidden">Enregistrer</span>
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
