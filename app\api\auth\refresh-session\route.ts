import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { initializeFirebaseAdmin } from "@/lib/server-auth"

export async function POST(request: NextRequest) {
  try {
    // Récupérer le cookie de session actuel
    const sessionCookie = cookies().get("__session")?.value
    const { auth } = initializeFirebaseAdmin()

    // Si pas de cookie, essayer de récupérer l'ID token du corps de la requête
    if (!sessionCookie) {
      try {
        const body = await request.json()
        const { idToken } = body

        if (idToken) {
          console.log("[Refresh Session] No cookie found, but ID token provided. Creating new session.")

          try {
            // Vérifier que l'ID token est valide
            const decodedIdToken = await auth.verifyIdToken(idToken)
            console.log(`[Refresh Session] ID token verified for user: ${decodedIdToken.uid}`)

            // Créer un nouveau cookie de session avec l'ID token fourni
            const expiresIn = 60 * 60 * 24 * 5 * 1000 // 5 jours
            const newSessionCookie = await auth.createSessionCookie(idToken, { expiresIn })

            // Définir le nouveau cookie
            const options = {
              name: "__session",
              value: newSessionCookie,
              maxAge: expiresIn / 1000,
              httpOnly: true,
              secure: process.env.NODE_ENV === "production",
              path: "/",
              sameSite: "lax" as const,
            }

            cookies().set(options)
            console.log("[Refresh Session] New session cookie created and set successfully")

            return NextResponse.json(
              {
                status: "success",
                message: "New session created successfully",
              },
              { status: 200 },
            )
          } catch (tokenError: any) {
            console.error("[Refresh Session] Error verifying ID token:", tokenError)

            if (tokenError.code === "auth/id-token-expired") {
              return NextResponse.json(
                {
                  error: "ID token has expired",
                  code: "token_expired",
                },
                { status: 401 },
              )
            }

            return NextResponse.json(
              {
                error: tokenError.message || "Invalid ID token",
                code: tokenError.code || "invalid_token",
              },
              { status: 401 },
            )
          }
        } else {
          console.log("[Refresh Session] No ID token provided in request body")
          return NextResponse.json(
            {
              error: "ID token is required",
              code: "token_required",
            },
            { status: 400 },
          )
        }
      } catch (parseError) {
        // Si le corps n'est pas un JSON valide ou ne contient pas d'ID token
        console.log("[Refresh Session] Invalid request body:", parseError)
        return NextResponse.json(
          {
            error: "Invalid request body",
            code: "invalid_request",
          },
          { status: 400 },
        )
      }
    }

    // Vérifier la validité du cookie existant
    try {
      // Vérifier le cookie de session
      const decodedClaims = await auth.verifySessionCookie(sessionCookie)
      console.log(`[Refresh Session] Session cookie verified for user: ${decodedClaims.uid}`)

      // Si le cookie est valide, récupérer l'utilisateur
      const user = await auth.getUser(decodedClaims.uid)

      // Générer un nouveau token personnalisé
      const customToken = await auth.createCustomToken(user.uid)
      console.log(`[Refresh Session] Custom token created for user: ${user.uid}`)

      // Prolonger la durée de vie du cookie de session
      const expiresIn = 60 * 60 * 24 * 5 * 1000 // 5 jours

      // Nous ne pouvons pas utiliser directement le customToken pour créer un cookie de session
      // Nous devons informer le client qu'il doit échanger ce token contre un idToken

      return NextResponse.json(
        {
          status: "success",
          message: "Session refreshed successfully",
          customToken,
          uid: user.uid,
        },
        { status: 200 },
      )
    } catch (error: any) {
      console.error("[Refresh Session] Error verifying session cookie:", error)

      // Supprimer le cookie invalide
      cookies().delete("__session")
      console.log("[Refresh Session] Invalid session cookie deleted")

      // Essayer de récupérer l'ID token du corps de la requête comme fallback
      try {
        const body = await request.json()
        const { idToken } = body

        if (idToken) {
          console.log("[Refresh Session] Session cookie invalid, but ID token provided. Creating new session.")

          try {
            // Vérifier que l'ID token est valide
            const decodedIdToken = await auth.verifyIdToken(idToken)
            console.log(`[Refresh Session] ID token verified for user: ${decodedIdToken.uid}`)

            // Créer un nouveau cookie de session avec l'ID token fourni
            const expiresIn = 60 * 60 * 24 * 5 * 1000 // 5 jours
            const newSessionCookie = await auth.createSessionCookie(idToken, { expiresIn })

            // Définir le nouveau cookie
            const options = {
              name: "__session",
              value: newSessionCookie,
              maxAge: expiresIn / 1000,
              httpOnly: true,
              secure: process.env.NODE_ENV === "production",
              path: "/",
              sameSite: "lax" as const,
            }

            cookies().set(options)
            console.log("[Refresh Session] New session cookie created as fallback")

            return NextResponse.json(
              {
                status: "success",
                message: "New session created as fallback",
              },
              { status: 200 },
            )
          } catch (tokenError: any) {
            console.error("[Refresh Session] Error verifying fallback ID token:", tokenError)
          }
        }
      } catch (parseError) {
        // Ignorer les erreurs de parsing du corps
      }

      if (error.code === "auth/session-cookie-expired") {
        return NextResponse.json(
          {
            error: "Session expired",
            code: "session_expired",
          },
          { status: 401 },
        )
      }

      return NextResponse.json(
        {
          error: "Invalid session",
          code: error.code || "invalid_session",
        },
        { status: 401 },
      )
    }
  } catch (error: any) {
    console.error("[Refresh Session] Error refreshing session:", error)
    return NextResponse.json(
      {
        error: error.message || "Failed to refresh session",
        code: error.code || "server_error",
      },
      { status: 500 },
    )
  }
}
