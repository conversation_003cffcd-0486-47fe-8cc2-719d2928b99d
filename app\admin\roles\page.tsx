"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db } from "@/lib/firebase"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Pencil, Trash2 } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { type Role, PREDEFINED_ROLES } from "@/lib/roles"
import { PERMISSIONS, type Permission } from "@/lib/permissions"
import { PermissionGate } from "@/components/permission-gate"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function RolesPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([])
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Form state
  const [roleName, setRoleName] = useState("")
  const [roleDescription, setRoleDescription] = useState("")
  const [selectedPermissions, setSelectedPermissions] = useState<Permission[]>([])

  useEffect(() => {
    fetchRoles()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredRoles(roles)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredRoles(
        roles.filter(
          (role) =>
            role.name.toLowerCase().includes(lowercasedSearch) ||
            role.description.toLowerCase().includes(lowercasedSearch),
        ),
      )
    }
  }, [searchTerm, roles])

  const fetchRoles = async () => {
    try {
      setIsLoading(true)

      // Get predefined roles
      const predefinedRoles = Object.values(PREDEFINED_ROLES)

      // Get custom roles from Firestore
      try {
        const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
        let customRoles: Role[] = []

        if (rolesDoc.exists()) {
          customRoles = rolesDoc.data().roles || []
        } else {
          // If document doesn't exist, initialize it
          await setDoc(doc(db(), "settings", "roles"), { roles: [] }, { merge: true })
        }

        // Merge predefined and custom roles
        // If a custom role has the same ID as a predefined role, use the custom one
        const mergedRoles = predefinedRoles.map((predefinedRole) => {
          const customOverride = customRoles.find((customRole) => customRole.id === predefinedRole.id)
          return customOverride || predefinedRole
        })

        // Add any custom roles that don't have predefined counterparts
        const customOnlyRoles = customRoles.filter(
          (customRole) => !predefinedRoles.some((predefinedRole) => predefinedRole.id === customRole.id),
        )

        const allRoles = [...mergedRoles, ...customOnlyRoles]
        setRoles(allRoles)
        setFilteredRoles(allRoles)
      } catch (error) {
        console.error("Error fetching custom roles:", error)
        // Fall back to predefined roles
        setRoles(predefinedRoles)
        setFilteredRoles(predefinedRoles)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des rôles:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les rôles",
        variant: "destructive",
      })
      // Ensure we at least have predefined roles
      setRoles(Object.values(PREDEFINED_ROLES))
      setFilteredRoles(Object.values(PREDEFINED_ROLES))
    } finally {
      setIsLoading(false)
    }
  }

  // Update the handleEditRole function to allow editing pre-defined roles
  const handleEditRole = (role: Role) => {
    setEditingRole(role)
    setRoleName(role.name)
    setRoleDescription(role.description)
    setSelectedPermissions([...role.permissions])
    setIsDialogOpen(true)
  }

  // Update the handleDeleteRole function to prevent deletion of pre-defined roles
  const handleDeleteRole = async (roleId: string) => {
    try {
      // Check if it's the admin role or a pre-defined role
      const isPredefinedRole = Object.values(PREDEFINED_ROLES).some((role) => role.id === roleId)

      if (roleId === "admin") {
        toast({
          title: "Erreur",
          description: "Impossible de supprimer le rôle administrateur",
          variant: "destructive",
        })
        return
      }

      if (isPredefinedRole) {
        toast({
          title: "Erreur",
          description: "Impossible de supprimer un rôle prédéfini",
          variant: "destructive",
        })
        return
      }

      // Get current custom roles
      const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
      let customRoles: Role[] = []

      if (rolesDoc.exists()) {
        customRoles = rolesDoc.data().roles || []
      }

      // Remove the role
      const updatedRoles = customRoles.filter((role) => role.id !== roleId)

      // Update Firestore
      await setDoc(doc(db(), "settings", "roles"), { roles: updatedRoles })

      // Update local state
      setRoles((prevRoles) => prevRoles.filter((role) => role.id !== roleId))

      toast({
        title: "Succès",
        description: "Le rôle a été supprimé avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la suppression du rôle:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le rôle",
        variant: "destructive",
      })
    }
  }

  // Update the handleSaveRole function to handle pre-defined roles
  const handleSaveRole = async () => {
    if (!roleName) {
      toast({
        title: "Erreur",
        description: "Veuillez saisir un nom pour le rôle",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSaving(true)

      // Get current custom roles
      const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
      let customRoles: Role[] = []

      if (rolesDoc.exists()) {
        customRoles = rolesDoc.data().roles || []
      }

      if (editingRole) {
        // Check if it's the admin role
        if (editingRole.id === "admin") {
          toast({
            title: "Erreur",
            description: "Impossible de modifier le rôle administrateur",
            variant: "destructive",
          })
          return
        }

        // Check if it's a pre-defined role
        const isPredefinedRole = Object.values(PREDEFINED_ROLES).some(
          (role) => role.id === editingRole.id && role.isPreDefined,
        )

        if (isPredefinedRole) {
          // For pre-defined roles, we need to update them in the custom roles collection
          // Check if the role already exists in custom roles
          const existingRoleIndex = customRoles.findIndex((role) => role.id === editingRole.id)

          if (existingRoleIndex >= 0) {
            // Update existing custom override
            customRoles[existingRoleIndex] = {
              ...customRoles[existingRoleIndex],
              name: roleName,
              description: roleDescription,
              permissions: selectedPermissions,
              isPreDefined: true,
            }
          } else {
            // Add as a new custom override
            customRoles.push({
              id: editingRole.id,
              name: roleName,
              description: roleDescription,
              permissions: selectedPermissions,
              isPreDefined: true,
            })
          }

          await setDoc(doc(db(), "settings", "roles"), { roles: customRoles })

          // Update local state - find the role in the current roles array and update it
          setRoles((prevRoles) =>
            prevRoles.map((role) =>
              role.id === editingRole.id
                ? {
                    ...role,
                    name: roleName,
                    description: roleDescription,
                    permissions: selectedPermissions,
                  }
                : role,
            ),
          )
        } else {
          // For custom roles, update them normally
          const updatedRoles = customRoles.map((role) =>
            role.id === editingRole.id
              ? {
                  ...role,
                  name: roleName,
                  description: roleDescription,
                  permissions: selectedPermissions,
                }
              : role,
          )

          await setDoc(doc(db(), "settings", "roles"), { roles: updatedRoles })

          // Update local state
          setRoles((prevRoles) =>
            prevRoles.map((role) =>
              role.id === editingRole.id
                ? {
                    ...role,
                    name: roleName,
                    description: roleDescription,
                    permissions: selectedPermissions,
                  }
                : role,
            ),
          )
        }
      } else {
        // Create new role
        const newRole: Role = {
          id: `role_${Date.now()}`,
          name: roleName,
          description: roleDescription,
          permissions: selectedPermissions,
        }

        const updatedRoles = [...customRoles, newRole]
        await setDoc(doc(db(), "settings", "roles"), { roles: updatedRoles })

        // Update local state
        setRoles((prevRoles) => [...prevRoles, newRole])
      }

      toast({
        title: "Succès",
        description: `Le rôle a été ${editingRole ? "modifié" : "créé"} avec succès`,
      })

      setIsDialogOpen(false)
    } catch (error) {
      console.error(`Erreur lors de la ${editingRole ? "modification" : "création"} du rôle:`, error)
      toast({
        title: "Erreur",
        description: `Impossible de ${editingRole ? "modifier" : "créer"} le rôle`,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const togglePermission = (permission: Permission) => {
    setSelectedPermissions((prev) =>
      prev.includes(permission) ? prev.filter((p) => p !== permission) : [...prev, permission],
    )
  }

  // Group permissions by resource type
  const groupedPermissions: Record<string, Permission[]> = {}
  Object.values(PERMISSIONS).forEach((permission) => {
    if (permission === "admin") {
      if (!groupedPermissions["Admin"]) {
        groupedPermissions["Admin"] = []
      }
      groupedPermissions["Admin"].push(permission)
    } else {
      const [action, resource] = permission.split(":") as [string, string]
      const resourceCapitalized = resource.charAt(0).toUpperCase() + resource.slice(1)

      if (!groupedPermissions[resourceCapitalized]) {
        groupedPermissions[resourceCapitalized] = []
      }

      groupedPermissions[resourceCapitalized].push(permission)
    }
  })

  const handleCreateRole = () => {
    setEditingRole(null)
    setRoleName("")
    setRoleDescription("")
    setSelectedPermissions([])
    setIsDialogOpen(true)
  }

  return (
    <PermissionGate permissions={[PERMISSIONS.ADMIN]}>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Gestion des rôles</h1>
          <Button onClick={handleCreateRole}>
            <Plus className="mr-2 h-4 w-4" />
            Nouveau rôle
          </Button>
        </div>

        <Card className="mb-6">
          <CardContent className="pt-6">
            <Input
              placeholder="Rechercher un rôle..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </CardContent>
        </Card>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nom</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Nombre de permissions</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        Aucun rôle trouvé
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">{role.name}</TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>
                          {role.isSystem ? "Système" : role.isPreDefined ? "Prédéfini" : "Personnalisé"}
                        </TableCell>
                        <TableCell>
                          {role.permissions.includes("admin")
                            ? "Toutes les permissions"
                            : `${role.permissions.length} permission${role.permissions.length > 1 ? "s" : ""}`}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEditRole(role)}
                              disabled={role.isSystem}
                              title={role.isSystem ? "Le rôle administrateur ne peut pas être modifié" : "Modifier"}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>

                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  disabled={role.isSystem || role.isPreDefined}
                                  title={
                                    role.isSystem
                                      ? "Le rôle administrateur ne peut pas être supprimé"
                                      : role.isPreDefined
                                        ? "Les rôles prédéfinis ne peuvent pas être supprimés"
                                        : "Supprimer"
                                  }
                                >
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Êtes-vous sûr de vouloir supprimer ce rôle ? Cette action est irréversible.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Annuler</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteRole(role.id)}
                                    className="bg-red-500 hover:bg-red-600"
                                  >
                                    Supprimer
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Role Edit/Create Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingRole ? "Modifier le rôle" : "Créer un nouveau rôle"}</DialogTitle>
              <DialogDescription>
                {editingRole
                  ? "Modifiez les détails et les permissions du rôle"
                  : "Définissez un nouveau rôle avec des permissions spécifiques"}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nom
                </Label>
                <Input
                  id="name"
                  value={roleName}
                  onChange={(e) => setRoleName(e.target.value)}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={roleDescription}
                  onChange={(e) => setRoleDescription(e.target.value)}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-1 gap-4 mt-4">
                <Label className="font-bold text-lg">Permissions</Label>

                {Object.entries(groupedPermissions).map(([resource, permissions]) => (
                  <Card key={resource} className="mb-4">
                    <CardHeader className="py-2">
                      <CardTitle className="text-md">{resource}</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {permissions.map((permission) => (
                        <div key={permission} className="flex items-center space-x-2">
                          <Checkbox
                            id={permission}
                            checked={selectedPermissions.includes(permission)}
                            onCheckedChange={() => togglePermission(permission)}
                          />
                          <Label htmlFor={permission} className="capitalize">
                            {permission === "admin"
                              ? "Administrateur (toutes les permissions)"
                              : permission.replace(":", " : ")}
                          </Label>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Annuler
              </Button>
              <Button onClick={handleSaveRole} disabled={isSaving}>
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editingRole ? "Mettre à jour" : "Créer"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </PermissionGate>
  )
}
