// Service Worker pour la persistance d'authentification
// Ce service worker est dédié à la gestion des données d'authentification
// pour permettre une utilisation hors ligne de l'application

// Configuration
const AUTH_SW_VERSION = "v3"
const AUTH_CACHE_NAME = `acr-direct-auth-${AUTH_SW_VERSION}`
const AUTH_DATA_PATH = "/auth-data"
const AUTH_SESSION_PATH = "/auth-session"
const AUTH_TOKEN_PATH = "/auth-token"
const AUTH_USER_PATH = "/auth-user"

// Journalisation
const DEBUG = true
function log(...args) {
  if (DEBUG) {
    console.log("[Auth SW]", ...args)
  }
}

log("Service Worker d'authentification chargé", AUTH_SW_VERSION)

// If there's an import of 'hash', replace it with a native implementation
// For example, if there's:
// import { hash } from 'some-module';
// Remove it and implement a simple hash function if needed:

// Add this function if hash functionality is needed:
function simpleHash(str) {
  let hash = 0
  if (str.length === 0) return hash
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32bit integer
  }
  return hash.toString()
}

// Fonction pour stocker des données dans le cache
async function storeInCache(path, data) {
  try {
    const cache = await caches.open(AUTH_CACHE_NAME)
    const response = new Response(JSON.stringify(data), {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "max-age=604800", // 7 jours
        "X-Auth-SW-Version": AUTH_SW_VERSION,
        "X-Auth-SW-Timestamp": Date.now().toString(),
      },
    })

    await cache.put(path, response)
    log(`Données stockées dans le cache: ${path}`)
    return true
  } catch (error) {
    log(`Erreur lors du stockage dans le cache: ${path}`, error)
    return false
  }
}

// Fonction pour récupérer des données du cache
async function getFromCache(path) {
  try {
    const cache = await caches.open(AUTH_CACHE_NAME)
    const response = await cache.match(path)

    if (!response) {
      log(`Aucune donnée trouvée dans le cache: ${path}`)
      return null
    }

    const data = await response.json()
    log(`Données récupérées du cache: ${path}`)
    return data
  } catch (error) {
    log(`Erreur lors de la récupération depuis le cache: ${path}`, error)
    return null
  }
}

// Écouter les messages du client
self.addEventListener("message", async (event) => {
  if (!event.data || !event.data.type) return

  const { type, payload, timestamp, version } = event.data
  log(`Message reçu: ${type}`)

  // Fonction pour répondre au client
  const respond = (data) => {
    if (event.ports && event.ports[0]) {
      event.ports[0].postMessage(data)
    }
  }

  switch (type) {
    case "AUTH_DATA":
      // Stocker les données d'authentification complètes
      const authData = payload
      const success = await storeInCache(AUTH_DATA_PATH, {
        ...authData,
        timestamp: timestamp || Date.now(),
        version: version || AUTH_SW_VERSION,
      })

      // Stocker également les composants individuels pour un accès plus facile
      if (authData.token) {
        await storeInCache(AUTH_TOKEN_PATH, {
          token: authData.token,
          timestamp: timestamp || Date.now(),
        })
      }

      if (authData.uid) {
        await storeInCache(AUTH_USER_PATH, {
          uid: authData.uid,
          timestamp: timestamp || Date.now(),
        })
      }

      respond({ success, timestamp: Date.now() })
      break

    case "KEEP_AUTH_ALIVE":
      // Mettre à jour le timestamp de la session
      const sessionData = (await getFromCache(AUTH_SESSION_PATH)) || {}
      await storeInCache(AUTH_SESSION_PATH, {
        ...sessionData,
        timestamp: timestamp || Date.now(),
        state: "active",
      })

      respond({ success: true, timestamp: Date.now() })
      break

    case "SAVE_AUTH_STATE":
      // Sauvegarder l'état d'authentification avant fermeture
      await storeInCache(AUTH_SESSION_PATH, {
        uid: event.data.uid,
        timestamp: timestamp || Date.now(),
        state: "closing",
        userData: event.data.userData,
      })

      respond({ success: true, timestamp: Date.now() })
      break

    case "RESTORE_AUTH_STATE":
      // Restaurer l'état d'authentification
      await storeInCache(AUTH_SESSION_PATH, {
        uid: event.data.uid,
        token: event.data.token,
        timestamp: timestamp || Date.now(),
        state: "restored",
        userData: event.data.userData,
      })

      respond({ success: true, timestamp: Date.now() })
      break

    case "FORCE_AUTH_PERSISTENCE":
      // Forcer la persistance maximale
      await storeInCache(AUTH_DATA_PATH, {
        uid: event.data.uid,
        token: event.data.token,
        timestamp: timestamp || Date.now(),
        state: "forced",
        userData: event.data.userData,
        version: version || AUTH_SW_VERSION,
      })

      await storeInCache(AUTH_SESSION_PATH, {
        uid: event.data.uid,
        timestamp: timestamp || Date.now(),
        state: "forced",
      })

      await storeInCache(AUTH_TOKEN_PATH, {
        token: event.data.token,
        timestamp: timestamp || Date.now(),
      })

      await storeInCache(AUTH_USER_PATH, {
        uid: event.data.uid,
        userData: event.data.userData,
        timestamp: timestamp || Date.now(),
      })

      respond({ success: true, timestamp: Date.now() })
      break

    default:
      log(`Type de message non reconnu: ${type}`)
      respond({ success: false, error: "Unknown message type" })
  }
})

// Fonction pour créer une réponse d'erreur
function createErrorResponse(message, status = 404) {
  return new Response(
    JSON.stringify({
      error: message,
      timestamp: Date.now(),
      version: AUTH_SW_VERSION,
    }),
    {
      status,
      headers: {
        "Content-Type": "application/json",
        "X-Auth-SW-Version": AUTH_SW_VERSION,
        "X-Auth-SW-Timestamp": Date.now().toString(),
      },
    },
  )
}

// Intercepter les requêtes pour récupérer les données d'authentification
self.addEventListener("fetch", (event) => {
  const url = new URL(event.request.url)
  const pathname = url.pathname

  // Gérer les différentes routes d'authentification
  if (
    pathname.endsWith(AUTH_DATA_PATH) ||
    pathname.endsWith(AUTH_SESSION_PATH) ||
    pathname.endsWith(AUTH_TOKEN_PATH) ||
    pathname.endsWith(AUTH_USER_PATH)
  ) {
    log(`Requête interceptée: ${pathname}`)

    event.respondWith(
      (async () => {
        try {
          const cache = await caches.open(AUTH_CACHE_NAME)

          // Déterminer le chemin de cache à utiliser
          let cachePath = AUTH_DATA_PATH
          if (pathname.endsWith(AUTH_SESSION_PATH)) cachePath = AUTH_SESSION_PATH
          if (pathname.endsWith(AUTH_TOKEN_PATH)) cachePath = AUTH_TOKEN_PATH
          if (pathname.endsWith(AUTH_USER_PATH)) cachePath = AUTH_USER_PATH

          const response = await cache.match(cachePath)

          if (response) {
            log(`Données trouvées dans le cache: ${cachePath}`)

            // Ajouter des en-têtes pour le débogage
            const headers = new Headers(response.headers)
            headers.set("X-Auth-SW-Version", AUTH_SW_VERSION)
            headers.set("X-Auth-SW-Timestamp", Date.now().toString())
            headers.set("X-Auth-SW-Cache-Hit", "true")

            // Créer une nouvelle réponse avec les en-têtes mis à jour
            return new Response(response.body, {
              status: response.status,
              statusText: response.statusText,
              headers,
            })
          }

          log(`Aucune donnée trouvée dans le cache: ${cachePath}`)
          return createErrorResponse(`No auth data found for ${cachePath}`)
        } catch (error) {
          log(`Erreur lors de la récupération depuis le cache:`, error)
          return createErrorResponse("Internal error in auth service worker", 500)
        }
      })(),
    )
  }

  // Route pour vérifier l'état du service worker
  if (pathname.endsWith("/auth-sw-status")) {
    log("Requête de statut du service worker")

    event.respondWith(
      new Response(
        JSON.stringify({
          version: AUTH_SW_VERSION,
          timestamp: Date.now(),
          active: true,
          paths: [AUTH_DATA_PATH, AUTH_SESSION_PATH, AUTH_TOKEN_PATH, AUTH_USER_PATH],
        }),
        {
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "X-Auth-SW-Version": AUTH_SW_VERSION,
          },
        },
      ),
    )
  }
})

// Fonction pour nettoyer les anciens caches
async function cleanupOldCaches() {
  try {
    const cacheNames = await caches.keys()
    const oldCacheNames = cacheNames.filter((name) => name.startsWith("acr-direct-auth-") && name !== AUTH_CACHE_NAME)

    if (oldCacheNames.length > 0) {
      log(`Nettoyage des anciens caches: ${oldCacheNames.join(", ")}`)
      await Promise.all(oldCacheNames.map((name) => caches.delete(name)))
      log(`${oldCacheNames.length} ancien(s) cache(s) supprimé(s)`)
    }
  } catch (error) {
    log("Erreur lors du nettoyage des anciens caches:", error)
  }
}

// Installer le service worker
self.addEventListener("install", (event) => {
  log("Installation du service worker d'authentification")

  // Précacher les routes essentielles
  event.waitUntil(
    (async () => {
      try {
        // Créer un cache vide pour les données d'authentification
        const cache = await caches.open(AUTH_CACHE_NAME)

        // Précacher la réponse de statut
        await cache.put(
          "/auth-sw-status",
          new Response(
            JSON.stringify({
              version: AUTH_SW_VERSION,
              timestamp: Date.now(),
              active: true,
              installed: true,
            }),
            {
              headers: {
                "Content-Type": "application/json",
                "X-Auth-SW-Version": AUTH_SW_VERSION,
              },
            },
          ),
        )

        log("Précache terminé")

        // Activer immédiatement le nouveau service worker
        await self.skipWaiting()
      } catch (error) {
        log("Erreur lors de l'installation:", error)
      }
    })(),
  )
})

// Activer le service worker
self.addEventListener("activate", (event) => {
  log("Activation du service worker d'authentification")

  event.waitUntil(
    (async () => {
      try {
        // Nettoyer les anciens caches
        await cleanupOldCaches()

        // Prendre le contrôle de tous les clients
        await clients.claim()

        log("Service worker d'authentification activé et contrôlant tous les clients")

        // Notifier tous les clients que le service worker est actif
        const allClients = await clients.matchAll({ includeUncontrolled: true })
        allClients.forEach((client) => {
          client.postMessage({
            type: "AUTH_SW_ACTIVATED",
            version: AUTH_SW_VERSION,
            timestamp: Date.now(),
          })
        })
      } catch (error) {
        log("Erreur lors de l'activation:", error)
      }
    })(),
  )
})
