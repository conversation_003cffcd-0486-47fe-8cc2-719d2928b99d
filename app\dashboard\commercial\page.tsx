"use client"

import { useState, useEffect, useRef } from "react"
import { useAuth } from "@/components/auth-provider"
import { FranceMap } from "@/components/france-map"
import { CommercialContactCard } from "@/components/commercial-contact-card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { getCommercialContactByDepartment, getCommercialContacts } from "@/lib/commercial-utils"
import type { CommercialContact } from "@/lib/commercial-types"
import { getDepartmentName } from "@/lib/commercial-types"
import { Skeleton } from "@/components/ui/skeleton"

export default function CommercialContactPage() {
  const { user, userData } = useAuth()
  const [selectedDepartment, setSelectedDepartment] = useState<string | undefined>(undefined)
  const [displayedContact, setDisplayedContact] = useState<CommercialContact | null>(null)
  const [userCommercialContact, setUserCommercialContact] = useState<CommercialContact | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [highlightedDepartments, setHighlightedDepartments] = useState<string[]>([])
  const [isUserContact, setIsUserContact] = useState(true)
  const mapContainerRef = useRef<HTMLDivElement>(null)

  // Force a re-render when user data changes
  const userDepartment = userData?.department

  // Load user's department and all commercial contacts
  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch all commercial contacts
        const contacts = await getCommercialContacts()

        if (!contacts || contacts.length === 0) {
          setError("Aucun contact commercial n'est disponible dans le système.")
          setLoading(false)
          return
        }

        // Extract all departments that have a commercial contact
        const depts = contacts.flatMap((contact) => contact.departments)
        setHighlightedDepartments(depts)

        // If user has a department, fetch their commercial contact
        if (userDepartment) {
          // Direct lookup in contacts array first (more efficient)
          let userContact = contacts.find((contact) => contact.departments.includes(userDepartment))

          // If not found in the array, try the API call
          if (!userContact) {
            userContact = await getCommercialContactByDepartment(userDepartment)
          }

          if (userContact) {
            setUserCommercialContact(userContact)
            setDisplayedContact(userContact)
            setSelectedDepartment(userDepartment)
            setIsUserContact(true)
          } else {
            setError(`Aucun contact commercial n'est assigné à votre département (${userDepartment}).`)
          }
        } else {
          setError("Vous n'avez pas de département assigné à votre profil.")
        }
      } catch (err) {
        console.error("Error fetching initial data:", err)
        setError("Une erreur est survenue lors du chargement des données.")
      } finally {
        setLoading(false)
      }
    }

    fetchInitialData()
  }, [userDepartment])

  // Fetch commercial contact when department is selected
  const fetchCommercialContact = async (departmentCode: string) => {
    if (departmentCode === selectedDepartment && !isUserContact) return

    setLoading(true)
    setError(null)

    try {
      const contact = await getCommercialContactByDepartment(departmentCode)

      if (contact) {
        setDisplayedContact(contact)
        setIsUserContact(userDepartment === departmentCode)
      } else {
        setError(
          `Aucun contact commercial n'est assigné au département ${departmentCode} (${getDepartmentName(departmentCode)}).`,
        )
      }
    } catch (err) {
      console.error("Error fetching commercial contact:", err)
      setError("Une erreur est survenue lors de la récupération du contact commercial.")
    } finally {
      setLoading(false)
    }
  }

  const handleSelectDepartment = (departmentCode: string) => {
    setSelectedDepartment(departmentCode)
    fetchCommercialContact(departmentCode)
  }

  const resetToUserContact = () => {
    if (userDepartment && userCommercialContact) {
      setSelectedDepartment(userDepartment)
      setDisplayedContact(userCommercialContact)
      setIsUserContact(true)
      setError(null)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Votre contact local</h1>
        <p className="text-muted-foreground"></p>
      </div>

      {/* Contact commercial display area - above the map */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {isUserContact ? "" : selectedDepartment ? getDepartmentName(selectedDepartment) : ""}
          </h2>

          {!isUserContact && userCommercialContact && (
            <Button variant="outline" onClick={resetToUserContact} className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Revenir à mon contact
            </Button>
          )}
        </div>

        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-12 w-3/4" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Aucun contact trouvé</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : displayedContact ? (
          <CommercialContactCard contact={displayedContact} showDepartments={true} />
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              Aucun contact commercial n'est disponible. Veuillez sélectionner un département sur la carte.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Map section - full width and height */}
      <div className="w-full" ref={mapContainerRef}>
        <div className="relative w-full h-[50vh] overflow-hidden">
          <FranceMap
            selectedDepartment={selectedDepartment}
            onSelectDepartment={handleSelectDepartment}
            highlightedDepartments={highlightedDepartments}
          />
        </div>
      </div>
    </div>
  )
}
