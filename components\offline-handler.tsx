"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { WifiOff, Wifi } from "lucide-react"
import { ConnectionStatus } from "@/components/connection-status"

interface OfflineHandlerProps {
  redirectToOfflinePage?: boolean
  showAlert?: boolean
  showStatusBadge?: boolean
}

/**
 * Composant qui gère l'état hors ligne de l'application
 * @param redirectToOfflinePage Si true, redirige vers la page hors ligne en cas de perte de connexion
 * @param showAlert Si true, affiche une alerte en cas de perte ou de récupération de connexion
 * @param showStatusBadge Si true, affiche un badge de statut de connexion
 */
export function OfflineHandler({
  redirectToOfflinePage = false,
  showAlert = true,
  showStatusBadge = true,
}: OfflineHandlerProps) {
  const router = useRouter()
  const [isOnline, setIsOnline] = useState<boolean>(true)
  const [wasOffline, setWasOffline] = useState<boolean>(false)
  const [showOfflineAlert, setShowOfflineAlert] = useState<boolean>(false)
  const [showOnlineAlert, setShowOnlineAlert] = useState<boolean>(false)

  // Gérer les changements d'état de connexion
  const handleOnlineStatusChange = () => {
    const online = navigator.onLine

    // Si nous passons de hors ligne à en ligne
    if (!isOnline && online) {
      setWasOffline(true)
      if (showAlert) {
        setShowOnlineAlert(true)
        setTimeout(() => setShowOnlineAlert(false), 5000)
      }
    }

    // Si nous passons de en ligne à hors ligne
    if (isOnline && !online) {
      if (showAlert) {
        setShowOfflineAlert(true)
        setTimeout(() => setShowOfflineAlert(false), 5000)
      }

      // Rediriger vers la page hors ligne si demandé
      if (redirectToOfflinePage) {
        router.push("/offline")
      }
    }

    setIsOnline(online)
  }

  useEffect(() => {
    // Vérifier l'état initial
    setIsOnline(navigator.onLine)

    // Ajouter les écouteurs d'événements
    window.addEventListener("online", handleOnlineStatusChange)
    window.addEventListener("offline", handleOnlineStatusChange)

    return () => {
      window.removeEventListener("online", handleOnlineStatusChange)
      window.removeEventListener("offline", handleOnlineStatusChange)
    }
  }, [isOnline])

  return (
    <>
      {/* Alerte hors ligne */}
      {showOfflineAlert && (
        <Alert
          variant="warning"
          className="fixed bottom-4 right-4 z-40 max-w-md shadow-lg animate-in fade-in slide-in-from-bottom-5"
        >
          <WifiOff className="h-4 w-4 mr-2" />
          <AlertTitle>Vous êtes hors ligne</AlertTitle>
          <AlertDescription>
            L'application continue de fonctionner avec les données en cache.
            {redirectToOfflinePage && " Redirection vers la page hors ligne..."}
          </AlertDescription>
        </Alert>
      )}

      {/* Alerte retour en ligne */}
      {showOnlineAlert && (
        <Alert
          variant="default"
          className="fixed bottom-4 right-4 z-40 max-w-md shadow-lg bg-green-50 border-green-200 animate-in fade-in slide-in-from-bottom-5"
        >
          <Wifi className="h-4 w-4 mr-2 text-green-600" />
          <AlertTitle>Connexion rétablie</AlertTitle>
          <AlertDescription>
            Vous êtes de nouveau connecté au serveur. Les données sont en cours de synchronisation.
          </AlertDescription>
        </Alert>
      )}

      {/* Badge de statut de connexion */}
      {showStatusBadge && <ConnectionStatus showStatusBadge={true} showOfflineAlert={false} />}
    </>
  )
}

export default OfflineHandler
