/**
 * Tests automatisés pour le fonctionnement hors ligne
 * Ce script teste le fonctionnement de l'application en mode hors ligne
 * en utilisant Puppeteer pour simuler un navigateur sans connexion
 */

const puppeteer = require("puppeteer")
const fs = require("fs")
const path = require("path")

// Configuration
const config = {
  baseUrl: "http://localhost:3000",
  loginEmail: "<EMAIL>",
  loginPassword: "password",
  outputDir: path.join(__dirname, "reports"),
  screenshotDir: path.join(__dirname, "screenshots"),
  timeout: 30000, // 30 secondes
}

// Créer les répertoires de sortie s'ils n'existent pas
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true })
}
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true })
}

// Fonction pour générer un rapport
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, "-")
  const reportPath = path.join(config.outputDir, `offline-test-report-${timestamp}.json`)

  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
  console.log(`Rapport généré: ${reportPath}`)

  // Générer un rapport HTML
  const htmlReportPath = path.join(config.outputDir, `offline-test-report-${timestamp}.html`)
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de test hors ligne</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .summary { display: flex; margin-bottom: 20px; }
        .summary-box { flex: 1; padding: 15px; margin: 0 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .failure { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .test-case { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .test-case h3 { margin-top: 0; }
        .passed { border-left: 5px solid #28a745; }
        .failed { border-left: 5px solid #dc3545; }
        .skipped { border-left: 5px solid #ffc107; }
        .error { color: #dc3545; }
        .steps { margin-left: 20px; }
        .step { margin-bottom: 5px; }
        .step-passed { color: #28a745; }
        .step-failed { color: #dc3545; }
        .screenshot { max-width: 100%; height: auto; border: 1px solid #ddd; margin-top: 10px; }
      </style>
    </head>
    <body>
      <h1>Rapport de test hors ligne</h1>
      <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
      <p><strong>URL de base:</strong> ${config.baseUrl}</p>
      
      <div class="summary">
        <div class="summary-box ${results.success ? "success" : "failure"}">
          <h2>Résultat global</h2>
          <p>${results.success ? "Succès" : "Échec"}</p>
          <p>${results.testsPassed}/${results.totalTests} tests réussis</p>
        </div>
        <div class="summary-box info">
          <h2>Durée</h2>
          <p>${results.duration} secondes</p>
        </div>
      </div>
      
      <h2>Détails des tests</h2>
      ${results.tests
        .map(
          (test) => `
        <div class="test-case ${test.success ? "passed" : "failed"}">
          <h3>${test.name}</h3>
          <p><strong>Statut:</strong> ${test.success ? "Réussi" : "Échoué"}</p>
          ${test.error ? `<p class="error"><strong>Erreur:</strong> ${test.error}</p>` : ""}
          <div class="steps">
            ${test.steps
              .map(
                (step) => `
              <div class="step ${step.success ? "step-passed" : "step-failed"}">
                ${step.success ? "✓" : "✗"} ${step.name}
                ${step.error ? `<div class="error">${step.error}</div>` : ""}
              </div>
            `,
              )
              .join("")}
          </div>
          ${test.screenshot ? `<img class="screenshot" src="../screenshots/${path.basename(test.screenshot)}" alt="Capture d'écran">` : ""}
        </div>
      `,
        )
        .join("")}
    </body>
    </html>
  `

  fs.writeFileSync(htmlReportPath, htmlContent)
  console.log(`Rapport HTML généré: ${htmlReportPath}`)

  return reportPath
}

// Fonction principale pour exécuter les tests
async function runTests() {
  console.log("Démarrage des tests de fonctionnement hors ligne...")

  const startTime = Date.now()
  const browser = await puppeteer.launch({
    headless: false, // Mettre à true pour exécuter sans interface graphique
    defaultViewport: { width: 1280, height: 800 },
    args: ["--window-size=1280,800"],
  })

  const results = {
    timestamp: new Date().toISOString(),
    baseUrl: config.baseUrl,
    success: false,
    testsPassed: 0,
    totalTests: 0,
    duration: 0,
    tests: [],
  }

  try {
    const page = await browser.newPage()
    page.setDefaultTimeout(config.timeout)

    // Activer la journalisation de la console
    page.on("console", (msg) => console.log(`[Page Console] ${msg.text()}`))

    // Test 1: Connexion et préchargement des données
    const loginTest = {
      name: "Connexion et préchargement des données",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
    }

    try {
      // Étape 1: Accéder à la page de connexion
      loginTest.steps.push({ name: "Accès à la page de connexion", success: false })
      await page.goto(`${config.baseUrl}/`, { waitUntil: "networkidle2" })
      loginTest.steps[0].success = true

      // Étape 2: Remplir le formulaire de connexion
      loginTest.steps.push({ name: "Remplissage du formulaire de connexion", success: false })
      await page.waitForSelector('input[type="email"]')
      await page.type('input[type="email"]', config.loginEmail)
      await page.type('input[type="password"]', config.loginPassword)
      loginTest.steps[1].success = true

      // Étape 3: Soumettre le formulaire
      loginTest.steps.push({ name: "Soumission du formulaire", success: false })
      await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('button[type="submit"]')])
      loginTest.steps[2].success = true

      // Étape 4: Vérifier la redirection vers le tableau de bord
      loginTest.steps.push({ name: "Vérification de la redirection vers le tableau de bord", success: false })
      const url = page.url()
      if (!url.includes("/dashboard")) {
        throw new Error(`Redirection incorrecte: ${url}`)
      }
      loginTest.steps[3].success = true

      // Étape 5: Attendre le préchargement des données
      loginTest.steps.push({ name: "Attente du préchargement des données", success: false })
      await page.waitForFunction(() => !document.querySelector(".loading-indicator"), { timeout: config.timeout })
      await page.waitForTimeout(5000) // Attendre 5 secondes pour le préchargement en arrière-plan
      loginTest.steps[4].success = true

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "login-success.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      loginTest.screenshot = screenshotPath

      loginTest.success = true
    } catch (error) {
      loginTest.error = error.message
      console.error("Erreur lors du test de connexion:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "login-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      loginTest.screenshot = screenshotPath
    }

    results.tests.push(loginTest)
    results.totalTests++
    if (loginTest.success) results.testsPassed++

    // Test 2: Navigation en mode hors ligne
    const offlineNavigationTest = {
      name: "Navigation en mode hors ligne",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
    }

    try {
      // Ne pas exécuter ce test si la connexion a échoué
      if (!loginTest.success) {
        offlineNavigationTest.error = "Test ignoré car la connexion a échoué"
        offlineNavigationTest.steps.push({ name: "Test ignoré", success: false })
        throw new Error("Test ignoré car la connexion a échoué")
      }

      // Étape 1: Passer en mode hors ligne
      offlineNavigationTest.steps.push({ name: "Passage en mode hors ligne", success: false })
      await page.setOfflineMode(true)
      await page.waitForTimeout(1000) // Attendre que le mode hors ligne soit détecté
      offlineNavigationTest.steps[0].success = true

      // Étape 2: Naviguer vers la page d'actualités
      offlineNavigationTest.steps.push({ name: "Navigation vers la page d'actualités", success: false })
      await Promise.all([
        page.waitForNavigation({ waitUntil: "networkidle2" }),
        page.click('a[href="/dashboard/news"]'),
      ])
      offlineNavigationTest.steps[1].success = true

      // Étape 3: Vérifier que la page d'actualités est chargée
      offlineNavigationTest.steps.push({ name: "Vérification du chargement de la page d'actualités", success: false })
      await page.waitForSelector("h1")
      const pageTitle = await page.$eval("h1", (el) => el.textContent)
      if (!pageTitle.includes("Actualités")) {
        throw new Error(`Titre de page incorrect: ${pageTitle}`)
      }
      offlineNavigationTest.steps[2].success = true

      // Étape 4: Naviguer vers la page des favoris
      offlineNavigationTest.steps.push({ name: "Navigation vers la page des favoris", success: false })
      await Promise.all([
        page.waitForNavigation({ waitUntil: "networkidle2" }),
        page.click('a[href="/dashboard/favorites"]'),
      ])
      offlineNavigationTest.steps[3].success = true

      // Étape 5: Vérifier que la page des favoris est chargée
      offlineNavigationTest.steps.push({ name: "Vérification du chargement de la page des favoris", success: false })
      await page.waitForSelector("h1")
      const favoritesTitle = await page.$eval("h1", (el) => el.textContent)
      if (!favoritesTitle.includes("Favoris")) {
        throw new Error(`Titre de page incorrect: ${favoritesTitle}`)
      }
      offlineNavigationTest.steps[4].success = true

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "offline-navigation.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      offlineNavigationTest.screenshot = screenshotPath

      offlineNavigationTest.success = true
    } catch (error) {
      offlineNavigationTest.error = error.message
      console.error("Erreur lors du test de navigation hors ligne:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "offline-navigation-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      offlineNavigationTest.screenshot = screenshotPath
    }

    results.tests.push(offlineNavigationTest)
    results.totalTests++
    if (offlineNavigationTest.success) results.testsPassed++

    // Test 3: Retour en ligne et synchronisation
    const onlineSyncTest = {
      name: "Retour en ligne et synchronisation",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
    }

    try {
      // Ne pas exécuter ce test si la navigation hors ligne a échoué
      if (!offlineNavigationTest.success) {
        onlineSyncTest.error = "Test ignoré car la navigation hors ligne a échoué"
        onlineSyncTest.steps.push({ name: "Test ignoré", success: false })
        throw new Error("Test ignoré car la navigation hors ligne a échoué")
      }

      // Étape 1: Revenir en mode en ligne
      onlineSyncTest.steps.push({ name: "Retour en mode en ligne", success: false })
      await page.setOfflineMode(false)
      await page.waitForTimeout(2000) // Attendre que le mode en ligne soit détecté
      onlineSyncTest.steps[0].success = true

      // Étape 2: Vérifier l'indicateur de synchronisation
      onlineSyncTest.steps.push({ name: "Vérification de l'indicateur de synchronisation", success: false })
      // Attendre que l'indicateur de synchronisation apparaisse puis disparaisse
      await page.waitForTimeout(5000) // Attendre la synchronisation
      onlineSyncTest.steps[1].success = true

      // Étape 3: Naviguer vers le tableau de bord
      onlineSyncTest.steps.push({ name: "Navigation vers le tableau de bord", success: false })
      await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('a[href="/dashboard"]')])
      onlineSyncTest.steps[2].success = true

      // Étape 4: Vérifier que le tableau de bord est chargé
      onlineSyncTest.steps.push({ name: "Vérification du chargement du tableau de bord", success: false })
      await page.waitForSelector("h1")
      const dashboardTitle = await page.$eval("h1", (el) => el.textContent)
      if (!dashboardTitle.includes("Tableau de bord") && !dashboardTitle.includes("Bienvenue")) {
        throw new Error(`Titre de page incorrect: ${dashboardTitle}`)
      }
      onlineSyncTest.steps[3].success = true

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "online-sync.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      onlineSyncTest.screenshot = screenshotPath

      onlineSyncTest.success = true
    } catch (error) {
      onlineSyncTest.error = error.message
      console.error("Erreur lors du test de retour en ligne:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "online-sync-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      onlineSyncTest.screenshot = screenshotPath
    }

    results.tests.push(onlineSyncTest)
    results.totalTests++
    if (onlineSyncTest.success) results.testsPassed++

    // Calculer le résultat global
    results.success = results.testsPassed === results.totalTests
    results.duration = ((Date.now() - startTime) / 1000).toFixed(2)

    // Générer le rapport
    generateReport(results)
  } catch (error) {
    console.error("Erreur lors de l'exécution des tests:", error)
    results.error = error.message
  } finally {
    await browser.close()
  }

  return results
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  runTests()
    .then((results) => {
      console.log(`Tests terminés: ${results.testsPassed}/${results.totalTests} réussis`)
      process.exit(results.success ? 0 : 1)
    })
    .catch((error) => {
      console.error("Erreur lors de l'exécution des tests:", error)
      process.exit(1)
    })
}

module.exports = { runTests }
