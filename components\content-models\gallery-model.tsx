"use client"

import type React from "react"

import { useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { ImagePlus, X, Loader2, MoveUp, MoveDown } from "lucide-react"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export interface GalleryImage {
  id: string
  url: string
  file?: File
  altText: string
}

interface GalleryModelProps {
  images: GalleryImage[]
  onImagesChange: (images: GalleryImage[]) => void
  columns?: number
  onColumnsChange?: (columns: number) => void
  isUploading?: boolean
}

export function GalleryModel({
  images,
  onImagesChange,
  columns = 4,
  onColumnsChange = () => {},
  isUploading = false,
}: GalleryModelProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages: GalleryImage[] = [...images]

      Array.from(e.target.files).forEach((file) => {
        // Create a temporary URL for preview
        const url = URL.createObjectURL(file)
        newImages.push({
          id: `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          url,
          file,
          altText: "",
        })
      })

      onImagesChange(newImages)

      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleRemoveImage = (index: number) => {
    const newImages = [...images]
    newImages.splice(index, 1)
    onImagesChange(newImages)
  }

  const handleAltTextChange = (index: number, altText: string) => {
    const newImages = [...images]
    newImages[index].altText = altText
    onImagesChange(newImages)
  }

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images]
    const [movedItem] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, movedItem)
    onImagesChange(newImages)
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const sourceIndex = result.source.index
    const destinationIndex = result.destination.index

    moveImage(sourceIndex, destinationIndex)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="gallery-columns">Nombre de colonnes</Label>
        <Select value={columns.toString()} onValueChange={(value) => onColumnsChange(Number.parseInt(value))}>
          <SelectTrigger id="gallery-columns" className="w-full sm:w-40">
            <SelectValue placeholder="Colonnes" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 colonne</SelectItem>
            <SelectItem value="2">2 colonnes</SelectItem>
            <SelectItem value="3">3 colonnes</SelectItem>
            <SelectItem value="4">4 colonnes</SelectItem>
            <SelectItem value="5">5 colonnes</SelectItem>
            <SelectItem value="6">6 colonnes</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Définit le nombre de colonnes dans la galerie sur les grands écrans. Sur mobile, le nombre de colonnes sera
          automatiquement réduit.
        </p>
      </div>

      <div className="space-y-2">
        <Label>Images de la galerie</Label>
        <div className="flex flex-col gap-4">
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="gallery-images">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                  {images.map((image, index) => (
                    <Draggable key={image.id} draggableId={image.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="flex flex-col sm:flex-row gap-4 p-4 border rounded-lg bg-card"
                        >
                          <div className="relative w-full sm:w-32 h-32">
                            <img
                              src={image.url || "/placeholder.svg"}
                              alt={image.altText || "Image de galerie"}
                              className="object-cover w-full h-full rounded-md"
                            />
                            <Button
                              type="button"
                              size="icon"
                              variant="destructive"
                              className="absolute top-2 right-2 h-6 w-6 rounded-full"
                              onClick={() => handleRemoveImage(index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="flex-1 space-y-2">
                            <Label htmlFor={`alt-text-${index}`} className="text-xs">
                              Texte alternatif
                            </Label>
                            <Input
                              id={`alt-text-${index}`}
                              value={image.altText}
                              onChange={(e) => handleAltTextChange(index, e.target.value)}
                              placeholder="Description de l'image"
                              className="h-9"
                            />
                            <div className="flex gap-2 mt-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => moveImage(index, Math.max(0, index - 1))}
                                disabled={index === 0}
                                className="h-8 px-2"
                              >
                                <MoveUp className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => moveImage(index, Math.min(images.length - 1, index + 1))}
                                disabled={index === images.length - 1}
                                className="h-8 px-2"
                              >
                                <MoveDown className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="w-full py-8"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Téléchargement...
              </>
            ) : (
              <>
                <ImagePlus className="mr-2 h-4 w-4" />
                Ajouter des images
              </>
            )}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      </div>
    </div>
  )
}
