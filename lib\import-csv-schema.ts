// Schéma pour les données importées du CSV
import { normalizeSiret, normalizeClientCode } from "./user-matching-utils"

export interface ImportedUserData {
  clientCode: string // N° client
  clientType: string // Type de client (groupe principal)
  network: string // Réseau
  department: string // Département
  name: string // Nom
  siretLastDigits: string // 5 derniers SIRET
  additionalGroups: string[] // Groupes supplémentaires (toujours un tableau, même vide)
}

// Fonction pour valider et transformer les données CSV
export function validateCsvRow(row: Record<string, string>): ImportedUserData | null {
  // Vérifier que les champs obligatoires sont présents
  if (!row["N° client"] || !row["Type de client"] || !row["5 derniers SIRET"]) {
    return null
  }

  // Normaliser le code client
  const clientCode = normalizeClientCode(row["N° client"].trim())

  // Normaliser les 5 derniers chiffres du SIRET
  const siretLastDigits = normalizeSiret(row["5 derniers SIRET"].trim())

  // Extraire les groupes supplémentaires s'ils existent
  let additionalGroups: string[] = []
  if (row["Groupes supplémentaires"] && row["Groupes supplémentaires"].trim() !== "") {
    additionalGroups = row["Groupes supplémentaires"]
      .split(",")
      .map((group) => group.trim())
      .filter((group) => group.length > 0)
  }

  // Retourner les données structurées
  return {
    clientCode,
    clientType: row["Type de client"].trim(),
    network: row["Réseau"] ? row["Réseau"].trim() : "",
    department: row["Département"] ? row["Département"].trim() : "",
    name: row["Nom"] ? row["Nom"].trim() : "",
    siretLastDigits,
    additionalGroups, // Toujours un tableau (vide si pas de groupes)
  }
}
