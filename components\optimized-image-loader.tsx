"use client"

import { useEffect, useState } from "react"
import Image, { type ImageProps } from "next/image"
import { cacheImages } from "@/app/sw-register"

interface OptimizedImageProps extends Omit<ImageProps, "onLoadingComplete"> {
  fallbackSrc?: string
  preload?: boolean
}

export default function OptimizedImage({
  src,
  alt,
  fallbackSrc = "/placeholder.svg",
  preload = true,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [imageSrc, setImageSrc] = useState<string | null>(null)

  useEffect(() => {
    // Vérifier si l'image est déjà en cache
    if (typeof window !== "undefined" && "caches" in window && typeof src === "string") {
      caches.match(src).then((response) => {
        if (response) {
          // L'image est déjà en cache, l'utiliser directement
          setImageSrc(src)
          setIsLoaded(true)
        } else if (preload) {
          // Précharger l'image si elle n'est pas en cache
          cacheImages([src]).then(() => {
            setImageSrc(src)
            setIsLoaded(true)
          })
        } else {
          // Utiliser l'image normalement
          setImageSrc(src)
        }
      })
    } else {
      // Pas de support de cache, utiliser l'image normalement
      setImageSrc(src)
    }
  }, [src, preload])

  return (
    <div className={`relative ${!isLoaded ? "bg-gray-100 animate-pulse" : ""}`}>
      {imageSrc && (
        <Image
          src={imageSrc || "/placeholder.svg"}
          alt={alt}
          onLoadingComplete={() => setIsLoaded(true)}
          onError={() => {
            if (fallbackSrc && fallbackSrc !== src) {
              setImageSrc(fallbackSrc)
            }
          }}
          {...props}
        />
      )}
    </div>
  )
}
