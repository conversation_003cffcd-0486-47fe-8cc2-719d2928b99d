import { initializeApp, getApps, getApp } from "firebase/app"
import { getAuth, setPersistence, browserLocalPersistence } from "firebase/auth"
import {
  initializeFirestore,
  persistentLocalCache,
  persistentMultipleTabManager,
  doc,
  getDoc,
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  addDoc,
  setDoc,
  deleteDoc,
  serverTimestamp,
} from "firebase/firestore"
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"

// Vérifier si nous sommes côté client
const isBrowser = typeof window !== "undefined"

// Vérifier si Firebase est déjà initialisé
let firebaseInitialized = false

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
}

// Initialize Firebase une seule fois
let app
try {
  app = !getApps().length ? initializeApp(firebaseConfig) : getApp()
  firebaseInitialized = true
} catch (error) {
  console.error("Erreur lors de l'initialisation de Firebase:", error)
  firebaseInitialized = false
}

// Initialiser Auth avec lazy loading comme pour db et storage
let _auth: ReturnType<typeof getAuth> | null = null
export const auth = () => {
  // Ne pas initialiser l'auth côté serveur
  if (!isBrowser || !firebaseInitialized) {
    console.warn("Tentative d'accès à auth côté serveur ou Firebase non initialisé. Retour d'un objet factice.")
    return {
      currentUser: null,
      onAuthStateChanged: () => () => {},
    } as any
  }

  if (!_auth) {
    try {
      _auth = getAuth(app)

      // Configurer la persistance une seule fois
      setPersistence(_auth, browserLocalPersistence)
        .then(() => {
          console.log("Firebase Auth persistence set to browserLocalPersistence")

          // Stocker un indicateur dans le localStorage pour vérifier la persistance
          if (typeof localStorage !== "undefined") {
            localStorage.setItem("auth_persistence_enabled", "true")
          }
        })
        .catch((error) => {
          console.error("Error setting auth persistence:", error)
        })

      // Rafraîchir périodiquement le token sur mobile pour maintenir la session active
      if (isBrowser && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        console.log("Environnement mobile détecté, configuration du rafraîchissement périodique")

        // Rafraîchir le token périodiquement uniquement sur mobile
        setInterval(
          () => {
            const currentUser = _auth?.currentUser
            if (currentUser) {
              // Rafraîchir le token pour maintenir la session active
              currentUser
                .getIdToken(true)
                .then((token) => {
                  // Stocker le token dans le localStorage pour une persistance supplémentaire
                  if (typeof localStorage !== "undefined") {
                    localStorage.setItem("firebase_auth_token", token)
                    localStorage.setItem("firebase_auth_last_refresh", Date.now().toString())
                  }
                })
                .catch(console.error)
            }
          },
          15 * 60 * 1000,
        ) // Toutes les 15 minutes
      }

      // Désactiver reCAPTCHA pour les environnements de développement
      if (process.env.NODE_ENV === "development" || window.location.hostname.includes("vercel.app")) {
        _auth.settings = {
          ..._auth.settings,
          appVerificationDisabledForTesting: true,
        }
      }
    } catch (error) {
      console.error("Erreur lors de l'initialisation de Firebase Auth:", error)
      // Retourner un objet factice en cas d'erreur
      return {
        currentUser: null,
        onAuthStateChanged: () => () => {},
      } as any
    }
  }
  return _auth
}

// Initialiser Firestore
let _db: any = null
export const db = () => {
  // Ne pas initialiser Firestore côté serveur ou si Firebase n'est pas initialisé
  if (!isBrowser || !firebaseInitialized) {
    console.warn("Tentative d'accès à Firestore côté serveur ou Firebase non initialisé. Retour d'un objet factice.")
    return {
      collection: () => ({
        doc: () => ({
          get: () => Promise.resolve({ exists: false, data: () => ({}) }),
          onSnapshot: () => () => {},
        }),
      }),
      doc: () => ({
        get: () => Promise.resolve({ exists: false, data: () => ({}) }),
        onSnapshot: () => () => {},
      }),
    } as any
  }

  if (!_db) {
    try {
      // Initialiser Firestore avec la configuration de cache persistant
      _db = initializeFirestore(app, {
        cache: persistentLocalCache({
          tabManager: persistentMultipleTabManager(),
        }),
      })

      // Set up error listener for Firestore
      const originalConsoleError = console.error
      console.error = (...args) => {
        // Check if this is a Firebase connection error
        const errorString = args.join(" ")
        if (
          errorString.includes("Firestore") &&
          (errorString.includes("backend") || errorString.includes("offline") || errorString.includes("unavailable"))
        ) {
          // Store the timestamp of the last Firebase error
          localStorage.setItem("lastFirebaseError", Date.now().toString())
        }
        originalConsoleError.apply(console, args)
      }
    } catch (error) {
      console.error("Erreur lors de l'initialisation de Firestore:", error)
      // Retourner un objet factice en cas d'erreur
      return {
        collection: () => ({
          doc: () => ({
            get: () => Promise.resolve({ exists: false, data: () => ({}) }),
            onSnapshot: () => () => {},
          }),
        }),
        doc: () => ({
          get: () => Promise.resolve({ exists: false, data: () => ({}) }),
          onSnapshot: () => () => {},
        }),
      } as any
    }
  }
  return _db
}

// Initialiser Storage
let _storage: any = null
export const storage = () => {
  // Ne pas initialiser Storage côté serveur ou si Firebase n'est pas initialisé
  if (!isBrowser || !firebaseInitialized) {
    console.warn("Tentative d'accès à Storage côté serveur ou Firebase non initialisé. Retour d'un objet factice.")
    return {
      ref: () => ({
        put: () => Promise.resolve({}),
        getDownloadURL: () => Promise.resolve(""),
        delete: () => Promise.resolve(),
      }),
    } as any
  }

  if (!_storage) {
    try {
      _storage = getStorage(app)
    } catch (error) {
      console.error("Erreur lors de l'initialisation de Storage:", error)
      // Retourner un objet factice en cas d'erreur
      return {
        ref: () => ({
          put: () => Promise.resolve({}),
          getDownloadURL: () => Promise.resolve(""),
          delete: () => Promise.resolve(),
        }),
      } as any
    }
  }
  return _storage
}

// Helper functions for network connectivity
export async function enableFirestoreNetwork(): Promise<boolean> {
  if (!isBrowser || !firebaseInitialized) return false

  try {
    const { enableNetwork } = await import("firebase/firestore")
    await enableNetwork(db())
    return true
  } catch (error) {
    console.error("Erreur lors de l'activation du réseau Firestore:", error)
    return false
  }
}

export async function disableFirestoreNetwork(): Promise<boolean> {
  if (!isBrowser || !firebaseInitialized) return false

  try {
    const { disableNetwork } = await import("firebase/firestore")
    await disableNetwork(db())
    return true
  } catch (error) {
    console.error("Erreur lors de la désactivation du réseau Firestore:", error)
    return false
  }
}

export async function checkFirestoreNetworkStatus(): Promise<boolean> {
  if (!isBrowser || !firebaseInitialized) return false

  try {
    // Tenter une opération simple pour vérifier la connectivité
    await getDocs(query(collection(db(), "settings"), limit(1)))
    return true
  } catch (error) {
    console.error("Erreur lors de la vérification du réseau Firestore:", error)
    return false
  }
}

export {
  doc,
  getDoc,
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  addDoc,
  setDoc,
  deleteDoc,
  serverTimestamp,
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
  app,
}
