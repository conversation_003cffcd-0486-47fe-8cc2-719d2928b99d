"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db } from "@/lib/firebase"
import { collection, query, orderBy, getDocs, doc, updateDoc, getDoc, deleteDoc, setDoc } from "firebase/firestore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Pencil, Trash2, Shield, ShieldOff, Eye, EyeOff, AlertTriangle } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useGroups } from "@/lib/hooks"
import { PermissionGate } from "@/components/permission-gate"
import { PERMISSIONS } from "@/lib/permissions"
import { type Role, PREDEFINED_ROLES } from "@/lib/roles"
import { cn } from "@/lib/utils"

// Ajoutez ces imports en haut du fichier
import { UserDeletionWarning } from "@/components/user-deletion-warning"
import { checkUserAssociatedData } from "@/lib/user-service"

interface User {
  id: string
  email: string
  displayName: string
  photoURL?: string
  isAdmin: boolean
  groups: string[]
  roles: string[]
  createdAt: any
  lastLogin?: any
  isActive?: boolean
  isPending?: boolean
  registrationSource?: "pre-registered" | "manual"
}

export default function UsersPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const { groups } = useGroups()
  const [availableRoles, setAvailableRoles] = useState<Role[]>([])
  const [isDeleting, setIsDeleting] = useState(false)
  const [authDeleteError, setAuthDeleteError] = useState<string | null>(null)

  // Ajoutez cet état pour stocker les informations sur les données associées
  const [userDataInfo, setUserDataInfo] = useState<{ userId: string; count: number } | null>(null)

  // Ajoutez cet état pour contrôler l'ouverture de la boîte de dialogue
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)

  // Add state for error dialog
  const [errorDialogOpen, setErrorDialogOpen] = useState(false)
  const [errorDetails, setErrorDetails] = useState("")

  useEffect(() => {
    fetchUsers()
    fetchRoles()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredUsers(users)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredUsers(
        users.filter(
          (user) =>
            user.displayName?.toLowerCase().includes(lowercasedSearch) ||
            user.email?.toLowerCase().includes(lowercasedSearch),
        ),
      )
    }
  }, [searchTerm, users])

  const fetchRoles = async () => {
    try {
      // Get predefined roles
      const predefinedRoles = Object.values(PREDEFINED_ROLES)

      try {
        // Get custom roles from Firestore
        const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
        let customRoles: Role[] = []

        if (rolesDoc.exists()) {
          customRoles = rolesDoc.data().roles || []
        } else {
          // If document doesn't exist, initialize it
          await setDoc(doc(db(), "settings", "roles"), { roles: [] }, { merge: true })
        }

        // Combine predefined and custom roles
        setAvailableRoles([...predefinedRoles, ...customRoles])
      } catch (error) {
        console.error("Error fetching custom roles:", error)
        // Fall back to predefined roles
        setAvailableRoles(predefinedRoles)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des rôles:", error)
      // Ensure we at least have predefined roles
      setAvailableRoles(Object.values(PREDEFINED_ROLES))
    }
  }

  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      const usersQuery = query(collection(db(), "users"), orderBy("displayName"))
      const querySnapshot = await getDocs(usersQuery)

      const usersData: User[] = []
      querySnapshot.forEach((doc) => {
        const userData = doc.data()
        usersData.push({
          id: doc.id,
          ...userData,
          roles: userData.roles || [],
        } as User)
      })

      setUsers(usersData)
      setFilteredUsers(usersData)
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les utilisateurs",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Updated handleDelete function with better error handling
  const handleDelete = async (id: string) => {
    try {
      // Reset previous errors
      setAuthDeleteError(null)
      setIsDeleting(true)

      // Show a loading toast
      const loadingToastId = toast({
        title: "Suppression en cours",
        description: "Suppression de l'utilisateur et de ses données...",
      }).id

      // Step 1: Delete the user from Firestore first
      try {
        await deleteDoc(doc(db(), "users", id))
        console.log("User document deleted from Firestore")
      } catch (firestoreError) {
        console.error("Error deleting user from Firestore:", firestoreError)
        toast({
          title: "Erreur",
          description: "Impossible de supprimer l'utilisateur de la base de données. Veuillez réessayer.",
          variant: "destructive",
        })
        throw firestoreError // Propagate the error
      }

      // Step 2: Try to delete the user from Firebase Authentication
      let authSuccess = false
      try {
        console.log(`Sending delete request to API for user ${id}`)
        const response = await fetch(`/api/delete-user?uid=${id}`, {
          method: "DELETE",
        })

        // Handle the response
        const text = await response.text()
        console.log(`API response text: ${text.substring(0, 100)}${text.length > 100 ? "..." : ""}`)

        let result
        try {
          result = JSON.parse(text)
        } catch (jsonError) {
          console.error("Failed to parse API response as JSON:", jsonError)
          result = {
            success: false,
            error: "Invalid response format",
            details: text.substring(0, 200), // Include part of the response for debugging
          }
        }

        if (!response.ok || !result.success) {
          console.error("Failed to delete user from Firebase Auth:", result)

          // Store error details for potential display
          setAuthDeleteError(result.details || result.error || "Unknown error")

          // Show a warning toast
          toast({
            id: "auth-delete-warning",
            title: "Avertissement",
            description: "L'utilisateur a été supprimé de la base de données mais pas de Firebase Authentication",
            variant: "warning",
            action: (
              <Button variant="outline" size="sm" onClick={() => setErrorDialogOpen(true)}>
                Détails
              </Button>
            ),
          })
        } else {
          console.log("User successfully deleted from Firebase Auth")
          authSuccess = true
        }
      } catch (authError) {
        console.error("Error calling delete-user API:", authError)

        // Store error details
        setAuthDeleteError(authError instanceof Error ? authError.message : "Unknown error")

        // Show a warning toast
        toast({
          id: "auth-delete-error",
          title: "Avertissement",
          description:
            "L'utilisateur a été supprimé de la base de données mais une erreur est survenue lors de la suppression de l'authentification",
          variant: "warning",
          action: (
            <Button variant="outline" size="sm" onClick={() => setErrorDialogOpen(true)}>
              Détails
            </Button>
          ),
        })
      }

      // Remove the user from local state
      setUsers((prev) => prev.filter((user) => user.id !== id))

      // Show a success toast
      toast({
        title: "Succès",
        description: authSuccess
          ? "L'utilisateur a été complètement supprimé avec succès"
          : "L'utilisateur a été supprimé de la base de données",
        variant: "default",
      })
    } catch (error) {
      console.error("Error during user deletion:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la suppression de l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setDialogOpen(false)
    }
  }

  const toggleAdmin = async (id: string, currentState: boolean) => {
    try {
      await updateDoc(doc(db(), "users", id), {
        isAdmin: !currentState,
        roles: !currentState
          ? [...(users.find((u) => u.id === id)?.roles || []), "admin"]
          : (users.find((u) => u.id === id)?.roles || []).filter((r) => r !== "admin"),
      })

      setUsers((prev) =>
        prev.map((user) =>
          user.id === id
            ? {
                ...user,
                isAdmin: !currentState,
                roles: !currentState
                  ? [...(user.roles || []), "admin"]
                  : (user.roles || []).filter((r) => r !== "admin"),
              }
            : user,
        ),
      )

      toast({
        title: "Succès",
        description: `Les droits d'administrateur ont été ${!currentState ? "accordés" : "retirés"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification des droits d'administrateur:", error)
      toast({
        title: "Erreur",
        description: "Impossible de modifier les droits d'administrateur",
        variant: "destructive",
      })
    }
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "Jamais"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getGroupNames = (groupIds: string[]) => {
    if (!groupIds || groupIds.length === 0) return "Aucun groupe"

    return groupIds
      .map((id) => {
        const group = groups.find((g) => g.id === id)
        return group ? group.name : id
      })
      .join(", ")
  }

  const getRoleNames = (roleIds: string[]) => {
    if (!roleIds || roleIds.length === 0) return "Aucun rôle"

    return roleIds
      .map((id) => {
        const role = availableRoles.find((r) => r.id === id)
        return role ? role.name : id
      })
      .join(", ")
  }

  const toggleActiveStatus = async (id: string, currentStatus: boolean) => {
    try {
      await updateDoc(doc(db(), "users", id), {
        isActive: !currentStatus,
      })

      setUsers((prev) =>
        prev.map((user) =>
          user.id === id
            ? {
                ...user,
                isActive: !currentStatus,
              }
            : user,
        ),
      )

      toast({
        title: "Succès",
        description: `Le statut de l'utilisateur a été changé à ${!currentStatus ? "Actif" : "Inactif"}`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification du statut de l'utilisateur:", error)
      toast({
        title: "Erreur",
        description: "Impossible de modifier le statut de l'utilisateur",
        variant: "destructive",
      })
    }
  }

  // Remplacez la fonction checkDataBeforeDelete par celle-ci
  const prepareUserDeletion = async (userId: string) => {
    try {
      // Définir l'utilisateur sélectionné
      setSelectedUserId(userId)

      // Vérifier les données associées
      const dataInfo = await checkUserAssociatedData(userId)
      setUserDataInfo({
        userId,
        count: dataInfo.totalAssociatedItems,
      })

      // Ouvrir la boîte de dialogue
      setDialogOpen(true)
    } catch (error) {
      console.error("Erreur lors de la vérification des données associées:", error)
      // En cas d'erreur, on continue quand même
      setUserDataInfo({ userId, count: 0 })
      setDialogOpen(true)
    }
  }

  return (
    <PermissionGate permissions={[PERMISSIONS.READ_USERS, PERMISSIONS.ADMIN]} anyPermission={true}>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Gestion des utilisateurs</h1>
          <PermissionGate permissions={[PERMISSIONS.CREATE_USERS, PERMISSIONS.ADMIN]} anyPermission={true}>
            <Button onClick={() => router.push("/admin/users/create")}>
              <Plus className="mr-2 h-4 w-4" />
              Nouvel utilisateur
            </Button>
          </PermissionGate>
        </div>

        <Card className="mb-6">
          <CardContent className="pt-6">
            <Input
              placeholder="Rechercher un utilisateur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </CardContent>
        </Card>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Rôles spécifiques</TableHead>
                      <TableHead>Groupes</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Dernière connexion</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                          Aucun utilisateur trouvé
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              {user.photoURL && (
                                <img
                                  src={user.photoURL || "/placeholder.svg"}
                                  alt={user.displayName}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              )}
                              {user.displayName || "Sans nom"}
                            </div>
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge
                              className={cn(
                                "font-medium",
                                user.isActive
                                  ? "bg-green-600 hover:bg-green-700"
                                  : user.isPending
                                    ? "bg-amber-500 hover:bg-amber-600 text-white"
                                    : "bg-red-500 hover:bg-red-600",
                              )}
                            >
                              {user.isActive ? "Actif" : user.isPending ? "En attente" : "Inactif"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="line-clamp-1" title={getRoleNames(user.roles || [])}>
                              {getRoleNames(user.roles || [])}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="line-clamp-1" title={getGroupNames(user.groups)}>
                              {getGroupNames(user.groups)}
                            </span>
                          </TableCell>
                          <TableCell>
                            {user.registrationSource === "pre-registered" ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
                                Pré-enregistré
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                                Manuel
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>{formatDate(user.lastLogin)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <PermissionGate
                                permissions={[PERMISSIONS.UPDATE_USERS, PERMISSIONS.ADMIN]}
                                anyPermission={true}
                              >
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => toggleActiveStatus(user.id, user.isActive === true)}
                                  title={user.isActive ? "Désactiver l'utilisateur" : "Activer l'utilisateur"}
                                >
                                  {user.isActive ? (
                                    <EyeOff className="h-4 w-4 text-amber-500" />
                                  ) : (
                                    <Eye className="h-4 w-4 text-green-500" />
                                  )}
                                </Button>

                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => toggleAdmin(user.id, user.isAdmin)}
                                  title={user.isAdmin ? "Retirer les droits admin" : "Donner les droits admin"}
                                >
                                  {user.isAdmin ? <ShieldOff className="h-4 w-4" /> : <Shield className="h-4 w-4" />}
                                </Button>

                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => router.push(`/admin/users/edit/${user.id}`)}
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                              </PermissionGate>
                              <PermissionGate
                                permissions={[PERMISSIONS.DELETE_USERS, PERMISSIONS.ADMIN]}
                                anyPermission={true}
                              >
                                <Button variant="outline" size="icon" onClick={() => prepareUserDeletion(user.id)}>
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>

                                <AlertDialog
                                  open={dialogOpen && selectedUserId === user.id}
                                  onOpenChange={setDialogOpen}
                                >
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                                      <AlertDialogDescription asChild>
                                        <div className="space-y-2">
                                          {userDataInfo && userDataInfo.userId === user.id && (
                                            <UserDeletionWarning dataCount={userDataInfo.count} />
                                          )}
                                          <div>
                                            Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est
                                            irréversible.
                                          </div>
                                          <div>Les données suivantes seront également supprimées :</div>
                                          <ul className="list-disc pl-5 text-sm">
                                            <li>Profil utilisateur</li>
                                            <li>Favoris</li>
                                            <li>Commentaires</li>
                                            <li>Activités</li>
                                            <li>Profil commercial (si applicable)</li>
                                          </ul>
                                        </div>
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel onClick={() => setSelectedUserId(null)}>
                                        Annuler
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => {
                                          handleDelete(user.id)
                                          setSelectedUserId(null)
                                        }}
                                        className="bg-red-500 hover:bg-red-600"
                                        disabled={isDeleting}
                                      >
                                        {isDeleting ? (
                                          <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Suppression...
                                          </>
                                        ) : (
                                          "Supprimer"
                                        )}
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </PermissionGate>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Details Dialog */}
        <AlertDialog open={errorDialogOpen} onOpenChange={setErrorDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500" />
                Détails de l'erreur
              </AlertDialogTitle>
              <AlertDialogDescription className="space-y-4">
                <p>
                  L'utilisateur a été supprimé de la base de données, mais la suppression de son compte
                  d'authentification a échoué.
                </p>

                <div className="rounded-md bg-muted p-4 text-sm font-mono overflow-auto max-h-[200px]">
                  {authDeleteError || "Erreur inconnue"}
                </div>

                <p className="text-sm">
                  <strong>Note:</strong> Cette erreur est généralement causée par des problèmes de configuration ou de
                  permissions dans Firebase. L'utilisateur ne pourra plus se connecter à l'application, mais son compte
                  d'authentification reste présent dans Firebase.
                </p>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction>Fermer</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </PermissionGate>
  )
}
