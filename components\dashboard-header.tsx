"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { useAuth } from "@/components/auth-provider"
import { MobileNav } from "@/components/mobile-nav"
import { Settings, User, LogOut } from "lucide-react"
import Image from "next/image"
import { useState } from "react"
import { FallbackLogo } from "./fallback-logo"

export function DashboardHeader() {
  const pathname = usePathname()
  const { user, isAdmin, logout } = useAuth()
  const [logoError, setLogoError] = useState(false)

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background">
      <div className="container flex h-16 items-center justify-between py-4">
        <div className="flex items-center gap-2">
          <MobileNav />
          <Link href="/dashboard" className="flex items-center gap-2">
            {!logoError ? (
              <Image
                src="/logo-acr-direct.png"
                alt="ACR Direct"
                width={120}
                height={40}
                className="h-8 w-auto"
                onError={() => setLogoError(true)}
                priority
              />
            ) : (
              <FallbackLogo className="h-8 w-auto" />
            )}
          </Link>
        </div>
        <div className="flex items-center gap-4">
          <div className="hidden md:flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/dashboard/profile">
                <User className="h-5 w-5" />
                <span className="sr-only">Mon profil</span>
              </Link>
            </Button>
            <Button variant="ghost" size="icon" onClick={logout}>
              <LogOut className="h-5 w-5" />
              <span className="sr-only">Déconnexion</span>
            </Button>
          </div>
          <ThemeToggle />
          {isAdmin && (
            <Button variant="ghost" size="icon" asChild>
              <Link href="/admin">
                <Settings className="h-5 w-5" />
                <span className="sr-only">Administration</span>
              </Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}

export default DashboardHeader
