// Simple performance monitoring utility

// Store performance marks
const marks: Record<string, number> = {}

// Start timing a specific operation
export function startMeasure(name: string): void {
  if (typeof performance !== "undefined") {
    marks[name] = performance.now()
  }
}

// End timing and return the duration in milliseconds
export function endMeasure(name: string): number | null {
  if (typeof performance !== "undefined" && marks[name]) {
    const duration = performance.now() - marks[name]
    delete marks[name]
    return duration
  }
  return null
}

// Report a performance metric to the console in development
export function reportPerformance(name: string, duration: number): void {
  if (process.env.NODE_ENV === "development") {
    console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
  }

  // In production, we could send this to an analytics service
  if (process.env.NODE_ENV === "production") {
    // Example: send to analytics
    // analytics.logEvent('performance_measure', { name, duration })
  }
}

// Measure Web Vitals
export function measureWebVitals(): void {
  if (typeof window !== "undefined" && "performance" in window) {
    // First Contentful Paint
    const paintEntries = performance.getEntriesByType("paint")
    const fcp = paintEntries.find((entry) => entry.name === "first-contentful-paint")

    if (fcp) {
      reportPerformance("FCP", fcp.startTime)
    }

    // Largest Contentful Paint
    let lcpReported = false

    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      const lastEntry = entries[entries.length - 1]

      if (lastEntry && !lcpReported) {
        reportPerformance("LCP", lastEntry.startTime)
        lcpReported = true
      }
    }).observe({ type: "largest-contentful-paint", buffered: true })

    // First Input Delay
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      entries.forEach((entry) => {
        reportPerformance("FID", entry.processingStart - entry.startTime)
      })
    }).observe({ type: "first-input", buffered: true })

    // Cumulative Layout Shift
    let clsValue = 0
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += (entry as any).value
        }
      }
      reportPerformance("CLS", clsValue)
    }).observe({ type: "layout-shift", buffered: true })
  }
}
