"use client"

import type React from "react"

import { useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { ImagePlus, X, Loader2, MoveUp, MoveDown } from "lucide-react"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"

export interface CarouselSlide {
  id: string
  url: string
  file?: File
  title: string
  description: string
}

interface CarouselModelProps {
  slides: CarouselSlide[]
  onSlidesChange: (slides: CarouselSlide[]) => void
  autoplay: boolean
  onAutoplayChange: (autoplay: boolean) => void
  interval: number
  onIntervalChange: (interval: number) => void
  isUploading?: boolean
}

export function CarouselModel({
  slides,
  onSlidesChange,
  autoplay,
  onAutoplayChange,
  interval,
  onIntervalChange,
  isUploading = false,
}: CarouselModelProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newSlides: CarouselSlide[] = [...slides]

      Array.from(e.target.files).forEach((file) => {
        // Create a temporary URL for preview
        const url = URL.createObjectURL(file)
        newSlides.push({
          id: `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          url,
          file,
          title: "",
          description: "",
        })
      })

      onSlidesChange(newSlides)

      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleRemoveSlide = (index: number) => {
    const newSlides = [...slides]
    newSlides.splice(index, 1)
    onSlidesChange(newSlides)
  }

  const handleTitleChange = (index: number, title: string) => {
    const newSlides = [...slides]
    newSlides[index].title = title
    onSlidesChange(newSlides)
  }

  const handleDescriptionChange = (index: number, description: string) => {
    const newSlides = [...slides]
    newSlides[index].description = description
    onSlidesChange(newSlides)
  }

  const moveSlide = (fromIndex: number, toIndex: number) => {
    const newSlides = [...slides]
    const [movedItem] = newSlides.splice(fromIndex, 1)
    newSlides.splice(toIndex, 0, movedItem)
    onSlidesChange(newSlides)
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const sourceIndex = result.source.index
    const destinationIndex = result.destination.index

    moveSlide(sourceIndex, destinationIndex)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="autoplay">Défilement automatique</Label>
          <Switch id="autoplay" checked={autoplay} onCheckedChange={onAutoplayChange} />
        </div>

        {autoplay && (
          <div className="space-y-2">
            <Label htmlFor="interval">Intervalle entre les diapositives (secondes)</Label>
            <Input
              id="interval"
              type="number"
              min="1"
              max="20"
              value={interval}
              onChange={(e) => onIntervalChange(Number.parseInt(e.target.value) || 5)}
            />
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label>Diapositives du carousel</Label>
        <div className="flex flex-col gap-4">
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="carousel-slides">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                  {slides.map((slide, index) => (
                    <Draggable key={slide.id} draggableId={slide.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="flex flex-col gap-4 p-4 border rounded-lg bg-card"
                        >
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">Diapositive {index + 1}</h4>
                            <div className="flex gap-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => moveSlide(index, Math.max(0, index - 1))}
                                disabled={index === 0}
                                className="h-8 w-8 p-0"
                              >
                                <MoveUp className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => moveSlide(index, Math.min(slides.length - 1, index + 1))}
                                disabled={index === slides.length - 1}
                                className="h-8 w-8 p-0"
                              >
                                <MoveDown className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                variant="destructive"
                                className="h-8 w-8 p-0"
                                onClick={() => handleRemoveSlide(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="relative aspect-video w-full overflow-hidden rounded-md">
                            <img
                              src={slide.url || "/placeholder.svg"}
                              alt={slide.title || "Diapositive"}
                              className="object-cover w-full h-full"
                            />
                          </div>

                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label htmlFor={`slide-title-${index}`}>Titre</Label>
                              <Input
                                id={`slide-title-${index}`}
                                value={slide.title}
                                onChange={(e) => handleTitleChange(index, e.target.value)}
                                placeholder="Titre de la diapositive"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`slide-description-${index}`}>Description</Label>
                              <Input
                                id={`slide-description-${index}`}
                                value={slide.description}
                                onChange={(e) => handleDescriptionChange(index, e.target.value)}
                                placeholder="Description de la diapositive"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="w-full py-8"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Téléchargement...
              </>
            ) : (
              <>
                <ImagePlus className="mr-2 h-4 w-4" />
                Ajouter des diapositives
              </>
            )}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      </div>
    </div>
  )
}
