"use client"

import type { ReactNode } from "react"
import { useAuth } from "./auth-provider"
import type { Permission } from "@/lib/permissions"

interface PermissionGateProps {
  children: ReactNode
  permissions?: Permission[]
  anyPermission?: boolean
  fallback?: ReactNode
}

export function PermissionGate({
  children,
  permissions = [],
  anyPermission = false,
  fallback = null,
}: PermissionGateProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = useAuth()

  // If no permissions are specified, render children
  if (permissions.length === 0) {
    return <>{children}</>
  }

  // Check if user has required permissions
  const hasAccess = anyPermission ? hasAnyPermission(permissions) : hasAllPermissions(permissions)

  // Render children if user has access, otherwise render fallback
  return hasAccess ? <>{children}</> : <>{fallback}</>
}
