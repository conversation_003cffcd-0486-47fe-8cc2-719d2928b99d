"use client"

import { useEffect, useState } from "react"
import { collection, query, where, getDocs } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/components/auth-provider"
import { getUserGroups } from "@/lib/user-utils"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"

// Ajouter ces imports en haut du fichier
import { ContentModelRenderer } from "@/components/content-models/content-model-renderer"
import type { ContentModelType } from "@/components/content-models/content-type-selector"

interface PageProps {
  params: {
    slug: string
  }
}

// Modifier l'interface PageData pour inclure les nouveaux champs
interface PageData {
  id: string
  title: string
  content: string
  iconUrl?: string
  targetGroups: string[]
  showFrame?: boolean
  contentType?: ContentModelType
  modelData?: any
}

export default function DynamicPage({ params }: PageProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [pageData, setPageData] = useState<PageData | null>(null)
  const [userGroups, setUserGroups] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [authorized, setAuthorized] = useState(false)

  useEffect(() => {
    const fetchUserGroups = async () => {
      if (!user) return

      try {
        const groups = await getUserGroups(user.uid)
        setUserGroups(groups)
      } catch (error) {
        console.error("Error fetching user groups:", error)
      }
    }

    fetchUserGroups()
  }, [user])

  useEffect(() => {
    const fetchPageData = async () => {
      if (!params.slug || !userGroups.length) return

      try {
        // Query by slug instead of ID
        const pagesQuery = query(collection(db(), "menuItems"), where("slug", "==", params.slug))
        const querySnapshot = await getDocs(pagesQuery)

        if (!querySnapshot.empty) {
          const pageDoc = querySnapshot.docs[0]
          const data = pageDoc.data() as Omit<PageData, "id">

          // Check if user is authorized to view this page
          const isAuthorized =
            data.targetGroups.includes("all") || data.targetGroups.some((group) => userGroups.includes(group))

          if (isAuthorized) {
            setPageData({
              id: pageDoc.id,
              ...data,
            })
            setAuthorized(true)
          } else {
            setAuthorized(false)
          }
        } else {
          // Page doesn't exist
          setAuthorized(false)
        }
      } catch (error) {
        console.error("Error fetching page data:", error)
      } finally {
        setLoading(false)
      }
    }

    if (userGroups.length > 0) {
      fetchPageData()
    }
  }, [params.slug, userGroups])

  // Redirect if not authorized
  useEffect(() => {
    if (!loading && !authorized && user) {
      router.push("/dashboard")
    }
  }, [loading, authorized, user, router])

  // Fonction pour traiter le contenu et s'assurer que les blocs HTML bruts et les styles d'images sont correctement interprétés
  const processContent = (content: string) => {
    // Rechercher les blocs de HTML brut et les remplacer par leur contenu
    let processedContent = content.replace(
      /<div data-type="raw-html"[^>]*data-html-content="([^"]*)"[^>]*>.*?<\/div>/g,
      (match, htmlContent) => {
        // Décoder les entités HTML qui pourraient être échappées
        const decodedContent = htmlContent
          .replace(/&lt;/g, "<")
          .replace(/&gt;/g, ">")
          .replace(/&quot;/g, '"')
          .replace(/&amp;/g, "&")
        return decodedContent
      },
    )

    // S'assurer que les sauts de ligne sont préservés
    processedContent = processedContent.replace(
      /<br class="my-custom-break">/g,
      '<br class="my-custom-break" style="display: block; height: 1em;">',
    )

    return processedContent
  }

  // Effet pour ajouter des styles CSS pour les images responsives
  useEffect(() => {
    // Ajouter un style pour les images responsives
    const style = document.createElement("style")
    style.innerHTML = `
      .responsive-image {
        max-width: 100%;
        height: auto;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  return (
    <div className="space-y-6 px-4 sm:px-6 md:px-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">
          {loading ? <Skeleton className="h-8 w-48" /> : pageData?.title}
        </h2>
      </div>

      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      ) : pageData ? (
        pageData.showFrame ? (
          <Card className="dark:border-gray-800">
            <CardContent className="p-4 sm:p-6">
              {pageData.contentType && pageData.contentType !== "richtext" ? (
                <ContentModelRenderer
                  contentType={pageData.contentType}
                  content={pageData.modelData}
                  richTextContent={pageData.content}
                />
              ) : (
                <div
                  className="prose max-w-none prose-sm sm:prose dark:prose-invert"
                  dangerouslySetInnerHTML={{
                    __html: pageData.content ? processContent(pageData.content) : "",
                  }}
                />
              )}
            </CardContent>
          </Card>
        ) : pageData.contentType && pageData.contentType !== "richtext" ? (
          <ContentModelRenderer
            contentType={pageData.contentType}
            content={pageData.modelData}
            richTextContent={pageData.content}
          />
        ) : (
          <div
            className="prose max-w-none prose-sm sm:prose dark:prose-invert"
            dangerouslySetInnerHTML={{
              __html: pageData.content ? processContent(pageData.content) : "",
            }}
          />
        )
      ) : null}
    </div>
  )
}
