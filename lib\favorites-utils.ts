import { doc, getDoc, setDoc, arrayUnion, arrayRemove, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

// Structure pour stocker les favoris dans Firestore
interface UserFavorites {
  newsIds: string[]
}

// Add this constant at the top of the file
const FAVORITES_STATUS_CACHE_KEY = "user_has_favorites_"

// Récupérer les favoris d'un utilisateur
export async function getUserFavorites(userId: string): Promise<string[]> {
  try {
    // Utiliser db() comme une fonction au lieu d'une instance directe
    const userFavoritesRef = doc(db(), "userFavorites", userId)
    const userFavoritesDoc = await getDoc(userFavoritesRef)

    if (userFavoritesDoc.exists()) {
      const data = userFavoritesDoc.data() as UserFavorites
      return data.newsIds || []
    } else {
      // Créer un document vide si l'utilisateur n'a pas encore de favoris
      await setDoc(userFavoritesRef, { newsIds: [] })
      return []
    }
  } catch (error) {
    console.error("Error getting user favorites:", error)
    return []
  }
}

// Ajouter un article aux favoris
export async function addToFavorites(userId: string, newsId: string): Promise<boolean> {
  try {
    // Utiliser db() comme une fonction
    const userFavoritesRef = doc(db(), "userFavorites", userId)

    // Vérifier si le document existe déjà
    const docSnap = await getDoc(userFavoritesRef)

    if (docSnap.exists()) {
      // Mettre à jour le document existant
      await updateDoc(userFavoritesRef, {
        newsIds: arrayUnion(newsId),
      })
    } else {
      // Créer un nouveau document
      await setDoc(userFavoritesRef, {
        newsIds: [newsId],
      })
    }

    // Update the cache to indicate user has favorites
    localStorage.setItem(`${FAVORITES_STATUS_CACHE_KEY}${userId}`, "true")
    localStorage.setItem(`${FAVORITES_STATUS_CACHE_KEY}${userId}_timestamp`, Date.now().toString())

    return true
  } catch (error) {
    console.error("Error adding to favorites:", error)
    return false
  }
}

// Supprimer un article des favoris
export async function removeFromFavorites(userId: string, newsId: string): Promise<boolean> {
  try {
    // Utiliser db() comme une fonction
    const userFavoritesRef = doc(db(), "userFavorites", userId)

    await updateDoc(userFavoritesRef, {
      newsIds: arrayRemove(newsId),
    })

    // Check if the user still has favorites after removal
    const updatedDoc = await getDoc(userFavoritesRef)
    const data = updatedDoc.data() as UserFavorites
    const stillHasFavorites = data.newsIds && data.newsIds.length > 0

    // Update the cache
    localStorage.setItem(`${FAVORITES_STATUS_CACHE_KEY}${userId}`, stillHasFavorites.toString())
    localStorage.setItem(`${FAVORITES_STATUS_CACHE_KEY}${userId}_timestamp`, Date.now().toString())

    return true
  } catch (error) {
    console.error("Error removing from favorites:", error)
    return false
  }
}

// Vérifier si un article est dans les favoris
export async function isInFavorites(userId: string, newsId: string): Promise<boolean> {
  try {
    const favorites = await getUserFavorites(userId)
    return favorites.includes(newsId)
  } catch (error) {
    console.error("Error checking favorites:", error)
    return false
  }
}

// Update the toggleFavorite function to ensure cache is updated
export async function toggleFavorite(userId: string, newsId: string): Promise<boolean> {
  try {
    const isFavorite = await isInFavorites(userId, newsId)

    if (isFavorite) {
      await removeFromFavorites(userId, newsId)
      return false
    } else {
      await addToFavorites(userId, newsId)
      return true
    }
  } catch (error) {
    console.error("Error toggling favorite:", error)
    return false
  }
}
