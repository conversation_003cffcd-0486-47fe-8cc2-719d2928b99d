import { getAuth, signOut } from "firebase/auth"

// Configuration
const AUTH_VERSION = "v3"
const STORAGE_KEYS = {
  USER: `auth_user_${AUTH_VERSION}`,
  TOKEN: `auth_session_${AUTH_VERSION}`,
  TIMESTAMP: `auth_timestamp_${AUTH_VERSION}`,
  UID: "auth_user_uid",
  LAST_ACTIVE: "auth_last_active",
  LAST_TOKEN: "auth_last_token",
  REFRESH_TOKEN: "auth_refresh_token",
  USER_DATA: "auth_user_data",
  SESSION_STATE: "auth_session_state",
}

/**
 * Tente de récupérer la session en cas de désynchronisation entre client et serveur
 * Cette fonction utilise plusieurs stratégies pour récupérer la session:
 * 1. Utilise le token côté client pour créer une session côté serveur
 * 2. Utilise le service worker d'authentification si disponible
 * 3. Tente de restaurer la session depuis le stockage local
 *
 * @returns Promise<boolean> - true si la récupération a réussi, false sinon
 */
export async function recoverSession(): Promise<boolean> {
  try {
    console.log("[Session Recovery] Tentative de récupération de session...")

    // Approche simplifiée : si l'utilisateur est connecté côté client, créer une session côté serveur
    const auth = getAuth()

    // Vérifier si l'utilisateur est connecté côté client
    if (!auth.currentUser) {
      console.log("[Session Recovery] Aucun utilisateur connecté côté client, impossible de récupérer la session")

      // Vérifier si une session existe dans le stockage local
      try {
        // Vérifier d'abord si le service worker d'authentification peut restaurer la session
        if (typeof window !== "undefined" && window.attemptSessionRestore) {
          console.log("[Session Recovery] Tentative de restauration de session via le service worker...")
          const result = await window.attemptSessionRestore()

          if (result) {
            console.log("[Session Recovery] Session restaurée via le service worker, rechargement nécessaire")
            // Retourner true pour indiquer qu'une action a été prise, même si la session n'est pas encore complètement récupérée
            // Le composant SessionRecoveryEnhancer se chargera de recharger la page
            return true
          }
        }

        // Vérifier le localStorage
        const storedUser = localStorage.getItem(STORAGE_KEYS.USER) || localStorage.getItem("auth_user_v2")
        const storedToken = localStorage.getItem(STORAGE_KEYS.LAST_TOKEN) || localStorage.getItem("auth_last_token")
        const storedUserData = localStorage.getItem(STORAGE_KEYS.USER_DATA)

        if (storedUser || storedToken || storedUserData) {
          console.log(
            "[Session Recovery] Données d'authentification trouvées dans le localStorage, mais utilisateur non connecté",
          )

          // Vérifier si les données sont récentes (moins de 24h)
          const lastActive = localStorage.getItem(STORAGE_KEYS.LAST_ACTIVE)
          const now = Date.now()
          let isRecent = false

          if (lastActive) {
            const lastActiveTime = Number.parseInt(lastActive, 10)
            const timeDiff = now - lastActiveTime
            isRecent = timeDiff < 24 * 60 * 60 * 1000 // 24 heures
          }

          if (isRecent) {
            console.log("[Session Recovery] Données d'authentification récentes trouvées, tentative de restauration...")

            // Tenter de restaurer la session via le service worker si disponible
            if (typeof window !== "undefined" && window.cacheAuthData && storedToken) {
              try {
                await window.cacheAuthData({
                  token: storedToken,
                  uid: localStorage.getItem(STORAGE_KEYS.UID) || "",
                  timestamp: now,
                })
                console.log("[Session Recovery] Données d'authentification mises en cache dans le service worker")
              } catch (cacheError) {
                console.error(
                  "[Session Recovery] Erreur lors de la mise en cache des données d'authentification:",
                  cacheError,
                )
              }
            }

            // Retourner true pour indiquer qu'une action a été prise
            return true
          } else {
            console.log("[Session Recovery] Nettoyage des données d'authentification obsolètes")

            // Nettoyer les données d'authentification obsolètes
            // Anciennes clés (v2)
            localStorage.removeItem("auth_user_uid")
            localStorage.removeItem("auth_last_token")
            localStorage.removeItem("auth_last_active")
            localStorage.removeItem("auth_user_v2")
            localStorage.removeItem("auth_session_v2")
            localStorage.removeItem("auth_timestamp_v2")

            // Nouvelles clés (v3)
            Object.values(STORAGE_KEYS).forEach((key) => {
              localStorage.removeItem(key)
            })
          }
        }
      } catch (localStorageError) {
        console.error("[Session Recovery] Erreur lors de la vérification du stockage local:", localStorageError)
      }

      return false
    }

    // Afficher des informations sur l'utilisateur
    console.log(
      `[Session Recovery] Utilisateur côté client: ${auth.currentUser.uid}, email: ${auth.currentUser.email || "non défini"}`,
    )

    // Obtenir un token ID (sans forcer le rafraîchissement pour plus de rapidité)
    console.log("[Session Recovery] Obtention d'un token ID...")
    let idToken
    try {
      // Utiliser false pour éviter de forcer le rafraîchissement et accélérer le processus
      idToken = await auth.currentUser.getIdToken(false)
      console.log(`[Session Recovery] Token ID obtenu: ${idToken.substring(0, 10)}...`)
    } catch (tokenError) {
      console.error("[Session Recovery] Erreur lors de l'obtention du token:", tokenError)
      return false
    }

    // Forcer la persistance maximale si disponible
    if (typeof window !== "undefined" && window.forceMaximumPersistence) {
      try {
        console.log("[Session Recovery] Forçage de la persistance maximale...")
        await window.forceMaximumPersistence()
      } catch (persistenceError) {
        console.error("[Session Recovery] Erreur lors du forçage de la persistance maximale:", persistenceError)
      }
    }

    // Créer une session côté serveur avec sessionLogin
    console.log("[Session Recovery] Création d'une session côté serveur via sessionLogin...")
    try {
      // Utiliser XMLHttpRequest pour un meilleur contrôle sur les cookies
      const createSessionPromise = new Promise<boolean>((resolve, reject) => {
        const xhr = new XMLHttpRequest()
        xhr.open("POST", "/api/auth/sessionLogin", true)
        xhr.withCredentials = true // Important pour les cookies
        xhr.setRequestHeader("Content-Type", "application/json")
        xhr.timeout = 10000 // 10 secondes de timeout

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            console.log(`[Session Recovery] Session créée avec succès (${xhr.status})`)
            console.log(`[Session Recovery] Réponse: ${xhr.responseText}`)
            resolve(true)
          } else {
            console.error(`[Session Recovery] Échec de la création de session: ${xhr.status}`)
            console.error(`[Session Recovery] Réponse d'erreur: ${xhr.responseText}`)
            resolve(false)
          }
        }

        xhr.onerror = () => {
          console.error("[Session Recovery] Erreur réseau lors de la création de session")
          reject(new Error("Erreur réseau"))
        }

        xhr.ontimeout = () => {
          console.error("[Session Recovery] Timeout lors de la création de session")
          reject(new Error("Timeout"))
        }

        xhr.send(JSON.stringify({ idToken }))
      })

      const sessionCreated = await createSessionPromise
      if (!sessionCreated) {
        console.error("[Session Recovery] Échec de la création de session via sessionLogin")

        // Mettre en cache les données d'authentification dans le service worker si disponible
        if (typeof window !== "undefined" && window.cacheAuthData) {
          try {
            console.log("[Session Recovery] Mise en cache des données d'authentification dans le service worker...")
            await window.cacheAuthData({
              token: idToken,
              uid: auth.currentUser.uid,
              timestamp: Date.now(),
            })
            console.log("[Session Recovery] Données d'authentification mises en cache dans le service worker")
          } catch (cacheError) {
            console.error(
              "[Session Recovery] Erreur lors de la mise en cache des données d'authentification:",
              cacheError,
            )
          }
        }

        return false
      }

      // Attendre très peu pour que le cookie soit enregistré (optimisé pour la rapidité)
      console.log("[Session Recovery] Attente minimale pour l'enregistrement du cookie...")
      await new Promise((resolve) => setTimeout(resolve, 100))

      // Vérifier que la session a bien été créée
      console.log("[Session Recovery] Vérification de la session...")
      const verifyPromise = new Promise<boolean>((resolve, reject) => {
        const xhr = new XMLHttpRequest()
        xhr.open("GET", "/api/auth/check-session", true)
        xhr.withCredentials = true // Important pour les cookies

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText)
              console.log(`[Session Recovery] Réponse de vérification: ${xhr.responseText}`)
              resolve(response.authenticated === true)
            } catch (e) {
              console.error("[Session Recovery] Erreur de parsing de la réponse:", e)
              resolve(false)
            }
          } else {
            console.error(`[Session Recovery] Erreur lors de la vérification: ${xhr.status}`)
            resolve(false)
          }
        }

        xhr.onerror = () => {
          console.error("[Session Recovery] Erreur réseau lors de la vérification")
          reject(new Error("Erreur réseau"))
        }

        xhr.send()
      })

      const isAuthenticated = await verifyPromise
      if (isAuthenticated) {
        console.log("[Session Recovery] Session récupérée avec succès")
        return true
      } else {
        console.error("[Session Recovery] La session n'a pas été correctement récupérée")

        // Dernière tentative avec refresh-session
        console.log("[Session Recovery] Dernière tentative avec refresh-session...")
        const refreshPromise = new Promise<boolean>((resolve, reject) => {
          const xhr = new XMLHttpRequest()
          xhr.open("POST", "/api/auth/refresh-session", true)
          xhr.withCredentials = true
          xhr.setRequestHeader("Content-Type", "application/json")

          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              console.log(`[Session Recovery] Rafraîchissement réussi: ${xhr.responseText}`)
              resolve(true)
            } else {
              console.error(`[Session Recovery] Échec du rafraîchissement: ${xhr.status}`)
              resolve(false)
            }
          }

          xhr.onerror = () => {
            console.error("[Session Recovery] Erreur réseau lors du rafraîchissement")
            reject(new Error("Erreur réseau"))
          }

          xhr.send(JSON.stringify({ idToken }))
        })

        const refreshed = await refreshPromise
        if (!refreshed) {
          console.error("[Session Recovery] Échec de la récupération de session après toutes les tentatives")
          return false
        }

        // Vérifier à nouveau
        console.log("[Session Recovery] Vérification finale après refresh-session...")
        const finalCheckPromise = new Promise<boolean>((resolve, reject) => {
          const xhr = new XMLHttpRequest()
          xhr.open("GET", "/api/auth/check-session", true)
          xhr.withCredentials = true

          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText)
                console.log(`[Session Recovery] Réponse finale: ${xhr.responseText}`)
                resolve(response.authenticated === true)
              } catch (e) {
                console.error("[Session Recovery] Erreur de parsing de la réponse finale:", e)
                resolve(false)
              }
            } else {
              console.error(`[Session Recovery] Erreur lors de la vérification finale: ${xhr.status}`)
              resolve(false)
            }
          }

          xhr.onerror = () => {
            console.error("[Session Recovery] Erreur réseau lors de la vérification finale")
            reject(new Error("Erreur réseau"))
          }

          xhr.send()
        })

        const finalResult = await finalCheckPromise
        console.log(`[Session Recovery] Résultat final: ${finalResult ? "Succès" : "Échec"}`)
        return finalResult
      }
    } catch (error) {
      console.error("[Session Recovery] Exception lors de la récupération de session:", error)
      return false
    }
  } catch (error) {
    console.error("[Session Recovery] Exception globale lors de la récupération de session:", error)
    return false
  }
}
/**
 * Force une déconnexion complète (client et serveur) puis recharge la page
 * Cette fonction nettoie toutes les données d'authentification:
 * - Déconnexion de Firebase Authentication
 * - Suppression des cookies de session
 * - Nettoyage du localStorage et sessionStorage
 * - Nettoyage du cache du service worker
 *
 * @param skipReload - Si true, ne recharge pas la page (utile pour les tests)
 */
export async function forceLogoutAndReload(skipReload = false): Promise<void> {
  try {
    const auth = getAuth()

    // Nettoyer le localStorage
    try {
      // Anciennes clés (v2)
      localStorage.removeItem("auth_user_uid")
      localStorage.removeItem("auth_last_token")
      localStorage.removeItem("auth_last_active")
      localStorage.removeItem("auth_user_v2")
      localStorage.removeItem("auth_session_v2")
      localStorage.removeItem("auth_timestamp_v2")

      // Nouvelles clés (v3)
      Object.values(STORAGE_KEYS).forEach((key) => {
        localStorage.removeItem(key)
      })

      // Nettoyer également sessionStorage
      try {
        Object.values(STORAGE_KEYS).forEach((key) => {
          sessionStorage.removeItem(key)
        })
      } catch (sessionStorageError) {
        console.error("[Session Recovery] Erreur lors du nettoyage du sessionStorage:", sessionStorageError)
      }

      console.log("[Session Recovery] Données d'authentification supprimées du stockage local")
    } catch (localStorageError) {
      console.error("[Session Recovery] Erreur lors du nettoyage du localStorage:", localStorageError)
    }

    // Informer le service worker si disponible
    try {
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        // Utiliser le nouveau type de message pour le service worker v3
        navigator.serviceWorker.controller.postMessage({
          type: "CLEAR_AUTH_CACHE",
          version: AUTH_VERSION,
          timestamp: Date.now(),
        })
        console.log("[Session Recovery] Demande de nettoyage du cache d'authentification envoyée au service worker")
      }
    } catch (swError) {
      console.error("[Session Recovery] Erreur lors de la communication avec le service worker:", swError)
    }

    // Nettoyer IndexedDB si disponible
    try {
      if (typeof window !== "undefined" && window.indexedDB) {
        // Supprimer la base de données d'authentification
        const deleteRequest = window.indexedDB.deleteDatabase("acrDirectAuth")

        deleteRequest.onsuccess = () => {
          console.log("[Session Recovery] Base de données IndexedDB d'authentification supprimée avec succès")
        }

        deleteRequest.onerror = () => {
          console.error(
            "[Session Recovery] Erreur lors de la suppression de la base de données IndexedDB d'authentification",
          )
        }
      }
    } catch (indexedDBError) {
      console.error("[Session Recovery] Erreur lors du nettoyage d'IndexedDB:", indexedDBError)
    }

    // Déconnexion côté serveur
    try {
      console.log("[Session Recovery] Déconnexion côté serveur...")
      const logoutPromise = new Promise<boolean>((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open("POST", "/api/auth/sessionLogout", true)
        xhr.withCredentials = true

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            console.log("[Session Recovery] Déconnexion côté serveur réussie")
            resolve(true)
          } else {
            console.error(`[Session Recovery] Échec de la déconnexion côté serveur: ${xhr.status}`)
            resolve(false)
          }
        }

        xhr.onerror = () => {
          console.error("[Session Recovery] Erreur réseau lors de la déconnexion côté serveur")
          resolve(false)
        }

        xhr.send()
      })

      await logoutPromise
    } catch (serverLogoutError) {
      console.error("[Session Recovery] Exception lors de la déconnexion côté serveur:", serverLogoutError)
    }

    // Déconnexion côté client
    try {
      if (auth.currentUser) {
        console.log("[Session Recovery] Déconnexion côté client...")
        await signOut(auth)
        console.log("[Session Recovery] Déconnexion côté client réussie")
      } else {
        console.log("[Session Recovery] Aucun utilisateur connecté côté client")
      }
    } catch (clientLogoutError) {
      console.error("[Session Recovery] Erreur lors de la déconnexion côté client:", clientLogoutError)
    }

    if (skipReload) {
      console.log("[Session Recovery] Déconnexion forcée effectuée, rechargement de la page ignoré (mode test)")
    } else {
      console.log("[Session Recovery] Déconnexion forcée effectuée, rechargement de la page...")

      // Recharger la page
      setTimeout(() => {
        window.location.reload()
      }, 500) // Petit délai pour permettre aux opérations asynchrones de se terminer
    }
  } catch (error) {
    console.error("[Session Recovery] Erreur lors de la déconnexion forcée:", error)
    // Recharger quand même en cas d'erreur, sauf si skipReload est true
    if (!skipReload) {
      window.location.reload()
    }
  }
}

// Import getStoredAuthUser from auth-persistence.ts
import { getStoredAuthUser } from "./auth-persistence"

/**
 * Tente de récupérer une session après une erreur de service Firebase
 * @returns true si la récupération a réussi, false sinon
 */
export async function recoverFromServiceUnavailable(): Promise<boolean> {
  try {
    // Vérifier si nous avons des données utilisateur stockées localement
    const userInfo = await getStoredAuthUser()
    if (!userInfo) {
      console.log("Pas de données utilisateur pour la récupération")
      return false
    }

    console.log("Tentative de récupération de session pour", userInfo.uid)

    // Vérifier si l'utilisateur est déjà authentifié
    if (auth().currentUser && auth().currentUser.uid === userInfo.uid) {
      console.log("L'utilisateur est déjà authentifié")
      return true
    }

    // Attendre un court instant et vérifier à nouveau
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Vérifier si Firebase a récupéré automatiquement
    if (auth().currentUser && auth().currentUser.uid === userInfo.uid) {
      console.log("Session récupérée automatiquement après délai")
      return true
    }

    // Si nous arrivons ici, la récupération automatique a échoué
    console.log("Échec de la récupération automatique, utilisation du mode dégradé")

    // Mettre en place un indicateur de mode dégradé
    localStorage.setItem("firebase_degraded_mode", "true")

    return false
  } catch (error) {
    console.error("Erreur lors de la tentative de récupération:", error)
    return false
  }
}

// Exporter les fonctions nécessaires
// export { getStoredAuthUser } from './auth-persistence' // Removed duplicate export
import { auth } from "./firebase"
