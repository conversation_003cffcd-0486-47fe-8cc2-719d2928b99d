"use client"

import { useEffect } from "react"
import { enhanceAuthPersistence, attemptSessionRestore } from "@/lib/auth-persistence-enhancer"

export function AuthPersistenceEnhancer() {
  useEffect(() => {
    // Tenter de restaurer la session au chargement
    attemptSessionRestore()
      .then((user) => {
        if (user) {
          console.log("Session utilisateur restaurée:", user.uid)
        } else {
          console.log("Aucune session à restaurer")
        }
      })
      .catch((error) => {
        console.error("Erreur lors de la tentative de restauration de session:", error)
      })

    // Activer l'amélioration de la persistance
    const cleanup = enhanceAuthPersistence()

    return () => {
      if (cleanup) cleanup()
    }
  }, [])

  // Ce composant ne rend rien visuellement
  return null
}
