"use client"

import { useEffect, useState } from "react"
import { initOfflineSync } from "@/lib/offline-sync"

export function OfflineSyncInitializer() {
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    const initialize = async () => {
      try {
        // Attendre un peu pour s'assurer que tout est chargé
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const result = await initOfflineSync()
        setInitialized(result)

        if (result) {
          console.log("Système de synchronisation hors ligne initialisé avec succès")
        } else {
          console.error("Échec de l'initialisation du système de synchronisation hors ligne")
        }
      } catch (error) {
        console.error("Erreur lors de l'initialisation du système de synchronisation hors ligne:", error)
      }
    }

    initialize()
  }, [])

  // Ce composant ne rend rien visuellement
  return null
}
