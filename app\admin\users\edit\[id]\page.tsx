"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { db } from "@/lib/firebase"
import { doc, getDoc, updateDoc, serverTimestamp, setDoc } from "firebase/firestore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { useGroups } from "@/lib/hooks"
import { type Role, PREDEFINED_ROLES } from "@/lib/roles"
import { Checkbox } from "@/components/ui/checkbox"
import { PermissionGate } from "@/components/permission-gate"
import { PERMISSIONS } from "@/lib/permissions"
import { DEPARTMENTS } from "@/lib/commercial-types"
import { <PERSON>, <PERSON><PERSON>ontent, Select<PERSON><PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"

interface UserEditProps {
  params: {
    id: string
  }
}

export default function EditUserPage({ params }: UserEditProps) {
  const id = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [email, setEmail] = useState("")
  const [displayName, setDisplayName] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [isAdmin, setIsAdmin] = useState(false)
  const [isActive, setIsActive] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [availableRoles, setAvailableRoles] = useState<Role[]>([])
  const { groups, loading: loadingGroups } = useGroups()

  const [department, setDepartment] = useState("")
  const [phone, setPhone] = useState("")
  const [jobTitle, setJobTitle] = useState("")

  useEffect(() => {
    fetchUserData()
    fetchRoles()
  }, [id])

  const fetchRoles = async () => {
    try {
      // Get predefined roles
      const predefinedRoles = Object.values(PREDEFINED_ROLES)

      try {
        // Get custom roles from Firestore
        const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
        let customRoles: Role[] = []

        if (rolesDoc.exists()) {
          customRoles = rolesDoc.data().roles || []
        } else {
          // If document doesn't exist, initialize it
          await setDoc(doc(db(), "settings", "roles"), { roles: [] }, { merge: true })
        }

        // Combine predefined and custom roles
        setAvailableRoles([...predefinedRoles, ...customRoles])
      } catch (error) {
        console.error("Error fetching custom roles:", error)
        // Fall back to predefined roles
        setAvailableRoles(predefinedRoles)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des rôles:", error)
      // Ensure we at least have predefined roles
      setAvailableRoles(Object.values(PREDEFINED_ROLES))
    }
  }

  const fetchUserData = async () => {
    try {
      const docRef = doc(db(), "users", id)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        setEmail(data.email || "")
        setDisplayName(data.displayName || "")
        setFirstName(data.firstName || "")
        setLastName(data.lastName || "")
        setIsAdmin(data.isAdmin || false)
        setSelectedGroups(data.groups || [])
        setSelectedRoles(data.roles || [])
        setIsActive(data.isActive !== false) // Default to true if not explicitly false
        setDepartment(data.department || "")
        setPhone(data.phone || "")
        setJobTitle(data.jobTitle || "")

        // If user is admin but doesn't have admin role, add it
        if (data.isAdmin && data.roles && !data.roles.includes("admin")) {
          setSelectedRoles([...data.roles, "admin"])
        }
      } else {
        toast({
          title: "Erreur",
          description: "Utilisateur introuvable",
          variant: "destructive",
        })
        router.push("/admin/users")
      }
    } catch (error) {
      console.error("Erreur lors du chargement de l'utilisateur:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setIsLoadingData(false)
    }
  }

  useEffect(() => {
    setDisplayName(`${firstName} ${lastName}`.trim())
  }, [firstName, lastName])

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups((prev) => (prev.includes(groupId) ? prev.filter((id) => id !== groupId) : [...prev, groupId]))
  }

  const handleRoleToggle = (roleId: string) => {
    // If toggling admin role, also update isAdmin state
    if (roleId === "admin") {
      setIsAdmin(!selectedRoles.includes("admin"))
    }

    setSelectedRoles((prev) => (prev.includes(roleId) ? prev.filter((id) => id !== roleId) : [...prev, roleId]))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !firstName || !lastName) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Prepare user data
      const userData = {
        email,
        firstName,
        lastName,
        displayName: `${firstName} ${lastName}`.trim(),
        isAdmin,
        isActive, // Include isActive status
        groups: selectedGroups,
        roles: selectedRoles,
        updatedAt: serverTimestamp(),
        department: department,
        phone: phone,
        jobTitle: jobTitle,
      }

      // Update document in Firestore
      const docRef = doc(db(), "users", id)
      await updateDoc(docRef, userData)

      toast({
        title: "Succès",
        description: "L'utilisateur a été mis à jour avec succès",
      })

      router.push("/admin/users")
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'utilisateur:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <PermissionGate permissions={[PERMISSIONS.UPDATE_USERS, PERMISSIONS.ADMIN]} anyPermission={true}>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Modifier l'utilisateur</h1>

        <form onSubmit={handleSubmit}>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Informations de l'utilisateur</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <Label htmlFor="isActive">Statut du compte</Label>
                <div className="flex items-center gap-2">
                  <Label htmlFor="isActive" className={isActive ? "text-green-600" : "text-red-500"}>
                    {isActive ? "Actif" : "Inactif"}
                  </Label>
                  <Switch id="isActive" checked={isActive} onCheckedChange={setIsActive} />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">Prénom *</Label>
                  <Input id="firstName" value={firstName} onChange={(e) => setFirstName(e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Nom *</Label>
                  <Input id="lastName" value={lastName} onChange={(e) => setLastName(e.target.value)} required />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled
                />
                <p className="text-sm text-muted-foreground">L'adresse email ne peut pas être modifiée</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Numéro de téléphone</Label>
                <Input id="phone" value={phone} onChange={(e) => setPhone(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobTitle">Fonction</Label>
                <Input id="jobTitle" value={jobTitle} onChange={(e) => setJobTitle(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Département</Label>
                <Select value={department} onValueChange={setDepartment}>
                  <SelectTrigger id="department">
                    <SelectValue placeholder="Sélectionner un département" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept.code} value={dept.code}>
                        {dept.name} ({dept.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Rôles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <Label htmlFor="isAdmin">Administrateur</Label>
                <Switch
                  id="isAdmin"
                  checked={isAdmin}
                  onCheckedChange={(checked) => {
                    setIsAdmin(checked)
                    if (checked && !selectedRoles.includes("admin")) {
                      setSelectedRoles((prev) => [...prev, "admin"])
                    } else if (!checked && selectedRoles.includes("admin")) {
                      setSelectedRoles((prev) => prev.filter((role) => role !== "admin"))
                    }
                  }}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4">
                {availableRoles.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`role-${role.id}`}
                      checked={selectedRoles.includes(role.id)}
                      onCheckedChange={() => handleRoleToggle(role.id)}
                      disabled={role.id === "admin" && isAdmin} // Disable admin role checkbox if isAdmin is true
                    />
                    <div>
                      <Label htmlFor={`role-${role.id}`} className="font-medium">
                        {role.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">{role.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Groupes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loadingGroups ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : (
                <div className="space-y-2">
                  {groups.length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      Aucun groupe disponible. Veuillez d'abord créer des groupes.
                    </p>
                  ) : (
                    groups.map((group) => (
                      <div key={group.id} className="flex items-center space-x-2">
                        <Switch
                          id={`group-${group.id}`}
                          checked={selectedGroups.includes(group.id)}
                          onCheckedChange={() => handleGroupToggle(group.id)}
                        />
                        <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.push("/admin/users")} disabled={isLoading}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Enregistrer les modifications
            </Button>
          </div>
        </form>
      </div>
    </PermissionGate>
  )
}
