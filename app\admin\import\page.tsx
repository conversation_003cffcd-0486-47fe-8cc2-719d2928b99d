"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { db } from "@/lib/firebase"
import { collection, addDoc, getDocs, query, where, deleteDoc, doc } from "firebase/firestore"
import Papa from "papaparse"
import { normalizeClientCode, normalizeSiret } from "@/lib/user-matching-utils"
import { AlertCircle, CheckCircle2, FileSpreadsheet, Loader2, Trash2 } from "lucide-react"

// Définition du schéma CSV attendu
const REQUIRED_HEADERS = ["N° client", "Type de client", "5 derniers SIRET", "Téléphone", "Fonction"]
const OPTIONAL_HEADERS = ["Groupes supplémentaires", "Réseau", "Département", "Nom"]

export default function ImportPage() {
  const [file, setFile] = useState<File | null>(null)
  const [importing, setImporting] = useState(false)
  const [importedUsers, setImportedUsers] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  // Charger les utilisateurs importés au chargement de la page
  useEffect(() => {
    loadImportedUsers()
  }, [])

  // Fonction pour charger les utilisateurs importés depuis Firestore
  async function loadImportedUsers() {
    setLoading(true)
    try {
      // Correction: db -> db()
      const importedUsersRef = collection(db(), "importedUsers")
      const importedUsersSnapshot = await getDocs(importedUsersRef)
      const users = importedUsersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }))
      setImportedUsers(users)
      setError(null)
    } catch (err) {
      console.error("Erreur lors du chargement des utilisateurs importés:", err)
      setError("Erreur lors du chargement des utilisateurs importés")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour supprimer un utilisateur importé
  async function deleteImportedUser(id: string) {
    try {
      // Correction: db -> db()
      await deleteDoc(doc(db(), "importedUsers", id))
      setSuccess("Utilisateur supprimé avec succès")
      loadImportedUsers()
    } catch (err) {
      console.error("Erreur lors de la suppression de l'utilisateur:", err)
      setError("Erreur lors de la suppression de l'utilisateur")
    }
  }

  // Fonction pour gérer le changement de fichier
  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0])
      setError(null)
      setSuccess(null)
    }
  }

  // Fonction pour importer le fichier CSV
  function handleImport() {
    if (!file) {
      setError("Veuillez sélectionner un fichier CSV")
      return
    }

    setImporting(true)
    setError(null)
    setSuccess(null)

    // Lire le fichier CSV
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const csv = e.target?.result
        if (typeof csv !== "string") {
          throw new Error("Format de fichier invalide")
        }

        // Parser le CSV avec Papa.parse
        Papa.parse(csv, {
          header: true,
          skipEmptyLines: true,
          complete: async (results) => {
            try {
              // Vérifier les en-têtes requis
              const headers = results.meta.fields || []
              const missingHeaders = REQUIRED_HEADERS.filter((h) => !headers.includes(h))

              if (missingHeaders.length > 0) {
                setError(`En-têtes manquants: ${missingHeaders.join(", ")}`)
                setImporting(false)
                return
              }

              // Traiter les données
              const data = results.data as Record<string, string>[]
              const importedCount = await processImportData(data)

              setSuccess(`${importedCount} utilisateurs importés avec succès`)
              loadImportedUsers()
            } catch (err) {
              console.error("Erreur lors du traitement des données:", err)
              setError("Erreur lors du traitement des données CSV")
            } finally {
              setImporting(false)
            }
          },
          error: (err) => {
            console.error("Erreur lors du parsing CSV:", err)
            setError(`Erreur lors du parsing CSV: ${err.message}`)
            setImporting(false)
          },
        })
      } catch (err) {
        console.error("Erreur lors de la lecture du fichier:", err)
        setError("Erreur lors de la lecture du fichier")
        setImporting(false)
      }
    }

    reader.readAsText(file)
  }

  // Fonction pour traiter les données importées
  async function processImportData(data: Record<string, string>[]) {
    let importedCount = 0

    // Référence à la collection importedUsers
    // Correction: db -> db()
    const importedUsersRef = collection(db(), "importedUsers")

    for (const row of data) {
      try {
        // Extraire et normaliser les données
        const clientCode = normalizeClientCode(row["N° client"])
        const clientType = row["Type de client"]?.trim() || "default"
        const siretLastDigits = normalizeSiret(row["5 derniers SIRET"])

        // Vérifier les données obligatoires
        if (!clientCode || !siretLastDigits) {
          console.warn("Ligne ignorée - données manquantes:", row)
          continue
        }

        // Extraire les groupes supplémentaires
        const additionalGroups =
          row["Groupes supplémentaires"]
            ?.split(",")
            .map((g) => g.trim())
            .filter(Boolean) || []

        // Préparer les données à enregistrer
        const userData = {
          clientCode,
          clientType,
          siretLastDigits,
          additionalGroups,
          network: row["Réseau"]?.trim() || null,
          department: row["Département"]?.trim() || null,
          name: row["Nom"]?.trim() || null,
          importedAt: new Date().toISOString(),
          phone: row["Téléphone"]?.trim() || "",
          jobTitle: row["Fonction"]?.trim() || "",
        }

        // Vérifier si l'utilisateur existe déjà
        const existingQuery = query(
          importedUsersRef,
          where("clientCode", "==", clientCode),
          where("siretLastDigits", "==", siretLastDigits),
        )
        const existingDocs = await getDocs(existingQuery)

        if (existingDocs.empty) {
          // Ajouter le nouvel utilisateur
          await addDoc(importedUsersRef, userData)
          importedCount++
        } else {
          // Mettre à jour l'utilisateur existant
          const docId = existingDocs.docs[0].id
          // Correction: db -> db()
          await deleteDoc(doc(db(), "importedUsers", docId))
          await addDoc(importedUsersRef, userData)
          importedCount++
        }
      } catch (err) {
        console.error("Erreur lors du traitement d'une ligne:", err, row)
      }
    }

    return importedCount
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Importation d'utilisateurs pré-enregistrés</h1>

      <Card>
        <CardHeader>
          <CardTitle>Importer un fichier CSV</CardTitle>
          <CardDescription>
            Importez un fichier CSV contenant les informations des utilisateurs pré-enregistrés. Le fichier doit
            contenir les colonnes suivantes: {REQUIRED_HEADERS.join(", ")}. Colonnes optionnelles:{" "}
            {OPTIONAL_HEADERS.join(", ")}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="csv-file">Fichier CSV</Label>
            <Input id="csv-file" type="file" accept=".csv" onChange={handleFileChange} />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleImport} disabled={!file || importing}>
            {importing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Importation en cours...
              </>
            ) : (
              <>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Importer
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="default" className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Succès</AlertTitle>
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Utilisateurs pré-enregistrés</CardTitle>
          <CardDescription>Liste des utilisateurs pré-enregistrés dans le système.</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : importedUsers.length === 0 ? (
            <p className="text-center py-4 text-muted-foreground">Aucun utilisateur pré-enregistré</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="py-2 px-4 text-left">N° client</th>
                    <th className="py-2 px-4 text-left">5 derniers SIRET</th>
                    <th className="py-2 px-4 text-left">Type de client</th>
                    <th className="py-2 px-4 text-left">Groupes</th>
                    <th className="py-2 px-4 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {importedUsers.map((user) => (
                    <tr key={user.id} className="border-b hover:bg-muted/50">
                      <td className="py-2 px-4">{user.clientCode}</td>
                      <td className="py-2 px-4">{user.siretLastDigits}</td>
                      <td className="py-2 px-4">{user.clientType}</td>
                      <td className="py-2 px-4">
                        {Array.isArray(user.additionalGroups)
                          ? user.additionalGroups.join(", ")
                          : user.additionalGroups || "-"}
                      </td>
                      <td className="py-2 px-4">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => deleteImportedUser(user.id)}
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
