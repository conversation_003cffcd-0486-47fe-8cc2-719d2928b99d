"use client"

import { useState, useEffect } from "react"
import { db } from "@/lib/firebase"
import { collection, query, where, getDocs, doc, updateDoc } from "firebase/firestore"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Check, X, Search } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface PendingUser {
  id: string
  displayName: string
  email: string
  clientCode: string
  siretLastDigits: string
  phone: string
  jobTitle: string
  createdAt: any
}

export default function PendingUsersPage() {
  const [users, setUsers] = useState<PendingUser[]>([])
  const [filteredUsers, setFilteredUsers] = useState<PendingUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [processingUsers, setProcessingUsers] = useState<Record<string, boolean>>({})
  const [availableGroups, setAvailableGroups] = useState<{ id: string; name: string }[]>([])
  const [selectedGroups, setSelectedGroups] = useState<Record<string, string[]>>({})
  const { toast } = useToast()

  useEffect(() => {
    fetchPendingUsers()
    fetchGroups()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredUsers(users)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredUsers(
        users.filter(
          (user) =>
            user.displayName?.toLowerCase().includes(lowercasedSearch) ||
            user.email?.toLowerCase().includes(lowercasedSearch) ||
            user.clientCode?.toLowerCase().includes(lowercasedSearch),
        ),
      )
    }
  }, [searchTerm, users])

  const fetchGroups = async () => {
    try {
      const groupsQuery = query(collection(db(), "groups"))
      const querySnapshot = await getDocs(groupsQuery)

      const groups: { id: string; name: string }[] = []
      querySnapshot.forEach((doc) => {
        groups.push({
          id: doc.id,
          name: doc.data().name,
        })
      })

      setAvailableGroups(groups)
    } catch (error) {
      console.error("Erreur lors du chargement des groupes:", error)
    }
  }

  const fetchPendingUsers = async () => {
    try {
      setIsLoading(true)
      // Modifier la requête pour ne récupérer que les utilisateurs en attente
      // (ceux qui ont isConfirmed=false ET isPending=true)
      const usersQuery = query(
        collection(db(), "users"),
        where("isConfirmed", "==", false),
        where("isPending", "==", true),
      )
      const querySnapshot = await getDocs(usersQuery)

      const pendingUsers: PendingUser[] = []
      const groupSelections: Record<string, string[]> = {}

      querySnapshot.forEach((doc) => {
        const userData = doc.data()
        pendingUsers.push({
          id: doc.id,
          displayName: userData.displayName || "",
          email: userData.email || "",
          clientCode: userData.clientCode || "",
          siretLastDigits: userData.siretLastDigits || "",
          phone: userData.phone || "",
          jobTitle: userData.jobTitle || "",
          createdAt: userData.createdAt,
        })

        // Initialiser les groupes sélectionnés pour cet utilisateur
        groupSelections[doc.id] = userData.groups || []
      })

      setUsers(pendingUsers)
      setFilteredUsers(pendingUsers)
      setSelectedGroups(groupSelections)
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs en attente:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les utilisateurs en attente",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleApproveUser = async (userId: string) => {
    setProcessingUsers((prev) => ({ ...prev, [userId]: true }))

    try {
      const userFavoritesRef = doc(db(), "users", userId)
      await updateDoc(userFavoritesRef, {
        isConfirmed: true,
        isActive: true, // Ajouter ce champ
        isPending: false, // Ajouter ce champ
        groups: selectedGroups[userId] || [],
        confirmedAt: new Date(),
      })

      toast({
        title: "Utilisateur approuvé",
        description: "L'utilisateur a été approuvé avec succès",
      })

      // Mettre à jour la liste des utilisateurs
      setUsers((prev) => prev.filter((user) => user.id !== userId))
    } catch (error) {
      console.error("Erreur lors de l'approbation de l'utilisateur:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'approuver l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setProcessingUsers((prev) => ({ ...prev, [userId]: false }))
    }
  }

  const handleRejectUser = async (userId: string) => {
    setProcessingUsers((prev) => ({ ...prev, [userId]: true }))

    try {
      // Option 2: Marquer comme rejeté
      const userFavoritesRef = doc(db(), "users", userId)
      await updateDoc(userFavoritesRef, {
        isRejected: true,
        isActive: false, // Ajouter ce champ
        isPending: false, // Mettre à jour ce champ
        rejectedAt: new Date(),
      })

      toast({
        title: "Utilisateur rejeté",
        description: "L'utilisateur a été rejeté avec succès",
      })

      // Mettre à jour la liste des utilisateurs
      setUsers((prev) => prev.filter((user) => user.id !== userId))
    } catch (error) {
      console.error("Erreur lors du rejet de l'utilisateur:", error)
      toast({
        title: "Erreur",
        description: "Impossible de rejeter l'utilisateur",
        variant: "destructive",
      })
    } finally {
      setProcessingUsers((prev) => ({ ...prev, [userId]: false }))
    }
  }

  const handleGroupChange = (userId: string, groupId: string) => {
    setSelectedGroups((prev) => {
      const currentGroups = prev[userId] || []

      // Si le groupe est déjà sélectionné, le retirer
      if (currentGroups.includes(groupId)) {
        return {
          ...prev,
          [userId]: currentGroups.filter((id) => id !== groupId),
        }
      }

      // Sinon, l'ajouter
      return {
        ...prev,
        [userId]: [...currentGroups, groupId],
      }
    })
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Utilisateurs en attente de validation</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Validation des inscriptions</CardTitle>
          <CardDescription>Approuvez ou rejetez les demandes d'inscription des utilisateurs</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un utilisateur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucun utilisateur en attente de validation</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Utilisateur</TableHead>
                  <TableHead>Code client</TableHead>
                  <TableHead>SIRET (5 derniers)</TableHead>
                  <TableHead>Fonction</TableHead>
                  <TableHead>Date d'inscription</TableHead>
                  <TableHead>Groupes</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{user.displayName}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                        <p className="text-xs text-muted-foreground">{user.phone}</p>
                      </div>
                    </TableCell>
                    <TableCell>{user.clientCode}</TableCell>
                    <TableCell>{user.siretLastDigits}</TableCell>
                    <TableCell>{user.jobTitle}</TableCell>
                    <TableCell>{formatDate(user.createdAt)}</TableCell>
                    <TableCell>
                      <Select
                        value={selectedGroups[user.id]?.join(",")}
                        onValueChange={(value) => {
                          setSelectedGroups((prev) => ({
                            ...prev,
                            [user.id]: value ? value.split(",") : [],
                          }))
                        }}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Sélectionner des groupes" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableGroups.map((group) => (
                            <SelectItem key={group.id} value={group.id}>
                              {group.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <div className="mt-2 flex flex-wrap gap-1">
                        {selectedGroups[user.id]?.map((groupId) => {
                          const group = availableGroups.find((g) => g.id === groupId)
                          return group ? (
                            <Badge key={groupId} variant="outline" className="text-xs">
                              {group.name}
                            </Badge>
                          ) : null
                        })}
                      </div>
                      <Badge className="bg-amber-500 hover:bg-amber-600 text-white">En attente</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApproveUser(user.id)}
                          disabled={processingUsers[user.id]}
                          className="bg-green-50 text-green-700 hover:bg-green-100 hover:text-green-800"
                        >
                          {processingUsers[user.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Check className="h-4 w-4 mr-1" />
                          )}
                          Approuver
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRejectUser(user.id)}
                          disabled={processingUsers[user.id]}
                          className="bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800"
                        >
                          {processingUsers[user.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <X className="h-4 w-4 mr-1" />
                          )}
                          Rejeter
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
