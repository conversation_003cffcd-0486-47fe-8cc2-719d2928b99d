"use client"

import { useEffect, useState } from "react"
import { measureWebVitals } from "@/lib/performance"

export function PerformanceMonitor() {
  const [isActive, setIsActive] = useState(false)

  useEffect(() => {
    // Only activate in development or if explicitly enabled
    const shouldActivate =
      process.env.NODE_ENV === "development" || localStorage.getItem("enablePerfMonitoring") === "true"

    if (shouldActivate) {
      setIsActive(true)
      measureWebVitals()
    }

    // Listen for keyboard shortcut to toggle monitoring
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+P to toggle performance monitoring
      if (e.ctrlKey && e.shiftKey && e.key === "P") {
        e.preventDefault()
        const newState = !isActive
        setIsActive(newState)
        localStorage.setItem("enablePerfMonitoring", newState.toString())

        if (newState) {
          measureWebVitals()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [isActive])

  // This component doesn't render anything visible
  return null
}
