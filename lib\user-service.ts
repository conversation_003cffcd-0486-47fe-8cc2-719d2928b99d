import { doc, getDoc, updateDoc, collection, query, where, getDocs } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { hasAnyPermission } from "@/lib/permissions"
import type { Permission } from "@/lib/permissions"

/**
 * Service pour gérer les utilisateurs
 */
export const UserService = {
  /**
   * Met à jour les rôles d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param roles Nouveaux rôles
   * @param adminId ID de l'administrateur qui effectue la mise à jour
   */
  async updateUserRoles(userId: string, roles: string[], adminId: string): Promise<boolean> {
    try {
      const userRef = doc(db(), "users", userId)

      await updateDoc(userRef, {
        roles,
        updatedAt: new Date().toISOString(),
        updatedBy: adminId,
        lastPermissionUpdate: Date.now(), // Ajouter le timestamp de mise à jour des permissions
      })

      return true
    } catch (error) {
      console.error(`Failed to update roles for user ${userId}:`, error)
      throw error
    }
  },

  /**
   * Met à jour les groupes d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param groups Nouveaux groupes
   * @param adminId ID de l'administrateur qui effectue la mise à jour
   */
  async updateUserGroups(userId: string, groups: string[], adminId: string): Promise<boolean> {
    try {
      const userRef = doc(db(), "users", userId)

      await updateDoc(userRef, {
        groups,
        updatedAt: new Date().toISOString(),
        updatedBy: adminId,
        lastPermissionUpdate: Date.now(), // Ajouter le timestamp de mise à jour des permissions
      })

      return true
    } catch (error) {
      console.error(`Failed to update groups for user ${userId}:`, error)
      throw error
    }
  },

  /**
   * Récupère les groupes d'un utilisateur
   * @param userId ID de l'utilisateur
   */
  async getUserGroups(userId: string): Promise<string[]> {
    try {
      const userDoc = await getDoc(doc(db(), "users", userId))

      if (userDoc.exists()) {
        return userDoc.data().groups || []
      }

      return []
    } catch (error) {
      console.error(`Failed to get groups for user ${userId}:`, error)
      throw error
    }
  },

  // Ajouter cette fonction à la fin du fichier UserService

  /**
   * Met à jour le profil d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param profileData Données du profil à mettre à jour
   */
  async updateUserProfile(
    userId: string,
    profileData: Partial<{
      displayName: string
      phoneNumber: string
      company: string
      position: string
      address: string
      city: string
      postalCode: string
    }>,
  ): Promise<boolean> {
    try {
      const userRef = doc(db(), "users", userId)

      await updateDoc(userRef, {
        ...profileData,
        updatedAt: new Date().toISOString(),
      })

      return true
    } catch (error) {
      console.error(`Failed to update profile for user ${userId}:`, error)
      throw error
    }
  },
}

// Ajouter cette fonction pour récupérer les utilisateurs par rôle

export async function getUsersByRole(roleName: string): Promise<any[]> {
  try {
    const usersRef = collection(db(), "users")
    const q = query(usersRef, where("roles", "array-contains", roleName))
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }))
  } catch (error) {
    console.error("Error getting users by role:", error)
    throw error
  }
}

// Améliorons la fonction getUserById pour inclure des données supplémentaires si nécessaire

export async function getUserById(userId: string) {
  if (!userId) return null

  try {
    const userDoc = await getDoc(doc(db(), "users", userId))
    if (userDoc.exists()) {
      const userData = userDoc.data()

      // Log pour déboguer la structure des données
      console.log("Raw user data from Firestore:", userData)

      return {
        id: userDoc.id,
        ...userData,
      }
    }
    return null
  } catch (error) {
    console.error("Error getting user by ID:", error)
    throw error
  }
}

// Ajouter cette fonction à la fin du fichier

/**
 * Vérifie si un utilisateur a des données associées
 * @param userId ID de l'utilisateur
 * @returns Un objet contenant des informations sur les données associées
 */
export async function checkUserAssociatedData(userId: string) {
  try {
    const dbInstance = db()

    // Vérifier les favoris
    const favoritesQuery = query(collection(dbInstance, "favorites"), where("userId", "==", userId))
    const favoritesSnapshot = await getDocs(favoritesQuery)
    const hasFavorites = !favoritesSnapshot.empty

    // Vérifier les commentaires
    const commentsQuery = query(collection(dbInstance, "comments"), where("authorId", "==", userId))
    const commentsSnapshot = await getDocs(commentsQuery)
    const hasComments = !commentsSnapshot.empty

    // Vérifier le profil commercial
    const commercialQuery = query(collection(dbInstance, "commerciaux"), where("userId", "==", userId))
    const commercialSnapshot = await getDocs(commercialQuery)
    const hasCommercialProfile = !commercialSnapshot.empty

    // Vérifier les activités
    const activitiesQuery = query(collection(dbInstance, "activities"), where("userId", "==", userId))
    const activitiesSnapshot = await getDocs(activitiesQuery)
    const hasActivities = !activitiesSnapshot.empty

    return {
      hasFavorites,
      hasComments,
      hasCommercialProfile,
      hasActivities,
      totalAssociatedItems:
        favoritesSnapshot.size + commentsSnapshot.size + commercialSnapshot.size + activitiesSnapshot.size,
    }
  } catch (error) {
    console.error("Error checking user associated data:", error)
    throw error
  }
}

// Obtenir les groupes d'utilisateurs depuis Firestore
export async function getUserGroups(userId: string): Promise<string[]> {
  try {
    const userDoc = await getDoc(doc(db(), "users", userId))

    if (userDoc.exists()) {
      const userData = userDoc.data()
      const groups = userData.groups || []

      return groups
    } else {
      console.warn(`User document for ${userId} does not exist in Firestore`)
      return []
    }
  } catch (error) {
    console.error("Error getting user groups:", error)
    return []
  }
}

// Obtenir les éléments de menu en fonction des groupes d'utilisateurs
export async function getUserMenuItems(userId: string, userPermissions: Permission[]) {
  try {
    const userGroups = await getUserGroups(userId)

    if (!userGroups.length) return []

    const menuItemsRef = collection(db(), "menuItems")
    const menuItemsSnapshot = await getDocs(menuItemsRef)

    const items: any[] = []

    menuItemsSnapshot.forEach((doc) => {
      const data = doc.data()
      // Vérifier si cet élément de menu est pour l'un des groupes de l'utilisateur ou pour tous les groupes
      const isForUser =
        data.targetGroups.includes("all") || data.targetGroups.some((group: string) => userGroups.includes(group))

      // Vérifier si l'utilisateur a les autorisations requises pour voir la page
      const hasRequiredPermissions = hasAnyPermission(userPermissions, ["read:pages", "admin"])

      if (isForUser && data.isPublished && hasRequiredPermissions) {
        items.push({
          id: doc.id,
          title: data.title,
          path: `/dashboard/pages/${data.slug}`,
          iconUrl: data.iconUrl || null,
          displayOrder: data.displayOrder !== undefined ? data.displayOrder : 0,
        })
      }
    })

    return items
  } catch (error) {
    console.error("Error getting menu items for user:", error)
    return []
  }
}

/**
 * Vérifie si un utilisateur est un administrateur
 * @param userId ID de l'utilisateur
 */
export async function isAdmin(userId: string): Promise<boolean> {
  try {
    const userDoc = await getDoc(doc(db(), "users", userId))
    if (userDoc.exists()) {
      const userData = userDoc.data()
      return userData.isAdmin === true
    }
    return false
  } catch (error) {
    console.error("Error checking admin status:", error)
    return false
  }
}
