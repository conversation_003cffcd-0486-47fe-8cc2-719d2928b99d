"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { VersionDetailsCard } from "@/components/version-details-card"
import { ContentType } from "@/lib/history-utils"
import { diffWords } from "diff"
import { cn } from "@/lib/utils"

interface VersionComparisonProps {
  contentType: ContentType
  contentId: string
  version1Id: string
  version2Id: string
}

interface DiffResult {
  value: string
  added?: boolean
  removed?: boolean
}

export default function VersionComparison({ contentType, contentId, version1Id, version2Id }: VersionComparisonProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [version1, setVersion1] = useState<any>(null)
  const [version2, setVersion2] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [diffs, setDiffs] = useState<DiffResult[]>([])
  const [displayMode, setDisplayMode] = useState<"side-by-side" | "differential">("side-by-side")

  useEffect(() => {
    const fetchVersions = async () => {
      try {
        setIsLoading(true)

        const getVersion = async (versionId: string) => {
          // Use the correct collection based on the contentType
          const baseCollection = contentType === ContentType.NEWS ? "news" : "menuItems"
          const versionDoc = await getDoc(
            doc(db(), baseCollection, contentId, "versions", versionId === "current" ? contentId : versionId),
          )
          if (!versionDoc.exists()) {
            if (versionId === "current") {
              const currentDoc = await getDoc(doc(db(), baseCollection, contentId))
              return currentDoc.exists() ? { data: currentDoc.data(), versionId: "current" } : null
            }
            return null
          }
          return versionDoc.data()
        }

        const [v1, v2] = await Promise.all([getVersion(version1Id), getVersion(version2Id)])

        if (!v1 || !v2) {
          toast({
            title: "Erreur",
            description: "Une ou plusieurs versions sont introuvables",
            variant: "destructive",
          })
          router.push(`/admin/${contentType === ContentType.NEWS ? "news" : "pages"}/history/${contentId}`)
          return
        }

        setVersion1(v1)
        setVersion2(v2)

        // Perform diffing
        const content1 = v1.data.content || ""
        const content2 = v2.data.content || ""
        const diff = diffWords(content1, content2)
        setDiffs(diff)
      } catch (error) {
        console.error("Erreur lors du chargement des versions:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les détails des versions",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchVersions()
  }, [contentType, contentId, version1Id, version2Id, router, toast])

  // Fonction pour traiter le contenu et s'assurer que les blocs HTML bruts et les styles d'images sont correctement interprétés
  const processContent = (content: string) => {
    // Rechercher les blocs de HTML brut et les remplacer par leur contenu
    let processedContent = content.replace(
      /<div data-type="raw-html"[^>]*data-html-content="([^"]*)"[^>]*>.*?<\/div>/g,
      (match, htmlContent) => {
        // Décoder les entités HTML qui pourraient être échappées
        const decodedContent = htmlContent
          .replace(/&lt;/g, "<")
          .replace(/&gt;/g, ">")
          .replace(/&quot;/g, '"')
          .replace(/&amp;/g, "&")
        return decodedContent
      },
    )

    // S'assurer que les sauts de ligne sont préservés
    processedContent = processedContent.replace(
      /<br class="my-custom-break">/g,
      '<br class="my-custom-break" style="display: block; height: 1em;">',
    )

    return processedContent
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-2 mb-6">
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-10 w-64 mb-6" />
        <div className="space-y-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    )
  }

  if (!version1 || !version2) return null

  const getMetadata = (version: any, versionId: string) => {
    if (versionId === "current") {
      return {
        versionId: "current",
        createdAt: version.data.updatedAt || version.data.createdAt,
        createdBy: {
          displayName: "Current",
          uid: "current",
        },
        description: "Current Published Version",
      }
    } else {
      return {
        versionId: version.versionId,
        createdAt: version.createdAt,
        createdBy: version.createdBy,
        description: version.description,
      }
    }
  }

  const metadata1 = getMetadata(version1, version1Id)
  const metadata2 = getMetadata(version2, version2Id)

  const renderSideBySide = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <VersionDetailsCard version={metadata1} title="Version 1">
          <CardContent>
            <div className="prose max-w-none prose-sm sm:prose dark:prose-invert">
              {diffs.map((part, index) => {
                return part.added ? null : (
                  <span
                    key={index}
                    className={part.removed ? "text-red-500" : ""}
                    dangerouslySetInnerHTML={{ __html: part.value }}
                  />
                )
              })}
            </div>
          </CardContent>
        </VersionDetailsCard>
      </div>

      <div>
        <VersionDetailsCard version={metadata2} title="Version 2">
          <CardContent>
            <div className="prose max-w-none prose-sm sm:prose dark:prose-invert">
              {diffs.map((part, index) => {
                return part.removed ? null : (
                  <span
                    key={index}
                    className={part.added ? "text-green-500" : ""}
                    dangerouslySetInnerHTML={{ __html: part.value }}
                  />
                )
              })}
            </div>
          </CardContent>
        </VersionDetailsCard>
      </div>
    </div>
  )

  const renderDifferential = () => (
    <VersionDetailsCard version={metadata2} title="Version Différentielle">
      <CardContent>
        <div className="prose max-w-none prose-sm sm:prose dark:prose-invert">
          {diffs.map((part, index) => (
            <span
              key={index}
              className={cn(part.added ? "text-green-500" : "", part.removed ? "text-red-500" : "")}
              dangerouslySetInnerHTML={{ __html: part.value }}
            />
          ))}
        </div>
      </CardContent>
    </VersionDetailsCard>
  )

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6"></div>
    </div>
  )
}
