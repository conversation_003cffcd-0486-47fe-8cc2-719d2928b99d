"use client"

import { useState, useEffect, createContext, useContext, type ReactNode, useCallback } from "react"
import { auth } from "@/lib/firebase"
import { onAuthStateChanged, type User, signInWithCustomToken } from "firebase/auth"
import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { getRolePermissions } from "@/lib/roles"
import type { Permission } from "@/lib/permissions"
import { getStoredAuthToken, storeAuthUser } from "@/lib/auth-persistence"
import { getDocs, collection } from "firebase/firestore"
// Ajouter l'import pour les composants de persistance et récupération de session
import { AuthPersistenceEnhancer } from "@/components/auth-persistence-enhancer"
import { SessionRecoveryEnhancer } from "@/components/session-recovery-enhancer"

interface UserData {
  id: string
  email: string
  displayName: string | null
  role: string
  groups: string[]
  [key: string]: any
}

interface AuthState {
  user: User | null
  userData: UserData | null
  loading: boolean
  isAdmin: boolean
  menuItems: any[]
  hasPermission: (permission: Permission) => boolean
  hasAnyPermission: (permissions: Permission[]) => boolean
  hasAllPermissions: (permissions: Permission[]) => boolean
}

interface AuthContextProps extends AuthState {
  refreshToken: () => Promise<string | null>
  refreshMenuItems: () => Promise<void>
  logout: () => Promise<void>
  refreshUserData: (user: User) => Promise<void>
}

// Garder uniquement cette déclaration de AuthContext
export const AuthContext = createContext<AuthContextProps | undefined>(undefined)

export function useAuth(): AuthContextProps {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    userData: null,
    loading: true,
    isAdmin: false,
    menuItems: [],
    hasPermission: () => false,
    hasAnyPermission: () => false,
    hasAllPermissions: () => false,
  })

  const [availableRoles, setAvailableRoles] = useState([])

  // Fonction fetchMenuItems
  const fetchMenuItems = useCallback(async (userPermissions: Permission[], userGroups: string[] = []) => {
    try {
      console.log("Fetching menu items with groups:", userGroups)

      // Si l'utilisateur n'a pas de groupes, retourner un menu par défaut
      if (!userGroups || userGroups.length === 0) {
        console.log("No groups found, using default menu")
        setState((prev) => ({
          ...prev,
          menuItems: [
            {
              id: "default-home",
              title: "Accueil",
              path: "/dashboard",
              iconUrl: null,
              displayOrder: 0,
            },
            {
              id: "default-news",
              title: "Actualités",
              path: "/dashboard/news",
              iconUrl: null,
              displayOrder: 1,
            },
          ],
        }))
        return
      }

      const menuItemsSnapshot = await getDocs(collection(db(), "menuItems"))
      const items: any[] = []

      menuItemsSnapshot.forEach((doc) => {
        const data = doc.data()

        // Vérifier si cet élément de menu est pour l'un des groupes de l'utilisateur ou pour tous les groupes
        const isForUser =
          data.targetGroups?.includes("all") ||
          (Array.isArray(data.targetGroups) && data.targetGroups.some((group: string) => userGroups.includes(group)))

        if (data.isPublished && isForUser) {
          items.push({
            id: doc.id,
            title: data.title,
            path: `/dashboard/pages/${data.slug}`,
            iconUrl: data.iconUrl || null,
            displayOrder: data.displayOrder !== undefined ? data.displayOrder : 0,
          })
        }
      })

      // Trier les éléments par ordre d'affichage
      const sortedItems = items.sort((a, b) => {
        const orderA = a.displayOrder !== undefined ? a.displayOrder : 0
        const orderB = b.displayOrder !== undefined ? b.displayOrder : 0
        return orderA - orderB
      })

      console.log(`${sortedItems.length} éléments de menu chargés et triés`)

      setState((prev) => ({
        ...prev,
        menuItems: sortedItems,
      }))
    } catch (error) {
      console.error("Error fetching menu items:", error)
    }
  }, [])

  // Fonction fetchUserData
  const fetchUserData = useCallback(
    async (user: User) => {
      try {
        console.log(`Récupération des données utilisateur pour: ${user.uid}`)

        const userDocRef = doc(db(), "users", user.uid)
        const userDoc = await getDoc(userDocRef)

        if (userDoc.exists()) {
          const userData = {
            id: user.uid,
            email: user.email || "",
            displayName: user.displayName,
            ...userDoc.data(),
          } as UserData

          console.log(
            `Données utilisateur récupérées: ${userData.email}, rôles: ${userData.roles?.join(", ") || "aucun"}, groupes: ${userData.groups?.join(", ") || "aucun"}, département: ${userData.department || "non défini"}`,
          )

          // Stocker les groupes dans le localStorage pour un accès rapide
          if (userData.groups) {
            localStorage.setItem(`user_groups_${user.uid}`, JSON.stringify(userData.groups))
            localStorage.setItem(`user_groups_timestamp_${user.uid}`, Date.now().toString())
            console.log(`Groupes utilisateur mis en cache: ${userData.groups.join(", ")}`)
          }

          const permissions = getRolePermissions(userData.roles || [], availableRoles)
          console.log(`Permissions utilisateur: ${permissions.join(", ")}`)

          setState((prev) => ({
            ...prev,
            userData,
            isAdmin: userData.isAdmin === true,
            hasPermission: (permission: Permission) => permissions.includes(permission) || userData.isAdmin === true,
            hasAnyPermission: (permissions: Permission[]) =>
              permissions.some((permission) => permissions.includes(permission)) || userData.isAdmin === true,
            hasAllPermissions: (permissions: Permission[]) =>
              permissions.every((permission) => permissions.includes(permission)) || userData.isAdmin === true,
          }))

          await fetchMenuItems(permissions, userData.groups || [])
        } else {
          console.error(`Document utilisateur pour ${user.uid} introuvable dans Firestore`)
          setState((prev) => ({
            ...prev,
            error: new Error("User data not found"),
          }))
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des données utilisateur:", error)
        setState((prev) => ({
          ...prev,
          error: error as Error,
        }))
      }
    },
    [availableRoles, fetchMenuItems],
  )

  // Fonction refreshUserData
  const refreshUserData = useCallback(
    async (user: User) => {
      await fetchUserData(user)
    },
    [fetchUserData],
  )

  // Fonction refreshToken
  const refreshToken = useCallback(async (): Promise<string | null> => {
    if (!state.user) return null

    try {
      const token = await getStoredAuthToken()
      return token
    } catch (error) {
      console.error("Error refreshing token:", error)
      return null
    }
  }, [state.user])

  // Fonction refreshMenuItems
  const refreshMenuItems = useCallback(async () => {
    if (state.userData) {
      const permissions = getRolePermissions(state.userData.roles || [], availableRoles)
      await fetchMenuItems(permissions, state.userData.groups || [])
    }
  }, [state.userData, availableRoles, fetchMenuItems])

  // Effet pour charger les rôles disponibles
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const rolesDoc = await getDoc(doc(db(), "settings", "roles"))
        if (rolesDoc.exists()) {
          setAvailableRoles(rolesDoc.data().roles || [])
        }
      } catch (error) {
        console.error("Error fetching roles:", error)
      }
    }

    fetchRoles()
  }, [])

  // Effet pour gérer l'état d'authentification
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth(), async (user) => {
      if (user) {
        console.log(`Utilisateur authentifié: ${user.uid}`)
        setState((prev) => ({ ...prev, user, loading: true, error: null }))
        await storeAuthUser(user)
        await fetchUserData(user)
      } else {
        console.log("Aucun utilisateur authentifié")
        setState({
          user: null,
          userData: null,
          loading: false,
          isAdmin: false,
          menuItems: [],
          hasPermission: () => false,
          hasAnyPermission: () => false,
          hasAllPermissions: () => false,
        })
        await storeAuthUser(null)
      }
      setState((prev) => ({ ...prev, loading: false }))
    })

    return () => unsubscribe()
  }, [fetchUserData])

  // Effet pour essayer de restaurer la session si nécessaire
  useEffect(() => {
    const tryRestoreSession = async () => {
      try {
        // Vérifier si une session est stockée dans localStorage mais que l'utilisateur n'est pas connecté
        const hasLocalSession =
          localStorage.getItem("auth_user") ||
          localStorage.getItem("firebase:authUser:AIzaSyDQYgJTXQKIYOC0LEXuTWaZCYSGYrMBXQs:[DEFAULT]")

        if (hasLocalSession && !state.user) {
          console.log("Session locale trouvée mais utilisateur non connecté, tentative de restauration...")

          // Essayer de récupérer un token personnalisé depuis le serveur
          try {
            const response = await fetch("/api/auth/get-custom-token", {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
              credentials: "same-origin",
              cache: "no-store",
            })

            if (response.ok) {
              const data = await response.json()
              if (data.token) {
                // Utiliser le token pour se connecter à Firebase Auth
                const firebaseAuth = auth()
                await signInWithCustomToken(firebaseAuth, data.token)
                console.log("Session restaurée avec succès via token personnalisé")
              }
            }
          } catch (error) {
            console.error("Erreur lors de la restauration de session:", error)
          }
        }
      } catch (error) {
        console.error("Erreur lors de la tentative de restauration de session:", error)
      }
    }

    tryRestoreSession()
  }, [state.user])

  // Modifier la fonction logout pour qu'elle soit plus robuste
  const logout = useCallback(async () => {
    try {
      // Tenter d'abord la déconnexion côté client pour garantir que l'utilisateur est déconnecté
      // même si l'appel au serveur échoue
      const firebaseAuth = auth()

      // Appeler l'API de déconnexion côté serveur
      try {
        const response = await fetch("/api/auth/sessionLogout", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          // Ajouter un timeout pour éviter que l'appel ne bloque trop longtemps
          signal: AbortSignal.timeout(5000), // 5 secondes de timeout
        })

        if (!response.ok) {
          console.warn("Avertissement: La déconnexion côté serveur a échoué, mais la déconnexion locale continuera")
        }
      } catch (serverError) {
        console.warn("Erreur lors de l'appel à l'API de déconnexion:", serverError)
        // Continuer avec la déconnexion locale même si l'appel au serveur échoue
      }

      // Supprimer les données de session du localStorage
      if (typeof localStorage !== "undefined") {
        localStorage.removeItem("auth_user")
        localStorage.removeItem("auth_token")
        localStorage.removeItem("auth_expiration")
      }

      // Déconnexion de Firebase côté client
      await firebaseAuth.signOut()

      // Rediriger vers la page d'accueil après une déconnexion réussie
      window.location.href = "/"
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error)

      // Tentative de déconnexion de secours en cas d'échec
      try {
        auth().signOut()
        window.location.href = "/"
      } catch (fallbackError) {
        console.error("Échec de la déconnexion de secours:", fallbackError)
        // Forcer une redirection vers la page d'accueil en dernier recours
        window.location.href = "/?force_logout=true"
      }
    }
  }, [])

  const contextValue = {
    ...state,
    refreshToken,
    refreshMenuItems,
    refreshUserData,
    logout,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      <AuthPersistenceEnhancer />
      <SessionRecoveryEnhancer />
      {children}
    </AuthContext.Provider>
  )
}

export function FavoriteButton() {
  return null
}
