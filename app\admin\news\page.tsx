"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db } from "@/lib/firebase"
import { collection, query, orderBy, getDocs, doc, deleteDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Pencil, Trash2, Pin, PinOff, Eye, EyeOff, Save } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
// Ajouter l'import pour le bouton d'historique
import { HistoryButton } from "@/components/history-button"
import { ContentType } from "@/lib/history-types"

interface NewsItem {
  id: string
  title: string
  summary: string
  content: string
  imageUrl?: string
  isPublished: boolean
  isPinned: boolean
  createdAt: any
  updatedAt: any
  targetGroups: string[]
  displayOrder?: number
}

export default function NewsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [news, setNews] = useState<NewsItem[]>([])
  const [filteredNews, setFilteredNews] = useState<NewsItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [orderValues, setOrderValues] = useState<Record<string, number>>({})
  const [isSaving, setIsSaving] = useState(false)
  // Ajout d'un état pour suivre l'élément en cours de suppression
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  useEffect(() => {
    fetchNews()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredNews(news)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredNews(
        news.filter(
          (item) =>
            item.title.toLowerCase().includes(lowercasedSearch) ||
            (item.summary && item.summary.toLowerCase().includes(lowercasedSearch)),
        ),
      )
    }
  }, [searchTerm, news])

  // Initialiser les valeurs d'ordre lorsque les actualités sont chargées
  useEffect(() => {
    const initialOrderValues: Record<string, number> = {}
    news.forEach((item) => {
      initialOrderValues[item.id] = item.displayOrder || 0
    })
    setOrderValues(initialOrderValues)
  }, [news])

  const fetchNews = async () => {
    try {
      setIsLoading(true)
      // Simplifier la requête pour éviter l'erreur d'index composite
      // Utiliser uniquement un tri par date de création
      const newsQuery = query(collection(db(), "news"), orderBy("createdAt", "desc"))
      const querySnapshot = await getDocs(newsQuery)

      const newsItems: NewsItem[] = []
      querySnapshot.forEach((doc) => {
        newsItems.push({
          id: doc.id,
          ...doc.data(),
        } as NewsItem)
      })

      // Effectuer le tri côté client avec JavaScript
      const sortedItems = sortNewsList(newsItems)
      setNews(sortedItems)
      setFilteredNews(sortedItems)
    } catch (error) {
      console.error("Erreur lors du chargement des actualités:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les actualités",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Modification de la fonction handleDelete pour améliorer la gestion des erreurs et l'expérience utilisateur
  const handleDelete = async (id: string) => {
    try {
      // Définir l'état de suppression au début de l'opération
      setIsDeleting(id)

      // Mise à jour optimiste de l'interface utilisateur
      setNews((prevNews) => prevNews.filter((item) => item.id !== id))
      setFilteredNews((prevFilteredNews) => prevFilteredNews.filter((item) => item.id !== id))

      // Effectuer la suppression dans Firestore
      await deleteDoc(doc(db(), "news", id))

      toast({
        title: "Succès",
        description: "L'article a été supprimé avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la suppression de l'article:", error)

      // Restaurer les données en cas d'erreur
      fetchNews()

      // Message d'erreur plus spécifique
      const errorMessage = error instanceof Error ? error.message : "Une erreur inconnue s'est produite"
      toast({
        title: "Erreur de suppression",
        description: `Impossible de supprimer l'article: ${errorMessage}`,
        variant: "destructive",
      })
    } finally {
      // Réinitialiser l'état de suppression
      setIsDeleting(null)
    }
  }

  const togglePublished = async (id: string, currentState: boolean) => {
    try {
      await updateDoc(doc(db(), "news", id), {
        isPublished: !currentState,
      })

      setNews((prev) => prev.map((item) => (item.id === id ? { ...item, isPublished: !currentState } : item)))

      toast({
        title: "Succès",
        description: `L'article a été ${!currentState ? "publié" : "dépublié"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification de l'état de publication:", error)
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'état de publication",
        variant: "destructive",
      })
    }
  }

  const togglePinned = async (id: string, currentState: boolean) => {
    try {
      await updateDoc(doc(db(), "news", id), {
        isPinned: !currentState,
      })

      setNews((prev) => prev.map((item) => (item.id === id ? { ...item, isPinned: !currentState } : item)))

      toast({
        title: "Succès",
        description: `L'article a été ${!currentState ? "épinglé" : "désépinglé"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification de l'état d'épinglage:", error)
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'état d'épinglage",
        variant: "destructive",
      })
    }
  }

  // Gérer le changement de valeur d'ordre
  const handleOrderChange = (id: string, value: string) => {
    const numValue = Number.parseInt(value, 10) || 0
    setOrderValues((prev) => ({
      ...prev,
      [id]: numValue,
    }))
  }

  // Sauvegarder tous les ordres d'affichage
  const saveAllOrders = async () => {
    try {
      setIsSaving(true)

      // Créer un tableau de promesses pour toutes les mises à jour
      const updatePromises = news.map((item) => {
        const newOrder = orderValues[item.id]
        if (newOrder !== item.displayOrder) {
          return updateDoc(doc(db(), "news", item.id), {
            displayOrder: newOrder,
            updatedAt: serverTimestamp(),
          })
        }
        return Promise.resolve()
      })

      // Attendre que toutes les mises à jour soient terminées
      await Promise.all(updatePromises)

      // Mettre à jour l'état local avec les nouvelles valeurs d'ordre
      const updatedNews = news.map((item) => ({
        ...item,
        displayOrder: orderValues[item.id],
      }))

      // Trier et mettre à jour l'état
      const sortedNews = sortNewsList(updatedNews)
      setNews(sortedNews)
      setFilteredNews(
        sortNewsList(
          filteredNews.map((item) => ({
            ...item,
            displayOrder: orderValues[item.id],
          })),
        ),
      )

      toast({
        title: "Succès",
        description: "Les ordres d'affichage ont été mis à jour avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des ordres d'affichage:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour les ordres d'affichage",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Fonction de tri améliorée
  const sortNewsList = (newsList: NewsItem[]): NewsItem[] => {
    return [...newsList].sort((a, b) => {
      // D'abord les épinglés
      if (a.isPinned && !b.isPinned) return -1
      if (!a.isPinned && b.isPinned) return 1

      // Ensuite par ordre d'affichage
      const orderA = a.displayOrder !== undefined ? a.displayOrder : 0
      const orderB = b.displayOrder !== undefined ? b.displayOrder : 0
      if (orderA !== orderB) return orderA - orderB

      // Enfin par date de création (plus récent d'abord)
      const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
      const dateB = b.createdAt?.toDate?.() || new Date(a.createdAt)
      return dateB.getTime() - dateA.getTime()
    })
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6 flex-col sm:flex-row">
        <h1 className="text-3xl font-bold mb-4">Gestion des actualités</h1>
        <div className="flex gap-2 w-full sm:w-auto">
          <Button onClick={saveAllOrders} disabled={isSaving} variant="outline">
            {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
            Enregistrer l'ordre
          </Button>
          <Button onClick={() => router.push("/admin/news/create")}>
            <Plus className="mr-2 h-4 w-4" />
            Nouvel article
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <Input
            placeholder="Rechercher un article..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Titre</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Date de création</TableHead>
                  <TableHead>Dernière modification</TableHead>
                  <TableHead className="text-center">Ordre d'affichage</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredNews.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      Aucun article trouvé
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNews.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {item.isPinned && <Pin className="h-4 w-4 text-yellow-500" />}
                          {item.title}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={item.isPublished ? "default" : "secondary"}
                          className={cn(
                            "font-medium",
                            item.isPublished
                              ? "bg-green-600 hover:bg-green-700"
                              : "bg-amber-500 hover:bg-amber-600 text-white",
                          )}
                        >
                          {item.isPublished ? "Publié" : "Brouillon"}
                        </Badge>
                        {item.isPinned && (
                          <Badge className="ml-2 bg-blue-500 hover:bg-blue-600 text-white">Épinglé</Badge>
                        )}
                      </TableCell>
                      <TableCell>{formatDate(item.createdAt)}</TableCell>
                      <TableCell>{formatDate(item.updatedAt)}</TableCell>
                      <TableCell className="text-center">
                        <Input
                          type="number"
                          value={orderValues[item.id] || 0}
                          onChange={(e) => handleOrderChange(item.id, e.target.value)}
                          className="w-20 mx-auto text-center"
                          min="0"
                        />
                      </TableCell>
                      {/* Modifier la section des actions dans le tableau pour inclure le bouton d'historique */}
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => togglePublished(item.id, item.isPublished)}
                            title={item.isPublished ? "Dépublier" : "Publier"}
                          >
                            {item.isPublished ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>

                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => togglePinned(item.id, item.isPinned)}
                            title={item.isPinned ? "Désépingler" : "Épingler"}
                          >
                            {item.isPinned ? <PinOff className="h-4 w-4" /> : <Pin className="h-4 w-4" />}
                          </Button>

                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => router.push(`/admin/news/edit/${item.id}`)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>

                          <HistoryButton contentId={item.id} contentType={ContentType.NEWS} size="icon" />

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuler</AlertDialogCancel>
                                {/* Modification du bouton de suppression pour corriger le problème d'événement */}
                                <AlertDialogAction
                                  onClick={(e) => {
                                    e.preventDefault() // Empêcher le comportement par défaut
                                    handleDelete(item.id)
                                  }}
                                  disabled={isDeleting === item.id} // Désactiver pendant la suppression
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  {isDeleting === item.id ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Suppression...
                                    </>
                                  ) : (
                                    "Supprimer"
                                  )}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
