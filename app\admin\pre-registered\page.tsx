"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { db } from "@/lib/firebase"
import {
  collection,
  query,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  setDoc,
  serverTimestamp,
  addDoc,
} from "firebase/firestore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import {
  Loader2,
  Search,
  Download,
  Upload,
  Trash2,
  RefreshCw,
  FileText,
  CheckCircle2,
  Filter,
  AlertTriangle,
  Info,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Papa from "papaparse"
import { validateCsvRow } from "@/lib/import-csv-schema"
import { Progress } from "@/components/ui/progress"

// Import the utility functions at the top of the file
import { createMatchingKey } from "@/lib/user-matching-utils"

interface PreRegisteredUser {
  id: string
  clientCode: string
  clientType: string
  network: string
  department: string
  name: string
  siretLastDigits: string
  additionalGroups: string[]
  importedAt: any
  isRegistered?: boolean
  registeredUserId?: string
  registeredAt?: any
}

export default function PreRegisteredUsersPage() {
  const [users, setUsers] = useState<PreRegisteredUser[]>([])
  const [filteredUsers, setFilteredUsers] = useState<PreRegisteredUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState<"all" | "registered" | "pending">("all")
  const [filterType, setFilterType] = useState<string>("all")
  const [clientTypes, setClientTypes] = useState<string[]>([])
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importMode, setImportMode] = useState<"replace" | "update" | "add">("update")
  const [stats, setStats] = useState({
    total: 0,
    registered: 0,
    pending: 0,
  })
  const { toast } = useToast()
  const [progress, setProgress] = useState(0)
  const [isSyncingGroups, setIsSyncingGroups] = useState(false)

  useEffect(() => {
    fetchUsers()
  }, [])

  useEffect(() => {
    // Appliquer les filtres
    let filtered = [...users]

    // Filtre par terme de recherche
    if (searchTerm) {
      const lowercasedSearch = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (user) =>
          user.clientCode.toLowerCase().includes(lowercasedSearch) ||
          user.name.toLowerCase().includes(lowercasedSearch) ||
          user.siretLastDigits.includes(lowercasedSearch) ||
          user.clientType.toLowerCase().includes(lowercasedSearch),
      )
    }

    // Filtre par statut
    if (filterStatus !== "all") {
      filtered = filtered.filter((user) => (filterStatus === "registered" ? user.isRegistered : !user.isRegistered))
    }

    // Filtre par type de client
    if (filterType !== "all") {
      filtered = filtered.filter((user) => user.clientType === filterType)
    }

    setFilteredUsers(filtered)
  }, [searchTerm, filterStatus, filterType, users])

  // Extraire les types de clients uniques
  useEffect(() => {
    const types = [...new Set(users.map((user) => user.clientType))].sort()
    setClientTypes(types)
  }, [users])

  // Calculer les statistiques
  useEffect(() => {
    const registered = users.filter((user) => user.isRegistered).length
    setStats({
      total: users.length,
      registered,
      pending: users.length - registered,
    })
  }, [users])

  const fetchUsers = async () => {
    try {
      setIsLoading(true)

      // Récupérer les utilisateurs pré-enregistrés
      const preRegisteredRef = collection(db(), "importedUsers")
      const preRegisteredSnapshot = await getDocs(preRegisteredRef)

      // Récupérer les utilisateurs enregistrés pour faire la correspondance
      const registeredRef = collection(db(), "users")
      const registeredSnapshot = await getDocs(registeredRef)

      // Créer un map des utilisateurs enregistrés par clientCode et siretLastDigits
      const registeredMap = new Map()
      registeredSnapshot.forEach((doc) => {
        const userData = doc.data()
        if (userData.clientCode && userData.siretLastDigits) {
          const key = createMatchingKey(userData.clientCode, userData.siretLastDigits)
          registeredMap.set(key, {
            id: doc.id,
            registeredAt: userData.createdAt,
          })
        }
      })

      // Traiter les utilisateurs pré-enregistrés
      const preRegisteredUsers: PreRegisteredUser[] = []
      preRegisteredSnapshot.forEach((doc) => {
        const userData = doc.data() as Omit<PreRegisteredUser, "id">
        const key = createMatchingKey(userData.clientCode, userData.siretLastDigits)
        const registeredUser = registeredMap.get(key)

        preRegisteredUsers.push({
          id: doc.id,
          ...userData,
          additionalGroups: userData.additionalGroups || [],
          isRegistered: !!registeredUser,
          registeredUserId: registeredUser?.id,
          registeredAt: registeredUser?.registeredAt,
        })
      })

      setUsers(preRegisteredUsers)
      setFilteredUsers(preRegisteredUsers)
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs pré-enregistrés:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les utilisateurs pré-enregistrés",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteSelected = async () => {
    if (selectedUsers.length === 0) return

    try {
      setIsDeleting(true)

      for (const userId of selectedUsers) {
        await deleteDoc(doc(db(), "importedUsers", userId))
      }

      toast({
        title: "Succès",
        description: `${selectedUsers.length} utilisateur(s) supprimé(s)`,
      })

      // Rafraîchir la liste
      fetchUsers()
      setSelectedUsers([])
      setSelectAll(false)
    } catch (error) {
      console.error("Erreur lors de la suppression:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer les utilisateurs sélectionnés",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleExportCSV = () => {
    try {
      setIsExporting(true)

      // Préparer les données pour l'export
      const dataToExport = filteredUsers.map((user) => ({
        "N° client": user.clientCode,
        "Type de client": user.clientType,
        Réseau: user.network,
        Département: user.department,
        Nom: user.name,
        "5 derniers SIRET": user.siretLastDigits,
        "Groupes supplémentaires": user.additionalGroups.join(", "),
        Statut: user.isRegistered ? "Inscrit" : "En attente",
        "Date d'importation": formatDate(user.importedAt),
        "Date d'inscription": user.registeredAt ? formatDate(user.registeredAt) : "",
      }))

      // Générer le CSV
      const csv = Papa.unparse(dataToExport)

      // Créer un blob et un lien de téléchargement
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")
      const url = URL.createObjectURL(blob)

      link.setAttribute("href", url)
      link.setAttribute("download", `utilisateurs-pre-enregistres-${new Date().toISOString().split("T")[0]}.csv`)
      link.style.visibility = "hidden"

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Export réussi",
        description: `${dataToExport.length} utilisateurs exportés`,
      })
    } catch (error) {
      console.error("Erreur lors de l'export:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'exporter les données",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleImportFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile && selectedFile.name.toLowerCase().endsWith(".csv")) {
      setImportFile(selectedFile)
    } else if (selectedFile) {
      toast({
        title: "Format incorrect",
        description: "Veuillez sélectionner un fichier CSV",
        variant: "destructive",
      })
    }
  }

  // Ajouter la fonction de normalisation du SIRET
  const normalizeSiret = (siret: string): string => {
    // Supprimer les espaces et caractères non numériques
    const digitsOnly = siret.replace(/\D/g, "")
    // Garantir exactement 5 chiffres avec padStart puis slice pour s'assurer qu'on prend les 5 derniers
    return digitsOnly.padStart(5, "0").slice(-5)
  }

  const handleImportCSV = async () => {
    if (!importFile) return

    try {
      setIsImporting(true)
      setProgress(0)

      // Lire le fichier CSV
      const results = await new Promise<Papa.ParseResult<Record<string, string>>>((resolve, reject) => {
        Papa.parse(importFile, {
          header: true,
          skipEmptyLines: true,
          complete: resolve,
          error: reject,
        })
      })

      // Vérifier les en-têtes requis
      const requiredHeaders = ["N° client", "Type de client", "5 derniers SIRET"]
      const missingHeaders = requiredHeaders.filter((header) => !results.meta.fields?.includes(header))

      if (missingHeaders.length > 0) {
        toast({
          title: "En-têtes manquants",
          description: `Les en-têtes suivants sont requis : ${missingHeaders.join(", ")}`,
          variant: "destructive",
        })
        return
      }

      // Si mode "replace", supprimer d'abord tous les utilisateurs existants
      if (importMode === "replace") {
        const snapshot = await getDocs(collection(db(), "importedUsers"))

        // Simuler la progression de la suppression
        let deleteCount = 0
        const totalToDelete = snapshot.size

        for (const doc of snapshot.docs) {
          await deleteDoc(doc(db(), "importedUsers", doc.id))
          deleteCount++
          setProgress((deleteCount / (totalToDelete + results.data.length)) * 100 * 0.5)
        }
      }

      // Traiter les données
      let created = 0
      let updated = 0
      let skipped = 0
      let errors = 0

      for (let i = 0; i < results.data.length; i++) {
        const row = results.data[i]
        const validatedData = validateCsvRow(row)

        if (validatedData) {
          try {
            // Normaliser le SIRET
            const normalizedSiret = validatedData.siretLastDigits.padStart(5, "0")
            validatedData.siretLastDigits = normalizedSiret

            // Créer une clé unique basée sur le code client et les 5 derniers chiffres du SIRET
            const key = `${validatedData.clientCode}-${normalizedSiret}`

            // Vérifier si l'entrée existe déjà
            const existingQuery = query(collection(db(), "importedUsers"))
            const existingSnapshot = await getDocs(existingQuery)

            let existingDoc = null
            existingSnapshot.forEach((doc) => {
              const data = doc.data()
              if (data.clientCode === validatedData.clientCode) {
                // Normaliser le SIRET stocké pour la comparaison en utilisant la nouvelle fonction
                const storedSiret = data.siretLastDigits ? normalizeSiret(data.siretLastDigits) : ""
                const normalizedInputSiret = normalizeSiret(validatedData.siretLastDigits)

                if (storedSiret === normalizedInputSiret) {
                  existingDoc = { id: doc.id, ...data }
                }
              }
            })

            if (existingDoc) {
              // L'entrée existe déjà
              if (importMode === "add") {
                // Mode "ajouter uniquement" - ignorer les doublons
                skipped++
              } else {
                // Mode "mettre à jour" - mettre à jour l'entrée existante
                await updateDoc(doc(db(), "importedUsers", existingDoc.id), {
                  ...validatedData,
                  updatedAt: serverTimestamp(),
                })
                updated++
              }
            } else {
              // Nouvelle entrée
              await setDoc(doc(collection(db()), "importedUsers"), {
                ...validatedData,
                importedAt: serverTimestamp(),
              })
              created++
            }
          } catch (error) {
            console.error("Erreur lors du traitement de la ligne:", error)
            errors++
          }
        } else {
          errors++
        }

        // Mettre à jour la progression
        const progressValue =
          importMode === "replace"
            ? 50 + ((i + 1) / results.data.length) * 50
            : // Si mode replace, commencer à 50% après la suppression
              ((i + 1) / results.data.length) * 100 // Sinon, progression normale de 0 à 100%

        setProgress(progressValue)
      }

      toast({
        title: "Import terminé",
        description: `${created} créés, ${updated} mis à jour, ${skipped} ignorés, ${errors} erreurs`,
        variant: errors > 0 ? "warning" : "default",
      })

      // Rafraîchir la liste
      fetchUsers()
      setShowImportDialog(false)
      setImportFile(null)
    } catch (error) {
      console.error("Erreur lors de l'import:", error)
      toast({
        title: "Erreur d'import",
        description: `Une erreur est survenue : ${(error as Error).message}`,
        variant: "destructive",
      })
    } finally {
      setIsImporting(false)
    }
  }

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id))
    }
    setSelectAll(!selectAll)
  }

  const toggleSelectUser = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId))
    } else {
      setSelectedUsers([...selectedUsers, userId])
    }
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const syncGroups = async () => {
    setIsSyncingGroups(true)
    try {
      // 1. Fetch all existing groups from Firestore
      const existingGroupsRef = collection(db(), "groups")
      const existingGroupsSnapshot = await getDocs(existingGroupsRef)
      const existingGroupNames = new Set(existingGroupsSnapshot.docs.map((doc) => doc.data().name))

      // 2. Extract all unique group names from the 'Groupes Supplémentaires' column of the imported users
      const allGroupsFromCSV = new Set<string>()
      users.forEach((user) => {
        if (user.additionalGroups && Array.isArray(user.additionalGroups)) {
          user.additionalGroups.forEach((group) => {
            allGroupsFromCSV.add(group)
          })
        }
      })

      // 3. Identify the new groups that do not exist in Firestore
      const newGroups = Array.from(allGroupsFromCSV).filter((groupName) => !existingGroupNames.has(groupName))

      // 4. Create the new groups in Firestore
      let createdCount = 0
      for (const groupName of newGroups) {
        try {
          await addDoc(collection(db(), "groups"), {
            name: groupName,
            description: "Groupe créé automatiquement lors de l'import CSV",
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          })
          createdCount++
          console.log(`Groupe "${groupName}" créé avec succès.`)
        } catch (error) {
          console.error(`Erreur lors de la création du groupe "${groupName}":`, error)
        }
      }

      toast({
        title: "Synchronisation terminée",
        description: `${createdCount} nouveaux groupes créés.`,
      })
    } catch (error) {
      console.error("Erreur lors de la synchronisation des groupes:", error)
      toast({
        title: "Erreur de synchronisation",
        description: "Une erreur est survenue lors de la synchronisation des groupes.",
        variant: "destructive",
      })
    } finally {
      setIsSyncingGroups(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Utilisateurs pré-enregistrés</h1>
          <p className="text-muted-foreground">Gérez les utilisateurs importés depuis le fichier CSV</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => setShowImportDialog(true)} disabled={isImporting}>
            <Upload className="mr-2 h-4 w-4" />
            Importer CSV
          </Button>
          <Button variant="outline" onClick={handleExportCSV} disabled={isExporting || filteredUsers.length === 0}>
            <Download className="mr-2 h-4 w-4" />
            Exporter CSV
          </Button>
          <Button variant="outline" onClick={fetchUsers} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            Actualiser
          </Button>
          <Button variant="outline" onClick={syncGroups} disabled={isSyncingGroups}>
            {isSyncingGroups ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Synchroniser les groupes
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.total}</div>
            <p className="text-muted-foreground text-sm">Utilisateurs pré-enregistrés</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Inscrits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">{stats.registered}</div>
            <p className="text-muted-foreground text-sm">Utilisateurs ayant créé un compte</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">En attente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-amber-600">{stats.pending}</div>
            <p className="text-muted-foreground text-sm">Utilisateurs non encore inscrits</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des utilisateurs pré-enregistrés</CardTitle>
          <CardDescription>
            Ces utilisateurs pourront s'inscrire automatiquement en utilisant leur code client et les 5 derniers
            chiffres de leur SIRET
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex items-center space-x-2 flex-1">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>

              <div className="flex gap-2">
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les statuts</SelectItem>
                      <SelectItem value="registered">Inscrits</SelectItem>
                      <SelectItem value="pending">En attente</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Type de client" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les types</SelectItem>
                      {clientTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {selectedUsers.length > 0 && (
              <div className="flex items-center justify-between bg-blue-50 p-2 rounded-md">
                <span className="text-sm text-blue-700">{selectedUsers.length} utilisateur(s) sélectionné(s)</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteSelected}
                  disabled={isDeleting}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Supprimer
                </Button>
              </div>
            )}

            <Tabs defaultValue="table" className="w-full">
              <TabsList className="w-full">
                <TabsTrigger value="table" className="flex-1">
                  Tableau
                </TabsTrigger>
                <TabsTrigger value="cards" className="flex-1">
                  Cartes
                </TabsTrigger>
              </TabsList>

              <TabsContent value="table" className="mt-4">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Aucun utilisateur trouvé</p>
                  </div>
                ) : (
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">
                            <Checkbox checked={selectAll} onCheckedChange={toggleSelectAll} />
                          </TableHead>
                          <TableHead>Code client</TableHead>
                          <TableHead>Nom</TableHead>
                          <TableHead>Type de client</TableHead>
                          <TableHead>SIRET (5 derniers)</TableHead>
                          <TableHead>Réseau</TableHead>
                          <TableHead>Département</TableHead>
                          <TableHead>Groupes</TableHead>
                          <TableHead>Statut</TableHead>
                          <TableHead>Date d'importation</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={() => toggleSelectUser(user.id)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{user.clientCode}</TableCell>
                            <TableCell>{user.name}</TableCell>
                            <TableCell>{user.clientType}</TableCell>
                            <TableCell>{user.siretLastDigits}</TableCell>
                            <TableCell>{user.network}</TableCell>
                            <TableCell>{user.department}</TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {user.additionalGroups && user.additionalGroups.length > 0 ? (
                                  user.additionalGroups.map((group, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      {group}
                                    </Badge>
                                  ))
                                ) : (
                                  <span className="text-muted-foreground text-xs">Aucun</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {user.isRegistered ? (
                                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                  <CheckCircle2 className="mr-1 h-3 w-3" />
                                  Inscrit
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="text-amber-600">
                                  <AlertTriangle className="mr-1 h-3 w-3" />
                                  En attente
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>{formatDate(user.importedAt)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="cards" className="mt-4">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Aucun utilisateur trouvé</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredUsers.map((user) => (
                      <Card key={user.id} className="overflow-hidden">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-lg">{user.name || "Sans nom"}</CardTitle>
                              <CardDescription>Code client: {user.clientCode}</CardDescription>
                            </div>
                            <Checkbox
                              checked={selectedUsers.includes(user.id)}
                              onCheckedChange={() => toggleSelectUser(user.id)}
                            />
                          </div>
                        </CardHeader>
                        <CardContent className="pb-4">
                          <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                            <div>
                              <p className="text-muted-foreground">Type de client</p>
                              <p className="font-medium">{user.clientType}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">SIRET (5 derniers)</p>
                              <p className="font-medium">{user.siretLastDigits}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Réseau</p>
                              <p className="font-medium">{user.network || "N/A"}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Département</p>
                              <p className="font-medium">{user.department || "N/A"}</p>
                            </div>
                          </div>

                          <div className="mb-3">
                            <p className="text-muted-foreground text-sm mb-1">Groupes supplémentaires</p>
                            <div className="flex flex-wrap gap-1">
                              {user.additionalGroups && user.additionalGroups.length > 0 ? (
                                user.additionalGroups.map((group, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {group}
                                  </Badge>
                                ))
                              ) : (
                                <span className="text-muted-foreground text-xs">Aucun</span>
                              )}
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            {user.isRegistered ? (
                              <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                                <CheckCircle2 className="mr-1 h-3 w-3" />
                                Inscrit le {formatDate(user.registeredAt)}
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-amber-600">
                                <AlertTriangle className="mr-1 h-3 w-3" />
                                En attente
                              </Badge>
                            )}
                            <span className="text-xs text-muted-foreground">
                              Importé le {formatDate(user.importedAt)}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Dialog d'importation CSV */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        {showImportDialog && (
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Importer un fichier CSV</DialogTitle>
              <DialogDescription>
                Importez un fichier CSV contenant les utilisateurs à pré-enregistrer
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="csv-file">Fichier CSV</Label>
                <Input id="csv-file" type="file" accept=".csv" onChange={handleImportFile} disabled={isImporting} />
                {importFile && (
                  <p className="text-sm text-muted-foreground">
                    {importFile.name} ({Math.round(importFile.size / 1024)} Ko)
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Mode d'importation</Label>
                <div className="grid grid-cols-3 gap-2">
                  <div
                    className={`border rounded-md p-3 cursor-pointer ${importMode === "update" ? "border-primary bg-primary/5" : ""}`}
                    onClick={() => setImportMode("update")}
                  >
                    <div className="font-medium mb-1">Mettre à jour</div>
                    <p className="text-xs text-muted-foreground">
                      Met à jour les entrées existantes et ajoute les nouvelles
                    </p>
                  </div>
                  <div
                    className={`border rounded-md p-3 cursor-pointer ${importMode === "add" ? "border-primary bg-primary/5" : ""}`}
                    onClick={() => setImportMode("add")}
                  >
                    <div className="font-medium mb-1">Ajouter</div>
                    <p className="text-xs text-muted-foreground">Ajoute uniquement les nouvelles entrées</p>
                  </div>
                  <div
                    className={`border rounded-md p-3 cursor-pointer ${importMode === "replace" ? "border-primary bg-primary/5" : ""}`}
                    onClick={() => setImportMode("replace")}
                  >
                    <div className="font-medium mb-1">Remplacer</div>
                    <p className="text-xs text-muted-foreground">Supprime tout et importe à nouveau</p>
                  </div>
                </div>
              </div>

              {isImporting && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Importation en cours...</span>
                    <span className="text-sm">{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}

              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Format attendu</AlertTitle>
                <AlertDescription>
                  <p className="text-sm">Le fichier doit contenir les colonnes suivantes :</p>
                  <code className="text-xs block bg-muted p-2 rounded mt-1">
                    N° client,Type de client,Réseau,Département,Nom,5 derniers SIRET,Groupes supplémentaires
                  </code>
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowImportDialog(false)} disabled={isImporting}>
                Annuler
              </Button>
              <Button onClick={handleImportCSV} disabled={!importFile || isImporting}>
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importation...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Importer
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  )
}
