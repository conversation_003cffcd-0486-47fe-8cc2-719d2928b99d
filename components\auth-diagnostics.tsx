"use client"

import { useState, useEffect } from "react"
import { auth } from "@/lib/firebase"
import { useAuth } from "@/lib/hooks/use-auth"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { getStoredAuthUser, getStoredAuthToken } from "@/lib/auth-persistence"

export function AuthDiagnostics() {
  const { user, refreshToken, restoreSession } = useAuth()
  const [diagnostics, setDiagnostics] = useState<{
    firebaseUser: boolean
    storedUser: boolean
    storedToken: boolean
    localStorage: Record<string, string>
    persistenceMethod: string
  }>({
    firebaseUser: false,
    storedUser: false,
    storedToken: false,
    localStorage: {},
    persistenceMethod: "unknown",
  })

  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Fonction pour rafraîchir les diagnostics
  const refreshDiagnostics = async () => {
    const storedUser = await getStoredAuthUser()
    const storedToken = await getStoredAuthToken()

    // Récupérer les clés d'authentification de localStorage
    const localStorageKeys = [
      "auth_user_uid",
      "auth_last_active",
      "auth_last_token",
      "firebase_persistence_method",
      "auth_user_v2",
      "auth_session_v2",
      "auth_timestamp_v2",
    ]

    const localStorageData: Record<string, string> = {}
    localStorageKeys.forEach((key) => {
      const value = localStorage.getItem(key)
      if (value) {
        if (key === "auth_last_token") {
          // Masquer le token pour des raisons de sécurité
          localStorageData[key] = `${value.substring(0, 10)}...${value.substring(value.length - 10)}`
        } else {
          localStorageData[key] = value
        }
      } else {
        localStorageData[key] = "non défini"
      }
    })

    setDiagnostics({
      firebaseUser: !!auth.currentUser,
      storedUser: !!storedUser,
      storedToken: !!storedToken,
      localStorage: localStorageData,
      persistenceMethod: localStorage.getItem("firebase_persistence_method") || "non défini",
    })

    setLastUpdated(new Date())
  }

  // Rafraîchir les diagnostics au chargement et lorsque l'utilisateur change
  useEffect(() => {
    refreshDiagnostics()

    // Rafraîchir périodiquement
    const interval = setInterval(refreshDiagnostics, 30000) // Toutes les 30 secondes

    return () => clearInterval(interval)
  }, [user])

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Diagnostic d'authentification</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">État actuel</h3>
              <div className="grid grid-cols-2 gap-2">
                <span>Firebase User:</span>
                <span className={diagnostics.firebaseUser ? "text-green-600" : "text-red-600"}>
                  {diagnostics.firebaseUser ? "Présent" : "Absent"}
                </span>

                <span>Utilisateur stocké:</span>
                <span className={diagnostics.storedUser ? "text-green-600" : "text-red-600"}>
                  {diagnostics.storedUser ? "Présent" : "Absent"}
                </span>

                <span>Token stocké:</span>
                <span className={diagnostics.storedToken ? "text-green-600" : "text-red-600"}>
                  {diagnostics.storedToken ? "Présent" : "Absent"}
                </span>

                <span>Méthode de persistance:</span>
                <span>{diagnostics.persistenceMethod}</span>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Actions</h3>
              <div className="space-y-2">
                <Button onClick={refreshDiagnostics} variant="outline" size="sm" className="w-full">
                  Rafraîchir les diagnostics
                </Button>

                <Button onClick={() => refreshToken()} variant="outline" size="sm" className="w-full">
                  Rafraîchir le token
                </Button>

                <Button onClick={() => restoreSession()} variant="outline" size="sm" className="w-full">
                  Restaurer la session
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Données localStorage</h3>
            <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm overflow-auto max-h-40">
              <pre>{JSON.stringify(diagnostics.localStorage, null, 2)}</pre>
            </div>
          </div>

          <div className="text-xs text-gray-500 text-right">Dernière mise à jour: {lastUpdated.toLocaleString()}</div>
        </div>
      </CardContent>
    </Card>
  )
}
