"use client"

import type React from "react"

import { useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ImagePlus, X, Loader2, Plus, Trash2 } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"

export interface GridItem {
  id: string
  title: string
  description?: string
  imageUrl?: string
  link?: string
  file?: File
}

interface GridModelProps {
  items: GridItem[]
  onItemsChange: (items: GridItem[]) => void
  isUploading?: boolean
}

export function GridModel({ items, onItemsChange, isUploading = false }: GridModelProps) {
  const fileInputRefs = useRef<Record<string, HTMLInputElement | null>>({})

  const handleAddItem = () => {
    const newItem: GridItem = {
      id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: "",
      description: "",
      link: "",
    }
    onItemsChange([...items, newItem])
  }

  const handleRemoveItem = (id: string) => {
    onItemsChange(items.filter((item) => item.id !== id))
  }

  const handleItemChange = (id: string, field: keyof GridItem, value: string) => {
    const newItems = items.map((item) => {
      if (item.id === id) {
        return { ...item, [field]: value }
      }
      return item
    })
    onItemsChange(newItems)
  }

  const handleFileChange = (id: string, e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      const url = URL.createObjectURL(file)

      const newItems = items.map((item) => {
        if (item.id === id) {
          return { ...item, imageUrl: url, file }
        }
        return item
      })

      onItemsChange(newItems)

      // Reset input
      if (fileInputRefs.current[id]) {
        fileInputRefs.current[id]!.value = ""
      }
    }
  }

  const handleRemoveImage = (id: string) => {
    const newItems = items.map((item) => {
      if (item.id === id) {
        const { imageUrl, file, ...rest } = item
        return rest
      }
      return item
    })

    onItemsChange(newItems)
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const sourceIndex = result.source.index
    const destinationIndex = result.destination.index

    const reorderedItems = Array.from(items)
    const [removed] = reorderedItems.splice(sourceIndex, 1)
    reorderedItems.splice(destinationIndex, 0, removed)

    onItemsChange(reorderedItems)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Éléments de la grille</Label>

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="grid-items">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                {items.map((item, index) => (
                  <Draggable key={item.id} draggableId={item.id} index={index}>
                    {(provided) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className="border"
                      >
                        <CardHeader className="p-4 pb-2 flex flex-row justify-between items-center">
                          <h4 className="font-medium">Élément {index + 1}</h4>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </CardHeader>
                        <CardContent className="p-4 pt-0 space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor={`title-${item.id}`}>Titre</Label>
                            <Input
                              id={`title-${item.id}`}
                              value={item.title || ""}
                              onChange={(e) => handleItemChange(item.id, "title", e.target.value)}
                              placeholder="Titre de l'élément"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`description-${item.id}`}>Description</Label>
                            <Textarea
                              id={`description-${item.id}`}
                              value={item.description || ""}
                              onChange={(e) => handleItemChange(item.id, "description", e.target.value)}
                              placeholder="Description de l'élément"
                              rows={2}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`link-${item.id}`}>Lien (optionnel)</Label>
                            <Input
                              id={`link-${item.id}`}
                              value={item.link || ""}
                              onChange={(e) => handleItemChange(item.id, "link", e.target.value)}
                              placeholder="https://exemple.com"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Image</Label>
                            {item.imageUrl ? (
                              <div className="relative">
                                <img
                                  src={item.imageUrl || "/placeholder.svg"}
                                  alt={item.title || "Image de l'élément"}
                                  className="w-full h-40 object-cover rounded-md"
                                />
                                <Button
                                  type="button"
                                  size="icon"
                                  variant="destructive"
                                  className="absolute top-2 right-2 h-6 w-6 rounded-full"
                                  onClick={() => handleRemoveImage(item.id)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            ) : (
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => fileInputRefs.current[item.id]?.click()}
                                disabled={isUploading}
                                className="w-full py-8"
                              >
                                <ImagePlus className="mr-2 h-4 w-4" />
                                Ajouter une image
                              </Button>
                            )}
                            <input
                              ref={(el) => (fileInputRefs.current[item.id] = el)}
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleFileChange(item.id, e)}
                              className="hidden"
                            />
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>

        <Button type="button" variant="outline" onClick={handleAddItem} disabled={isUploading} className="w-full mt-4">
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Chargement...
            </>
          ) : (
            <>
              <Plus className="mr-2 h-4 w-4" />
              Ajouter un élément
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
