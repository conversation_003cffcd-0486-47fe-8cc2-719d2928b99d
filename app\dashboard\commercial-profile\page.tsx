"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { X, Upload, AlertCircle } from "lucide-react"
import { getCommercialContactByUserId, saveCommercialContact, uploadCommercialPhoto } from "@/lib/commercial-utils"
import { DEPARTMENTS } from "@/lib/commercial-types"
import type { CommercialContact } from "@/lib/commercial-types"
import { useAuth } from "@/components/auth-provider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTitle } from "@/components/ui/alert"
import { getDoc, doc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import { AccessDenied } from "@/components/access-denied"

function CommercialProfileContent() {
  const router = useRouter()
  const { user, hasPermission } = useAuth()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [contact, setContact] = useState<CommercialContact | null>(null)
  const [name, setName] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [departments, setDepartments] = useState<string[]>([])
  const [photoFile, setPhotoFile] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [photoURL, setPhotoURL] = useState<string>("")
  const [error, setError] = useState<string | null>(null)
  const [hasCommercialRole, setHasCommercialRole] = useState(false)

  const { toast } = useToast()

  useEffect(() => {
    toast({
      title: "Gestion de votre profil commercial",
      description: "Vous pouvez modifier les informations de votre profil commercial ici.",
    })
  }, [toast])

  useEffect(() => {
    if (!user) return

    const fetchCommercialProfile = async () => {
      try {
        setLoading(true)
        const data = await getCommercialContactByUserId(user.uid)

        if (data) {
          setContact(data)
          setName(data.name)
          // Try to split the name into firstName and lastName
          const nameParts = data.name.split(" ")
          if (nameParts.length > 1) {
            setFirstName(nameParts[0])
            setLastName(nameParts.slice(1).join(" "))
          } else {
            setFirstName(data.name)
            setLastName("")
          }
          setEmail(data.email)
          setPhone(data.phone)
          setDepartments(data.departments || [])
          setPhotoURL(data.photoURL || "")
          if (data.photoURL) {
            setPhotoPreview(data.photoURL)
          }
          setError(null)
        } else {
          // Pas de profil commercial trouvé pour cet utilisateur
          setError("Vous n'avez pas encore de profil commercial. Veuillez contacter un administrateur.")
        }
      } catch (error) {
        console.error("Error fetching commercial profile:", error)
        setError("Impossible de récupérer votre profil commercial.")
      } finally {
        setLoading(false)
      }
    }

    fetchCommercialProfile()
  }, [user])

  // Add a useEffect to update name when firstName or lastName changes
  useEffect(() => {
    setName(`${firstName} ${lastName}`.trim())
  }, [firstName, lastName])

  // Ajouter un useEffect pour vérifier si l'utilisateur a le rôle "commercial"
  useEffect(() => {
    if (user) {
      // Vérifier si l'utilisateur a le rôle "commercial"
      const checkCommercialRole = async () => {
        try {
          const userDoc = await getDoc(doc(db(), "users", user.uid))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            const roles = userData.roles || []
            setHasCommercialRole(roles.includes("commercial"))
          }
        } catch (error) {
          console.error("Error checking commercial role:", error)
        }
      }

      checkCommercialRole()
    }
  }, [user])

  // Modifier la condition d'accès
  const canEditProfile = hasCommercialRole

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setPhotoFile(file)

      // Create a preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setPhotoPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleAddDepartment = (departmentCode: string) => {
    if (!departments.includes(departmentCode)) {
      setDepartments([...departments, departmentCode])
    }
  }

  const handleRemoveDepartment = (departmentCode: string) => {
    setDepartments(departments.filter((d) => d !== departmentCode))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!firstName || !lastName || !email || !phone) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)

    try {
      // Upload new photo if provided
      let updatedPhotoURL = photoURL
      if (photoFile && contact) {
        updatedPhotoURL = await uploadCommercialPhoto(photoFile, contact.id)
      }

      // Save the contact
      if (contact) {
        await saveCommercialContact({
          id: contact.id,
          name: `${firstName} ${lastName}`.trim(),
          email,
          phone,
          departments,
          photoURL: updatedPhotoURL,
          userId: user?.uid, // Conserver le lien avec l'utilisateur
        })

        toast({
          title: "Succès",
          description: "Votre profil commercial a été mis à jour avec succès.",
        })
      }
    } catch (error) {
      console.error("Error updating commercial profile:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour de votre profil.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <h1 className="text-3xl font-bold tracking-tight">Mon profil commercial</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!canEditProfile) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <h1 className="text-3xl font-bold tracking-tight">Mon profil commercial</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Accès refusé</AlertTitle>
          <AlertDescription>Vous n'avez pas les permissions nécessaires pour accéder à cette page.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Mon profil commercial</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Mes informations de contact</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Prénom *</Label>
                    <Input
                      id="firstName"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      placeholder="Jean"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nom *</Label>
                    <Input
                      id="lastName"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      placeholder="Dupont"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Téléphone *</Label>
                  <Input
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="06 12 34 56 78"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department">Ajouter un département</Label>
                  <Select onValueChange={handleAddDepartment}>
                    <SelectTrigger id="department">
                      <SelectValue placeholder="Sélectionner un département" />
                    </SelectTrigger>
                    <SelectContent>
                      {DEPARTMENTS.map((dept) => (
                        <SelectItem key={dept.code} value={dept.code}>
                          {dept.code} - {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Départements assignés</Label>
                {departments.length === 0 ? (
                  <p className="text-sm text-muted-foreground">Aucun département assigné</p>
                ) : (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {departments.map((dept) => {
                      const deptName = DEPARTMENTS.find((d) => d.code === dept)?.name || dept
                      return (
                        <Badge key={dept} variant="secondary" className="flex items-center gap-1">
                          {dept} - {deptName}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                            onClick={() => handleRemoveDepartment(dept)}
                          >
                            <X className="h-3 w-3" />
                            <span className="sr-only">Supprimer</span>
                          </Button>
                        </Badge>
                      )
                    })}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="photo">Photo de profil</Label>
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    {photoPreview ? (
                      <AvatarImage src={photoPreview || "/placeholder.svg"} alt="Preview" />
                    ) : (
                      <AvatarFallback>?</AvatarFallback>
                    )}
                  </Avatar>
                  <div className="flex-1">
                    <Input id="photo" type="file" accept="image/*" onChange={handlePhotoChange} className="hidden" />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById("photo")?.click()}
                      className="w-full"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {photoFile ? "Changer la photo" : photoURL ? "Modifier la photo" : "Télécharger une photo"}
                    </Button>
                    {photoFile && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {photoFile.name} ({Math.round(photoFile.size / 1024)} Ko)
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button type="submit" disabled={saving}>
                {saving ? (
                  <>
                    <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                    Enregistrement...
                  </>
                ) : (
                  "Enregistrer"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default function CommercialProfilePage() {
  const { user } = useAuth()
  const [hasCommercialRole, setHasCommercialRole] = useState(false)

  useEffect(() => {
    if (user) {
      const checkCommercialRole = async () => {
        try {
          const userDoc = await getDoc(doc(db(), "users", user.uid))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            const roles = userData.roles || []
            setHasCommercialRole(roles.includes("commercial"))
          }
        } catch (error) {
          console.error("Error checking commercial role:", error)
        }
      }

      checkCommercialRole()
    }
  }, [user])

  // If the user has the commercial role, show the profile content
  // Otherwise show access denied
  return hasCommercialRole ? <CommercialProfileContent /> : <AccessDenied />
}
