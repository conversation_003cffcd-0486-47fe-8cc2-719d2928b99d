"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { auth, db } from "@/lib/firebase"
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth"
import { doc, setDoc, serverTimestamp, getDoc } from "firebase/firestore"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Eye, EyeOff, Mail, Lock } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { z } from "zod"

// Import the utility functions at the top of the file
import { normalizeClientCode } from "@/lib/user-matching-utils"

// Schéma de validation
const registerSchema = z
  .object({
    clientCode: z
      .string()
      .length(6, "Le code client doit comporter exactement 6 chiffres")
      .regex(/^\d{6}$/, "Le code client doit contenir uniquement des chiffres"),
    siretLastDigits: z
      .string()
      .length(5, "Les 5 derniers chiffres du SIRET sont requis")
      .regex(/^\d{5}$/, "Format invalide, uniquement des chiffres"),
    firstName: z.string().min(1, "Le prénom est requis"),
    lastName: z.string().min(1, "Le nom est requis"),
    email: z.string().email("Adresse email invalide"),
    phone: z.string().optional(),
    jobTitle: z.string().optional(),
    password: z.string().min(8, "Le mot de passe doit contenir au moins 8 caractères"),
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine((val) => val === true, "Vous devez accepter les conditions d'utilisation"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirmPassword"],
  })

type RegisterFormData = z.infer<typeof registerSchema>

export function RegisterForm() {
  const [formData, setFormData] = useState<RegisterFormData>({
    clientCode: "",
    siretLastDigits: "000", // Pré-remplir avec 3 zéros
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    jobTitle: "",
    password: "",
    confirmPassword: "",
    acceptTerms: false,
  })

  const [errors, setErrors] = useState<Partial<Record<keyof RegisterFormData, string>>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showTerms, setShowTerms] = useState(false)
  const [showPrivacy, setShowPrivacy] = useState(false)
  const [termsContent, setTermsContent] = useState("")
  const [privacyContent, setPrivacyContent] = useState("")

  const { toast } = useToast()
  const router = useRouter()

  // Fonction pour charger les contenus des conditions et politique
  const fetchContent = async () => {
    try {
      // Définir un contenu par défaut au cas où les documents ne seraient pas accessibles
      const defaultTerms =
        "<p>Les conditions d'utilisation ne sont pas disponibles actuellement. En vous inscrivant, vous acceptez les conditions générales d'utilisation du service.</p>"
      const defaultPrivacy =
        "<p>La politique de confidentialité n'est pas disponible actuellement. Nous nous engageons à protéger vos données personnelles conformément à la législation en vigueur.</p>"

      try {
        // Essayer de récupérer les documents
        const termsDoc = await getDoc(doc(db(), "settings", "terms"))
        if (termsDoc.exists()) {
          setTermsContent(termsDoc.data().content || defaultTerms)
        } else {
          console.log("Document terms n'existe pas, utilisation du contenu par défaut")
          setTermsContent(defaultTerms)
        }
      } catch (termsError) {
        console.error("Erreur lors du chargement des conditions d'utilisation:", termsError)
        setTermsContent(defaultTerms)
      }

      try {
        // Essayer de récupérer les documents
        const privacyDoc = await getDoc(doc(db(), "settings", "privacy"))
        if (privacyDoc.exists()) {
          setPrivacyContent(privacyDoc.data().content || defaultPrivacy)
        } else {
          console.log("Document privacy n'existe pas, utilisation du contenu par défaut")
          setPrivacyContent(defaultPrivacy)
        }
      } catch (privacyError) {
        console.error("Erreur lors du chargement de la politique de confidentialité:", privacyError)
        setPrivacyContent(defaultPrivacy)
      }
    } catch (error) {
      console.error("Erreur générale lors du chargement des contenus:", error)
      // Définir un contenu par défaut en cas d'erreur
      setTermsContent(
        "<p>Les conditions d'utilisation ne sont pas disponibles actuellement. En vous inscrivant, vous acceptez les conditions générales d'utilisation du service.</p>",
      )
      setPrivacyContent(
        "<p>La politique de confidentialité n'est pas disponible actuellement. Nous nous engageons à protéger vos données personnelles conformément à la législation en vigueur.</p>",
      )
    }
  }

  // Charger les contenus au chargement du composant
  useEffect(() => {
    fetchContent()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))

    // Effacer l'erreur lorsque l'utilisateur modifie le champ
    if (errors[name as keyof RegisterFormData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }))
    }
  }

  const validateForm = (): boolean => {
    try {
      registerSchema.parse(formData)
      setErrors({})
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof RegisterFormData, string>> = {}
        error.errors.forEach((err) => {
          const path = err.path[0] as keyof RegisterFormData
          newErrors[path] = err.message
        })
        setErrors(newErrors)
      }
      return false
    }
  }

  // Fonction améliorée de normalisation du SIRET
  const normalizeSiret = (siret: string): string => {
    // Supprimer les espaces et caractères non numériques
    const digitsOnly = siret.replace(/\D/g, "")
    // Garantir exactement 5 chiffres avec padStart puis slice pour s'assurer qu'on prend les 5 derniers
    return digitsOnly.padStart(5, "0").slice(-5)
  }

  // Version simplifiée qui ne fait pas d'appel API
  const getDefaultUserGroups = () => {
    return {
      isConfirmed: true,
      groups: ["default"],
      clientType: "default",
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Dans la fonction handleSubmit, remplacer la partie qui utilise getDefaultUserGroups() par un appel à l'API

      // Remplacer cette partie:
      // Utiliser directement les valeurs par défaut sans appel API
      // const { isConfirmed, groups, clientType } = getDefaultUserGroups()

      // Par celle-ci:
      // Vérifier si l'utilisateur est pré-enregistré
      const preRegistrationResponse = await fetch("/api/check-pre-registration", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientCode: formData.clientCode,
          siretLastDigits: formData.siretLastDigits,
        }),
      })

      if (!preRegistrationResponse.ok) {
        throw new Error("Erreur lors de la vérification de pré-enregistrement")
      }

      const { isConfirmed, groups, clientType, matchDetails } = await preRegistrationResponse.json()

      // Afficher un message différent selon le résultat de la vérification
      let registrationMessage = isConfirmed
        ? "Votre compte a été créé et activé."
        : "Votre compte a été créé mais doit être validé par un administrateur. Vous serez notifié par email lorsque votre compte sera activé."

      // Si la correspondance est partielle, ajouter une information supplémentaire
      if (matchDetails && matchDetails.matchType.startsWith("partial_")) {
        registrationMessage += " Une correspondance partielle a été trouvée avec vos informations."
      }

      // Créer l'utilisateur dans Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, formData.email, formData.password)
      const user = userCredential.user

      // Mettre à jour le profil utilisateur
      await updateProfile(user, {
        displayName: `${formData.firstName} ${formData.lastName}`,
      })

      // Dans handleSubmit, mettre à jour la création du document utilisateur
      await setDoc(doc(db(), "users", user.uid), {
        uid: user.uid,
        email: formData.email,
        displayName: `${formData.firstName} ${formData.lastName}`,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone || "",
        jobTitle: formData.jobTitle || "",
        clientCode: normalizeClientCode(formData.clientCode),
        siretLastDigits: normalizeSiret(formData.siretLastDigits),
        isConfirmed: isConfirmed,
        isAdmin: false,
        isActive: isConfirmed, // Définir isActive en fonction du statut de confirmation
        isPending: !isConfirmed, // Définir isPending comme l'opposé de isConfirmed
        groups: groups.length > 0 ? groups : ["default"], // Assurer qu'il y a au moins un groupe par défaut
        clientType: clientType || "default", // Assurer qu'il y a un type de client par défaut
        roles: ["viewer"], // Ajouter automatiquement le rôle "Lecteur" à tous les nouveaux utilisateurs
        createdAt: serverTimestamp(),
        // Ajouter la source d'inscription pour suivre les utilisateurs pré-enregistrés
        registrationSource: isConfirmed ? "pre-registered" : "manual",
        // Ajouter l'horodatage de la dernière connexion
        lastLoginAt: serverTimestamp(),
      })

      toast({
        title: isConfirmed ? "Inscription réussie" : "Inscription en attente de validation",
        description: registrationMessage,
      })

      if (isConfirmed) {
        // Rediriger vers le tableau de bord si l'utilisateur est confirmé
        setTimeout(() => {
          router.push("/dashboard")
        }, 2000)
      } else {
        // Rediriger vers une page d'attente de confirmation
        setTimeout(() => {
          router.push("/pending-confirmation")
        }, 2000)
      }
    } catch (error: any) {
      console.error("Erreur d'inscription:", error)

      let errorMessage = "Une erreur est survenue lors de l'inscription."

      if (error.code === "auth/email-already-in-use") {
        errorMessage = "Cette adresse email est déjà utilisée."
      }

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="clientCode" className="text-sm font-medium">
              Code client *
            </Label>
            <Input
              id="clientCode"
              name="clientCode"
              value={formData.clientCode}
              onChange={(e) => {
                // Accepter uniquement les chiffres
                const value = e.target.value.replace(/\D/g, "")
                // Limiter à 6 chiffres
                if (value.length <= 6) {
                  setFormData((prev) => ({ ...prev, clientCode: value }))
                }
              }}
              required
              maxLength={6}
              inputMode="numeric"
              className="bg-background"
            />
            <p className="text-xs text-muted-foreground">Votre code client ACR est composé de 6 chiffres</p>
            {errors.clientCode && <p className="text-sm text-red-500">{errors.clientCode}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="siretLastDigits" className="text-sm font-medium">
              5 derniers chiffres du SIRET *
            </Label>
            <Input
              id="siretLastDigits"
              name="siretLastDigits"
              value={formData.siretLastDigits}
              onChange={(e) => {
                // Accepter uniquement les chiffres
                const value = e.target.value.replace(/\D/g, "")
                // Limiter à 5 chiffres
                if (value.length <= 5) {
                  setFormData((prev) => ({ ...prev, siretLastDigits: value }))
                }
              }}
              required
              maxLength={5}
              inputMode="numeric"
              placeholder="00000"
              className="bg-background"
            />
            {errors.siretLastDigits && <p className="text-sm text-red-500">{errors.siretLastDigits}</p>}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">
              Prénom *
            </Label>
            <Input
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              required
              className="bg-background"
            />
            {errors.firstName && <p className="text-sm text-red-500">{errors.firstName}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium">
              Nom *
            </Label>
            <Input
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              required
              className="bg-background"
            />
            {errors.lastName && <p className="text-sm text-red-500">{errors.lastName}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">
            Adresse e-mail *
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="pl-10 bg-background"
            />
          </div>
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone" className="text-sm font-medium">
              Numéro de téléphone
            </Label>
            <Input id="phone" name="phone" value={formData.phone} onChange={handleChange} className="bg-background" />
            {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="jobTitle" className="text-sm font-medium">
              Fonction
            </Label>
            <Input
              id="jobTitle"
              name="jobTitle"
              value={formData.jobTitle}
              onChange={handleChange}
              className="bg-background"
            />
            {errors.jobTitle && <p className="text-sm text-red-500">{errors.jobTitle}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">
            Mot de passe *
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleChange}
              required
              className="pl-10 bg-background"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-sm font-medium">
            Confirmation du mot de passe *
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              className="pl-10 bg-background"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword}</p>}
        </div>

        <div className="flex items-start space-x-2">
          <Checkbox
            id="acceptTerms"
            name="acceptTerms"
            checked={formData.acceptTerms}
            onCheckedChange={(checked) => {
              setFormData((prev) => ({
                ...prev,
                acceptTerms: checked === true,
              }))
              if (errors.acceptTerms) {
                setErrors((prev) => ({
                  ...prev,
                  acceptTerms: undefined,
                }))
              }
            }}
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor="acceptTerms"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              J'accepte les{" "}
              <button
                type="button"
                className="text-primary hover:underline transition-colors"
                onClick={() => setShowTerms(true)}
              >
                conditions d'utilisation
              </button>{" "}
              et la{" "}
              <button
                type="button"
                className="text-primary hover:underline transition-colors"
                onClick={() => setShowPrivacy(true)}
              >
                politique de confidentialité
              </button>
            </label>
            {errors.acceptTerms && <p className="text-sm text-red-500">{errors.acceptTerms}</p>}
          </div>
        </div>

        <Button type="submit" className="w-full font-medium transition-all" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Inscription en cours...
            </>
          ) : (
            "S'inscrire"
          )}
        </Button>
      </form>

      {/* Modal pour les conditions d'utilisation */}
      <Dialog open={showTerms} onOpenChange={setShowTerms}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Conditions d'utilisation</DialogTitle>
            <DialogDescription>Veuillez lire attentivement les conditions d'utilisation ci-dessous.</DialogDescription>
          </DialogHeader>
          <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: termsContent || "Chargement..." }} />
        </DialogContent>
      </Dialog>

      {/* Modal pour la politique de confidentialité */}
      <Dialog open={showPrivacy} onOpenChange={setShowPrivacy}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Politique de confidentialité</DialogTitle>
            <DialogDescription>
              Veuillez lire attentivement notre politique de confidentialité ci-dessous.
            </DialogDescription>
          </DialogHeader>
          <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: privacyContent || "Chargement..." }} />
        </DialogContent>
      </Dialog>
    </>
  )
}
