/**
 * Service de préchargement des données fréquemment utilisées
 * Ce service permet de précharger les données essentielles en arrière-plan
 * pour accélérer l'affichage des pages et permettre une utilisation hors ligne
 */

import { preloadCollection, preloadDocument } from "@/lib/firestore-cache"
import { CACHE_DURATIONS } from "@/lib/cache-service"

/**
 * Précharge les pages HTML dans le service worker
 * @param urls Liste des URLs à précharger
 */
export async function cachePages(urls: string[]): Promise<boolean> {
  if (!urls || urls.length === 0) return false

  try {
    if (!navigator.serviceWorker || !navigator.serviceWorker.controller) {
      console.warn("Service Worker non disponible pour le préchargement des pages")
      return false
    }

    // Créer un canal de communication
    const messageChannel = new MessageChannel()

    // Promesse pour attendre la confirmation
    const result = await new Promise<boolean>((resolve) => {
      // Définir un timeout pour éviter les blocages
      const timeout = setTimeout(() => {
        console.warn("Timeout lors du préchargement des pages")
        resolve(false)
      }, 30000) // 30 secondes de timeout

      messageChannel.port1.onmessage = (event) => {
        clearTimeout(timeout)
        resolve(event.data.success === true)
      }

      // Envoyer les URLs au service worker
      navigator.serviceWorker.controller.postMessage(
        {
          type: "CACHE_PAGES",
          urls: urls,
        },
        [messageChannel.port2],
      )
    })

    return result
  } catch (error) {
    console.error("Erreur lors du préchargement des pages:", error)
    return false
  }
}

/**
 * Précharge les endpoints API dans le service worker
 * @param urls Liste des URLs d'API à précharger
 */
export async function cacheAPI(urls: string[]): Promise<boolean> {
  if (!urls || urls.length === 0) return false

  try {
    if (!navigator.serviceWorker || !navigator.serviceWorker.controller) {
      console.warn("Service Worker non disponible pour le préchargement des API")
      return false
    }

    // Créer un canal de communication
    const messageChannel = new MessageChannel()

    // Promesse pour attendre la confirmation
    const result = await new Promise<boolean>((resolve) => {
      // Définir un timeout pour éviter les blocages
      const timeout = setTimeout(() => {
        console.warn("Timeout lors du préchargement des API")
        resolve(false)
      }, 30000) // 30 secondes de timeout

      messageChannel.port1.onmessage = (event) => {
        clearTimeout(timeout)
        resolve(event.data.success === true)
      }

      // Envoyer les URLs au service worker
      navigator.serviceWorker.controller.postMessage(
        {
          type: "CACHE_API",
          urls: urls,
        },
        [messageChannel.port2],
      )
    })

    return result
  } catch (error) {
    console.error("Erreur lors du préchargement des API:", error)
    return false
  }
}

/**
 * Précharge les données essentielles pour un utilisateur
 * @param userId ID de l'utilisateur
 */
export async function preloadEssentialData(userId: string): Promise<void> {
  if (!userId) return

  try {
    console.log("Préchargement des données essentielles pour l'utilisateur", userId)

    // Précharger les données utilisateur
    await preloadDocument("users", userId, {
      duration: CACHE_DURATIONS.MEDIUM,
    })

    // Précharger les groupes de l'utilisateur
    // Note: Normalement, ces données sont déjà dans le document utilisateur,
    // mais on les précharge quand même pour être sûr
    await preloadDocument("userGroups", userId, {
      duration: CACHE_DURATIONS.LONG,
    })

    // Précharger les favoris de l'utilisateur
    await preloadDocument("userFavorites", userId, {
      duration: CACHE_DURATIONS.MEDIUM,
    })

    // Précharger les éléments de menu
    await preloadCollection(
      "menuItems",
      {
        where: [["isActive", "==", true]],
        orderBy: [["displayOrder", "asc"]],
      },
      {
        duration: CACHE_DURATIONS.LONG,
      },
    )

    console.log("Préchargement des données essentielles terminé")
  } catch (error) {
    console.error("Erreur lors du préchargement des données essentielles:", error)
  }
}

/**
 * Précharge les actualités pour un utilisateur
 * @param userId ID de l'utilisateur
 * @param userGroups Groupes de l'utilisateur
 */
export async function preloadNews(userId: string, userGroups: string[] = []): Promise<void> {
  if (!userId) return

  try {
    console.log("Préchargement des actualités")

    // Précharger toutes les actualités publiées
    const newsData = await preloadCollection(
      "news",
      {
        where: [["isPublished", "==", true]],
        orderBy: [["createdAt", "desc"]],
        limit: 20,
      },
      {
        duration: CACHE_DURATIONS.MEDIUM,
      },
    )

    // Précharger les images des actualités
    const imageUrls: string[] = []
    const thumbnailUrls: string[] = []
    const contentImageUrls: string[] = []

    // Extraire les URLs des images des actualités
    if (newsData && Array.isArray(newsData)) {
      newsData.forEach((item) => {
        // Ajouter l'image principale (thumbnail) si elle existe
        if (item.imageUrl) {
          console.log(`Ajout de l'image principale: ${item.imageUrl}`)
          thumbnailUrls.push(item.imageUrl)
          imageUrls.push(item.imageUrl)
        }

        // Extraire également les images du contenu (si elles sont dans le HTML)
        if (item.content) {
          const imgRegex = /<img[^>]+src="([^">]+)"/g
          let match
          while ((match = imgRegex.exec(item.content)) !== null) {
            if (match[1]) {
              console.log(`Ajout d'image de contenu: ${match[1]}`)
              contentImageUrls.push(match[1])
              imageUrls.push(match[1])
            }
          }
        }
      })
    }

    // Précharger les images principales (thumbnails)
    if (thumbnailUrls.length > 0) {
      console.log(`Préchargement de ${thumbnailUrls.length} images principales d'actualités`)
      try {
        const { cacheImages } = await import("@/app/sw-register")
        await cacheImages(thumbnailUrls)
        console.log("Préchargement des images principales terminé")
      } catch (error) {
        console.error("Erreur lors du préchargement des images principales d'actualités:", error)
      }
    }

    // Précharger les images de contenu
    if (contentImageUrls.length > 0) {
      console.log(`Préchargement de ${contentImageUrls.length} images de contenu d'actualités`)
      try {
        const { cacheImages } = await import("@/app/sw-register")
        await cacheImages(contentImageUrls)
        console.log("Préchargement des images de contenu terminé")
      } catch (error) {
        console.error("Erreur lors du préchargement des images de contenu d'actualités:", error)
      }
    }

    // Précharger les pages d'actualités individuelles
    if (newsData && Array.isArray(newsData)) {
      const newsUrls = newsData.map((item) => `/dashboard/news/${item.id}`)
      console.log(`Préchargement de ${newsUrls.length} pages d'actualités individuelles`)
      try {
        const { cachePages } = await import("@/app/sw-register")
        await cachePages(newsUrls)
        console.log("Préchargement des pages d'actualités individuelles terminé")
      } catch (error) {
        console.error("Erreur lors du préchargement des pages d'actualités individuelles:", error)
      }
    }

    console.log("Préchargement des actualités terminé")
  } catch (error) {
    console.error("Erreur lors du préchargement des actualités:", error)
  }
}

/**
 * Précharge les pages statiques
 */
export async function preloadStaticPages(): Promise<void> {
  try {
    console.log("Préchargement des pages statiques")

    // Précharger les pages publiées
    const pagesData = await preloadCollection(
      "menuItems",
      {
        where: [["isPublished", "==", true]],
        orderBy: [["updatedAt", "desc"]],
      },
      {
        duration: CACHE_DURATIONS.LONG,
      },
    )

    // Précharger les images des pages
    const imageUrls: string[] = []
    const menuIconUrls: string[] = []

    // Extraire les URLs des images des pages et des icônes de menu
    if (pagesData && Array.isArray(pagesData)) {
      pagesData.forEach((item) => {
        // Ajouter l'icône du menu si elle existe
        if (item.iconUrl) {
          console.log(`Ajout de l'icône de menu: ${item.iconUrl}`)
          menuIconUrls.push(item.iconUrl)
        }

        // Extraire les images du contenu (si elles sont dans le HTML)
        if (item.content) {
          const imgRegex = /<img[^>]+src="([^">]+)"/g
          let match
          while ((match = imgRegex.exec(item.content)) !== null) {
            if (match[1]) {
              imageUrls.push(match[1])
            }
          }
        }
      })
    }

    // Précharger les images de contenu
    if (imageUrls.length > 0) {
      console.log(`Préchargement de ${imageUrls.length} images de contenu de pages`)
      try {
        const { cacheImages } = await import("@/app/sw-register")
        await cacheImages(imageUrls)
      } catch (error) {
        console.error("Erreur lors du préchargement des images de contenu de pages:", error)
      }
    }

    // Précharger les icônes de menu
    if (menuIconUrls.length > 0) {
      console.log(`Préchargement de ${menuIconUrls.length} icônes de menu`)
      try {
        const { cacheImages } = await import("@/app/sw-register")
        await cacheImages(menuIconUrls)
      } catch (error) {
        console.error("Erreur lors du préchargement des icônes de menu:", error)
      }
    }

    // Précharger les pages individuelles
    if (pagesData && Array.isArray(pagesData)) {
      const pageUrls = pagesData.map((item) => `/dashboard/pages/${item.slug}`)
      console.log(`Préchargement de ${pageUrls.length} pages individuelles`)
      try {
        const { cachePages } = await import("@/app/sw-register")
        await cachePages(pageUrls)
        console.log("Préchargement des pages individuelles terminé")
      } catch (error) {
        console.error("Erreur lors du préchargement des pages individuelles:", error)
      }
    }

    console.log("Préchargement des pages statiques terminé")
  } catch (error) {
    console.error("Erreur lors du préchargement des pages statiques:", error)
  }
}

/**
 * Précharge les ressources importantes (images, etc.)
 */
export async function preloadResources(): Promise<void> {
  try {
    console.log("Préchargement des ressources importantes")

    // Liste des images importantes à précharger
    const importantImages = [
      // Logos et icônes
      "/logo-acr-direct.png",
      "/android-chrome-192x192.png",
      "/android-chrome-512x512.png",
      "/apple-touch-icon.png",
      "/favicon-32x32.png",
      "/favicon-16x16.png",
      "/favicon.ico",
      "/placeholder.svg",
      "/logo-acr-direct-dark.png", // Version sombre du logo
      "/logo-acr-direct-light.png", // Version claire du logo

      // Images d'interface
      "/images/offline.svg",
      "/images/error.svg",
      "/images/empty.svg",
      "/images/loading.svg",

      // Images de fallback
      "/images/placeholder-news.jpg",
      "/images/placeholder-profile.jpg",
      "/images/placeholder-icon.png",

      // Images de la carte de France (pour la page Contact commercial)
      "/images/france-map.svg",
      "/images/map-pin.svg",
    ]

    // Précharger les images
    try {
      const { cacheImages } = await import("@/app/sw-register")
      await cacheImages(importantImages)
      console.log(`${importantImages.length} images importantes mises en cache`)
    } catch (error) {
      console.error("Erreur lors du préchargement des images:", error)

      // Fallback si le service worker n'est pas disponible
      importantImages.forEach((src) => {
        const img = new Image()
        img.src = src
      })
    }

    // Précharger les polices importantes
    const importantFonts = ["/fonts/inter-var.woff2", "/fonts/inter-var-latin.woff2"]

    try {
      // Précharger les polices via le service worker
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        const messageChannel = new MessageChannel()

        navigator.serviceWorker.controller.postMessage(
          {
            type: "CACHE_FONTS",
            urls: importantFonts,
          },
          [messageChannel.port2],
        )
      }
    } catch (error) {
      console.error("Erreur lors du préchargement des polices:", error)
    }

    console.log("Préchargement des ressources importantes terminé")
  } catch (error) {
    console.error("Erreur lors du préchargement des ressources importantes:", error)
  }
}

/**
 * Précharge toutes les données en arrière-plan
 * @param userId ID de l'utilisateur
 * @param userGroups Groupes de l'utilisateur
 * @returns Promise<boolean> - true si le préchargement a réussi, false sinon
 */
export async function preloadAllData(userId: string, userGroups: string[] = []): Promise<boolean> {
  if (!userId) return false

  try {
    console.log("Démarrage du préchargement complet des données")
    let success = true

    // Précharger les données dans l'ordre de priorité
    await preloadEssentialData(userId)
    await preloadResources()

    // Précharger le reste en parallèle
    await Promise.all([preloadNews(userId, userGroups), preloadStaticPages()])

    // Précharger les pages importantes
    const importantPages = ["/", "/dashboard", "/profile", "/news", "/pages", "/offline"]
    const pagesResult = await cachePages(importantPages)
    if (!pagesResult) {
      console.warn("Échec du préchargement des pages importantes")
      success = false
    }

    // Précharger les API importantes
    const importantAPIs = ["/api/news", "/api/pages", "/api/menu", "/api/user/profile"]
    const apisResult = await cacheAPI(importantAPIs)
    if (!apisResult) {
      console.warn("Échec du préchargement des API importantes")
      success = false
    }

    // Précharger toutes les ressources en une seule fois via le service worker
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
      try {
        // Créer un canal de communication
        const messageChannel = new MessageChannel()

        // Promesse pour attendre la confirmation
        const preloadResult = await new Promise<boolean>((resolve) => {
          // Définir un timeout pour éviter les blocages
          const timeout = setTimeout(() => {
            console.warn("Timeout lors du préchargement complet")
            resolve(false)
          }, 60000) // 60 secondes de timeout

          messageChannel.port1.onmessage = (event) => {
            clearTimeout(timeout)

            // Si c'est un message de statut, l'afficher
            if (event.data.type === "PRELOAD_STATUS") {
              console.log(
                `Préchargement en cours: ${event.data.progress}% (${event.data.cachedResources}/${event.data.totalResources})`,
              )
              return
            }

            resolve(event.data.success === true)
          }

          // Collecter toutes les images importantes
          const allImages = [
            // Logos et icônes
            "/logo-acr-direct.png",
            "/android-chrome-192x192.png",
            "/android-chrome-512x512.png",
            "/apple-touch-icon.png",
            "/favicon-32x32.png",
            "/favicon-16x16.png",
            "/favicon.ico",
            "/placeholder.svg",
            "/logo-acr-direct-dark.png", // Version sombre du logo
            "/logo-acr-direct-light.png", // Version claire du logo

            // Images d'interface
            "/images/offline.svg",
            "/images/error.svg",
            "/images/empty.svg",
            "/images/loading.svg",

            // Images de fallback
            "/images/placeholder-news.jpg",
            "/images/placeholder-profile.jpg",
            "/images/placeholder-icon.png",

            // Images de la carte de France (pour la page Contact commercial)
            "/images/france-map.svg",
            "/images/map-pin.svg",
          ]

          // Envoyer la demande de préchargement complet au service worker
          navigator.serviceWorker.controller.postMessage(
            {
              type: "PRELOAD_ALL",
              pages: importantPages,
              apis: importantAPIs,
              images: allImages,
            },
            [messageChannel.port2],
          )
        })

        if (!preloadResult) {
          console.warn("Échec du préchargement complet via le service worker")
          success = false
        }
      } catch (error) {
        console.error("Erreur lors du préchargement complet via le service worker:", error)
        success = false
      }
    }

    console.log(`Préchargement complet des données terminé (${success ? "succès" : "avec des erreurs"})`)
    return success
  } catch (error) {
    console.error("Erreur lors du préchargement complet des données:", error)
    return false
  }
}

/**
 * Précharge les données pour une utilisation hors ligne
 * Cette fonction est plus complète que preloadAllData et est destinée
 * à être appelée explicitement par l'utilisateur pour préparer
 * l'application à une utilisation hors ligne
 *
 * @param userId ID de l'utilisateur
 * @param userGroups Groupes de l'utilisateur
 * @param progressCallback Fonction de callback pour suivre la progression
 * @returns Promise<boolean> - true si le préchargement a réussi, false sinon
 */
export async function preloadForOffline(
  userId: string,
  userGroups: string[] = [],
  progressCallback?: (progress: number, message: string) => void,
): Promise<boolean> {
  if (!userId) return false

  try {
    // Fonction pour mettre à jour la progression
    const updateProgress = (progress: number, message: string) => {
      console.log(`[Préchargement Offline] ${message} (${progress}%)`)
      if (progressCallback) {
        progressCallback(progress, message)
      }
    }

    updateProgress(0, "Démarrage du préchargement pour le mode hors ligne")

    // Étape 1: Forcer la persistance d'authentification
    updateProgress(5, "Forçage de la persistance d'authentification")
    if (typeof window !== "undefined" && window.forceMaximumPersistence) {
      await window.forceMaximumPersistence()
    }

    // Étape 2: Précharger les données essentielles
    updateProgress(10, "Préchargement des données essentielles")
    await preloadEssentialData(userId)

    // Étape 3: Précharger les ressources
    updateProgress(20, "Préchargement des ressources")
    await preloadResources()

    // Étape 4: Précharger les actualités et les pages statiques
    updateProgress(30, "Préchargement des actualités et des pages statiques")
    await Promise.all([preloadNews(userId, userGroups), preloadStaticPages()])

    // Étape 5: Précharger les pages importantes
    updateProgress(50, "Préchargement des pages importantes")
    const importantPages = ["/", "/dashboard", "/profile", "/news", "/pages", "/offline"]
    await cachePages(importantPages)

    // Étape 6: Précharger les API importantes
    updateProgress(70, "Préchargement des API importantes")
    const importantAPIs = ["/api/news", "/api/pages", "/api/menu", "/api/user/profile"]
    await cacheAPI(importantAPIs)

    // Étape 7: Précharger toutes les ressources en une seule fois via le service worker
    updateProgress(80, "Finalisation du préchargement")
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
      try {
        // Créer un canal de communication
        const messageChannel = new MessageChannel()

        // Promesse pour attendre la confirmation
        await new Promise<void>((resolve) => {
          // Définir un timeout pour éviter les blocages
          const timeout = setTimeout(() => {
            console.warn("Timeout lors du préchargement complet")
            resolve()
          }, 60000) // 60 secondes de timeout

          messageChannel.port1.onmessage = (event) => {
            // Si c'est un message de statut, mettre à jour la progression
            if (event.data.type === "PRELOAD_STATUS") {
              const progress = event.data.progress || 0
              updateProgress(
                80 + Math.floor(progress * 0.2),
                `Finalisation du préchargement (${event.data.cachedResources}/${event.data.totalResources})`,
              )
              return
            }

            clearTimeout(timeout)
            resolve()
          }

          // Collecter toutes les images importantes
          const allImages = [
            // Logos et icônes
            "/logo-acr-direct.png",
            "/android-chrome-192x192.png",
            "/android-chrome-512x512.png",
            "/apple-touch-icon.png",
            "/favicon-32x32.png",
            "/favicon-16x16.png",
            "/favicon.ico",
            "/placeholder.svg",
            "/logo-acr-direct-dark.png", // Version sombre du logo
            "/logo-acr-direct-light.png", // Version claire du logo

            // Images d'interface
            "/images/offline.svg",
            "/images/error.svg",
            "/images/empty.svg",
            "/images/loading.svg",

            // Images de fallback
            "/images/placeholder-news.jpg",
            "/images/placeholder-profile.jpg",
            "/images/placeholder-icon.png",

            // Images de la carte de France (pour la page Contact commercial)
            "/images/france-map.svg",
            "/images/map-pin.svg",
          ]

          // Envoyer la demande de préchargement complet au service worker
          navigator.serviceWorker.controller.postMessage(
            {
              type: "PRELOAD_ALL",
              pages: importantPages,
              apis: importantAPIs,
              images: allImages,
            },
            [messageChannel.port2],
          )
        })
      } catch (error) {
        console.error("Erreur lors du préchargement complet via le service worker:", error)
      }
    }

    updateProgress(100, "Préchargement pour le mode hors ligne terminé")

    // Stocker la date du dernier préchargement
    localStorage.setItem("offline_last_preload", new Date().toISOString())

    return true
  } catch (error) {
    console.error("Erreur lors du préchargement pour le mode hors ligne:", error)
    return false
  }
}
