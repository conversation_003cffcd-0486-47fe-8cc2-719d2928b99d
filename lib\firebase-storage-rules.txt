rules_version = '2';
service firebase.storage {
 match /b/{bucket}/o {
  // Fonction pour vérifier si l'utilisateur est admin
  function isAdmin() {
    return request.auth != null && 
           firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
  }
  
  // Accès aux images d'actualités - IMPORTANT: Mise à jour pour permettre l'accès aux administrateurs
  match /news/{fileName} {
    allow read: if request.auth != null;
    allow write: if isAdmin();
  }
  
  // Accès aux icônes de pages
  match /page-icons/{fileName} {
    allow read: if request.auth != null;
    allow write: if isAdmin();
  }
  
  // Accès aux images de contenu
  match /content-images/{fileName} {
    allow read: if request.auth != null;
    allow write: if isAdmin();
  }

  // Accès aux images des commerciaux
  match /commerciaux/{fileName} {
    allow read: if request.auth != null;
    allow write: if isAdmin();
  }
  
  // Accès aux ressources (documents, PDF, etc.)
  match /resources/{resourceId}/{fileName} {
    allow read: if request.auth != null && 
                firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.targetGroups.hasAny(
                  firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.groups
                );
    allow write: if isAdmin();
  }
  
  // Accès aux avatars utilisateurs
  match /avatars/{userId} {
    allow read: if request.auth != null;
    allow write: if request.auth != null && request.auth.uid == userId;
  }
  
  // Accès aux fichiers publics
  match /public/{fileName} {
    allow read: if request.auth != null;
    allow write: if isAdmin();
  }
  
  // Règle par défaut - refuser tout autre accès
  match /{allPaths=**} {
    allow read, write: if false;
  }
}
}
