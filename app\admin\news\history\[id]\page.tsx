"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Clock, User, Info, FileText, ArrowRightLeft, RotateCcw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { getContentHistory } from "@/lib/history-utils"
import type { NewsVersion } from "@/lib/history-types"
import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ContentType } from "@/lib/history-types"
import { ScrollArea } from "@/components/ui/scroll-area"

interface NewsHistoryPageProps {
  params: {
    id: string
  }
}

export default function NewsHistoryPage({ params }: NewsHistoryPageProps) {
  const { id } = params
  const router = useRouter()
  const { toast } = useToast()
  const [versions, setVersions] = useState<NewsVersion[]>([])
  const [currentNews, setCurrentNews] = useState<{ title: string; id: string } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchNewsAndHistory = async () => {
      try {
        setIsLoading(true)

        // Récupérer les informations de l'article actuel
        const newsDoc = await getDoc(doc(db(), "news", id))
        if (!newsDoc.exists()) {
          toast({
            title: "Erreur",
            description: "Article introuvable",
            variant: "destructive",
          })
          router.push("/admin/news")
          return
        }

        const newsData = newsDoc.data()
        setCurrentNews({
          title: newsData.title,
          id: newsDoc.id,
        })

        // Récupérer l'historique des versions
        let history = await getContentHistory(ContentType.NEWS, id)
        history = history as NewsVersion[]

        // Add the current news item as the first version
        const currentVersion = {
          versionId: "current",
          createdAt: newsData.updatedAt || newsData.createdAt,
          createdBy: {
            displayName: "Current",
            uid: "current",
          },
          description: "Current Published Version",
          data: newsData,
        }

        setVersions([currentVersion as any, ...history])
      } catch (error) {
        console.error("Erreur lors du chargement de l'historique:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger l'historique des versions",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchNewsAndHistory()
  }, [id, router, toast])

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "Date inconnue"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return format(date, "d MMMM yyyy à HH:mm", { locale: fr })
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" onClick={() => router.push(`/admin/news/edit/${id}`)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour à l'édition
        </Button>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Historique des versions</h1>
          {currentNews && (
            <p className="text-muted-foreground">
              Article : <span className="font-medium">{currentNews.title}</span>
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push(`/admin/news/edit/${id}`)}>
            <FileText className="mr-2 h-4 w-4" />
            Éditer l'article
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      ) : versions.length === 0 ? (
        <Card>
          <CardContent className="py-10 text-center">
            <Info className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Aucun historique disponible</h3>
            <p className="text-muted-foreground mb-6">
              Aucune version antérieure n'a été enregistrée pour cet article.
            </p>
            <Button onClick={() => router.push(`/admin/news/edit/${id}`)}>Retourner à l'édition</Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Versions disponibles</CardTitle>
              <CardDescription>
                Historique des modifications de l'article. La version la plus récente est affichée en premier.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] w-full">
                <div className="space-y-4">
                  {versions.map((version, index) => (
                    <Card key={version.versionId} className="overflow-hidden">
                      <div className="border-l-4 border-blue-500 pl-4 py-4 pr-6">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium flex items-center">
                              Version du {formatDate(version.createdAt)}
                              {index === 0 && <Badge className="ml-2 bg-blue-500">Plus récente</Badge>}
                            </h3>
                            <div className="text-sm text-muted-foreground flex items-center mt-1">
                              <User className="h-3 w-3 mr-1" />
                              {version.createdBy.displayName}
                            </div>
                            {version.description && (
                              <div className="text-sm mt-2 flex items-start">
                                <Info className="h-3 w-3 mr-1 mt-0.5" />
                                <span>{version.description}</span>
                              </div>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => router.push(`/admin/news/history/${id}/view/${version.versionId}`)}
                            >
                              <FileText className="h-3.5 w-3.5 mr-1" />
                              Voir
                            </Button>
                            {index > 0 && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  router.push(`/admin/news/history/${id}/compare/current/${version.versionId}`)
                                }
                              >
                                <ArrowRightLeft className="h-3.5 w-3.5 mr-1" />
                                Comparer
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                              onClick={() => router.push(`/admin/news/history/${id}/restore/${version.versionId}`)}
                            >
                              <RotateCcw className="h-3.5 w-3.5 mr-1" />
                              Restaurer
                            </Button>
                          </div>
                        </div>
                        <div className="mt-2 text-sm">
                          <div className="flex items-center text-muted-foreground">
                            <Clock className="h-3 w-3 mr-1" />
                            <span>
                              {index === 0
                                ? "Version actuelle"
                                : `Créée il y a ${formatRelativeTime(version.createdAt)}`}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

// Fonction pour formater le temps relatif (ex: "il y a 3 jours")
function formatRelativeTime(timestamp: any): string {
  if (!timestamp) return "date inconnue"

  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSecs = Math.round(diffMs / 1000)
  const diffMins = Math.round(diffSecs / 60)
  const diffHours = Math.round(diffMins / 60)
  const diffDays = Math.round(diffHours / 24)
  const diffMonths = Math.round(diffDays / 30)

  if (diffSecs < 60) return `${diffSecs} secondes`
  if (diffMins < 60) return `${diffMins} minutes`
  if (diffHours < 24) return `${diffHours} heures`
  if (diffDays < 30) return `${diffDays} jours`
  return `${diffMonths} mois`
}
