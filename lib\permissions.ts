// Define permission types
export type ResourceType = "news" | "pages" | "users" | "groups" | "settings" | "import" | "commercials"
export type ActionType = "create" | "read" | "update" | "delete" | "manage" | "publish" | "assign"

// Permission format: action:resource
export type Permission = `${ActionType}:${ResourceType}` | "admin"

// Predefined permissions
export const PERMISSIONS: Record<string, Permission> = {
  // Admin permission (grants all access)
  ADMIN: "admin",

  // News permissions
  CREATE_NEWS: "create:news",
  READ_NEWS: "read:news",
  UPDATE_NEWS: "update:news",
  DELETE_NEWS: "delete:news",
  PUBLISH_NEWS: "publish:news",

  // Pages permissions
  CREATE_PAGES: "create:pages",
  READ_PAGES: "read:pages",
  UPDATE_PAGES: "update:pages",
  DELETE_PAGES: "delete:pages",
  PUBLISH_PAGES: "publish:pages",

  // User permissions
  CREATE_USERS: "create:users",
  READ_USERS: "read:users",
  UPDATE_USERS: "update:users",
  DELETE_USERS: "delete:users",

  // Group permissions
  CREATE_GROUPS: "create:groups",
  READ_GROUPS: "read:groups",
  UPDATE_GROUPS: "update:groups",
  DELETE_GROUPS: "delete:groups",
  ASSIGN_GROUPS: "assign:groups",

  // Settings permissions
  READ_SETTINGS: "read:settings",
  UPDATE_SETTINGS: "update:settings",

  // Import permissions
  MANAGE_IMPORT: "manage:import",

  // Commercials permissions
  READ_COMMERCIALS: "read:commercials",
  CREATE_COMMERCIALS: "create:commercials",
  UPDATE_COMMERCIALS: "update:commercials",
  DELETE_COMMERCIALS: "delete:commercials",
}

// Helper function to check if a user has a specific permission
export function hasPermission(userPermissions: Permission[], permission: Permission): boolean {
  // Admin permission grants access to everything
  if (userPermissions.includes("admin")) {
    return true
  }

  return userPermissions.includes(permission)
}

// Helper function to check if a user has any of the specified permissions
export function hasAnyPermission(userPermissions: Permission[], permissions: Permission[]): boolean {
  // Admin permission grants access to everything
  if (userPermissions.includes("admin")) {
    return true
  }

  return permissions.some((permission) => userPermissions.includes(permission))
}

// Helper function to check if a user has all of the specified permissions
export function hasAllPermissions(userPermissions: Permission[], permissions: Permission[]): boolean {
  // Admin permission grants access to everything
  if (userPermissions.includes("admin")) {
    return true
  }

  return permissions.every((permission) => userPermissions.includes(permission))
}
