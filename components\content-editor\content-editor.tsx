"use client"

import { useState, useRef, useCallback, lazy, Suspense } from "react"
import { use<PERSON><PERSON><PERSON>, EditorContent, BubbleMenu } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Placeholder from "@tiptap/extension-placeholder"
import Link from "@tiptap/extension-link"
import TextAlign from "@tiptap/extension-text-align"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { storage } from "@/lib/firebase"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { CustomHtmlExtension } from "./extensions/custom-html-extension"
import { HardBreak } from "@tiptap/extension-hard-break"
import { EditorToolbar } from "./editor-toolbar"
import { cn } from "@/lib/utils"
import { CustomImage } from "./extensions/custom-image-extension"

// Lazy load dialogs to improve initial load performance
const ImageDialog = lazy(() => import("./image-dialog").then((mod) => ({ default: mod.ImageDialog })))
const LinkDialog = lazy(() => import("./link-dialog").then((mod) => ({ default: mod.LinkDialog })))
const HtmlDialog = lazy(() => import("./html-dialog").then((mod) => ({ default: mod.HtmlDialog })))

interface ContentEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  minHeight?: string
  readOnly?: boolean
}

export function ContentEditor({
  value,
  onChange,
  placeholder = "Commencez à écrire votre contenu...",
  className,
  minHeight = "200px",
  readOnly = false,
}: ContentEditorProps) {
  const [showLinkDialog, setShowLinkDialog] = useState(false)
  const [showImageDialog, setShowImageDialog] = useState(false)
  const [showHtmlDialog, setShowHtmlDialog] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const linkSelection = useRef<Range | null>(null)

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Désactiver le HardBreak par défaut pour le remplacer par notre version personnalisée
        hardBreak: false,
        // Désactiver l'extension Image standard car nous utilisons notre version personnalisée
        image: false,
      }),
      // Utiliser notre extension d'image personnalisée
      CustomImage,
      // Configuration personnalisée pour préserver les sauts de ligne multiples
      HardBreak.configure({
        keepMarks: true,
        HTMLAttributes: {
          class: "my-custom-break",
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-blue-600 underline hover:text-blue-800",
          rel: "noopener noreferrer",
          target: "_blank",
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
        alignments: ["left", "center", "right"],
      }),
      CustomHtmlExtension,
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: "prose prose-sm sm:prose max-w-none focus:outline-none dark:prose-invert",
        style: `min-height: ${minHeight}`,
        role: "textbox",
        "aria-multiline": "true",
        "aria-label": "Content editor",
      },
      // Personnaliser le comportement de la touche Entrée
      handleKeyDown: (view, event) => {
        // Si l'utilisateur appuie sur Shift+Entrée, insérer un saut de ligne dur
        if (event.key === "Enter" && event.shiftKey) {
          view.dispatch(view.state.tr.replaceSelectionWith(view.state.schema.nodes.hardBreak.create()))
          return true
        }
        return false
      },
    },
    editable: !readOnly,
  })

  const handleLinkDialogOpen = useCallback(() => {
    // Store the current selection to use when the dialog is confirmed
    if (editor?.state.selection) {
      linkSelection.current = editor.state.selection
      setShowLinkDialog(true)
    }
  }, [editor])

  const handleLinkConfirm = useCallback(
    (url: string) => {
      if (editor && url) {
        // Restore the selection and set the link
        editor.commands.setLink({ href: url })
      }
      setShowLinkDialog(false)
    },
    [editor],
  )

  // Fonction améliorée pour gérer l'insertion d'images avec des attributs personnalisés
  const handleImageConfirm = useCallback(
    async (imageData: { file?: File; url?: string; alt: string; align: string; width: string }) => {
      if (!editor) return

      try {
        let imageUrl = ""

        if (imageData.file) {
          setIsUploading(true)
          // Upload image to Firebase Storage
          const imageRef = ref(storage(), `content-images/${Date.now()}_${imageData.file.name}`)
          await uploadBytes(imageRef, imageData.file)
          imageUrl = await getDownloadURL(imageRef)
        } else if (imageData.url) {
          imageUrl = imageData.url
        } else {
          return
        }

        // Créer le style CSS en fonction de l'alignement et de la largeur
        const style = getImageStyle(imageData.align, imageData.width)

        // Utiliser la commande setImage standard mais avec nos attributs personnalisés
        editor
          .chain()
          .focus()
          .setImage({
            src: imageUrl,
            alt: imageData.alt,
            width: imageData.width,
            "data-align": imageData.align,
            style: style,
          })
          .run()
      } catch (error) {
        console.error("Error handling image:", error)
      } finally {
        setIsUploading(false)
        setShowImageDialog(false)
      }
    },
    [editor],
  )

  // Fonction pour générer le style CSS d'une image
  const getImageStyle = (align: string, width: string) => {
    let style = `width: ${width}; max-width: 100%; height: auto;`

    if (align === "center") {
      style += "display: block; margin-left: auto; margin-right: auto;"
    } else if (align === "left") {
      style += "float: left; margin-right: 1em; margin-bottom: 0.5em;"
    } else if (align === "right") {
      style += "float: right; margin-left: 1em; margin-bottom: 0.5em;"
    }

    return style
  }

  const handleHtmlConfirm = useCallback(
    (htmlContent: string) => {
      if (editor) {
        editor.commands.insertContentAt(editor.state.selection.anchor, {
          type: "customHtml",
          attrs: { content: htmlContent },
        })
      }
      setShowHtmlDialog(false)
    },
    [editor],
  )

  // Fonction pour insérer un saut de ligne dur
  const insertHardBreak = useCallback(() => {
    if (editor) {
      editor.chain().focus().setHardBreak().run()
    }
  }, [editor])

  if (!editor) {
    return <Skeleton className="w-full h-64" aria-label="Loading editor..." />
  }

  return (
    <div
      className={cn("border rounded-md overflow-hidden", className)}
      aria-label="Rich text editor"
      role="application"
    >
      {!readOnly && (
        <EditorToolbar
          editor={editor}
          onLinkClick={handleLinkDialogOpen}
          onImageClick={() => setShowImageDialog(true)}
          onHtmlClick={() => setShowHtmlDialog(true)}
          onHardBreakClick={insertHardBreak}
        />
      )}

      <div className="p-4 bg-background">
        <EditorContent editor={editor} />
      </div>

      {editor && (
        <BubbleMenu
          editor={editor}
          tippyOptions={{ duration: 100 }}
          shouldShow={({ editor, view, state, oldState, from, to }) => {
            // Only show the bubble menu for text selections, not for nodes like images
            return !editor.isActive("image") && !state.selection.empty && from !== to
          }}
        >
          <div className="flex items-center bg-white border rounded-md shadow-md p-1">
            <Button
              size="sm"
              variant="ghost"
              className="h-8 px-2"
              onClick={() => editor.chain().focus().toggleBold().run()}
              data-active={editor.isActive("bold")}
              aria-pressed={editor.isActive("bold")}
              aria-label="Bold"
            >
              <span className={cn("font-bold", editor.isActive("bold") ? "text-primary" : "")}>B</span>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 px-2"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              data-active={editor.isActive("italic")}
              aria-pressed={editor.isActive("italic")}
              aria-label="Italic"
            >
              <span className={cn("italic", editor.isActive("italic") ? "text-primary" : "")}>I</span>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 px-2"
              onClick={handleLinkDialogOpen}
              data-active={editor.isActive("link")}
              aria-pressed={editor.isActive("link")}
              aria-label="Link"
            >
              <span className={cn("underline", editor.isActive("link") ? "text-primary" : "")}>Link</span>
            </Button>
          </div>
        </BubbleMenu>
      )}

      {/* Lazy load dialogs only when needed */}
      <Suspense fallback={null}>
        {showLinkDialog && (
          <Suspense fallback={<Skeleton className="w-full h-64" />}>
            <LinkDialog
              isOpen={showLinkDialog}
              onClose={() => setShowLinkDialog(false)}
              onConfirm={handleLinkConfirm}
              initialUrl={editor?.getAttributes("link").href || ""}
            />
          </Suspense>
        )}

        {showImageDialog && (
          <Suspense fallback={<Skeleton className="w-full h-64" />}>
            <ImageDialog
              isOpen={showImageDialog}
              onClose={() => setShowImageDialog(false)}
              onConfirm={handleImageConfirm}
              isUploading={isUploading}
            />
          </Suspense>
        )}

        {showHtmlDialog && (
          <Suspense fallback={<Skeleton className="w-full h-64" />}>
            <HtmlDialog
              isOpen={showHtmlDialog}
              onClose={() => setShowHtmlDialog(false)}
              onConfirm={handleHtmlConfirm}
            />
          </Suspense>
        )}
      </Suspense>
    </div>
  )
}
