"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Download, CheckCircle2, AlertTriangle, RefreshCw } from "lucide-react"
import { useAuth } from "@/lib/hooks/use-auth"

export function OfflinePreloader() {
  const { user } = useAuth()
  const [loading, setLoading] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(0)
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle")
  const [logs, setLogs] = useState<string[]>([])
  const [lastPreload, setLastPreload] = useState<string | null>(null)

  // Fonction pour ajouter un log
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs((prev) => [`[${timestamp}] ${message}`, ...prev])
  }

  // Fonction pour précharger les données pour le mode hors ligne
  const preloadOfflineData = async () => {
    try {
      setLoading(true)
      setStatus("loading")
      setProgress(0)
      addLog("Démarrage du préchargement des données pour le mode hors ligne...")

      // Vérifier si l'utilisateur est connecté
      if (!user) {
        addLog("Erreur: Vous devez être connecté pour précharger les données")
        setStatus("error")
        setLoading(false)
        return
      }

      // Vérifier si nous sommes en ligne
      if (!navigator.onLine) {
        addLog("Erreur: Vous devez être en ligne pour précharger les données")
        setStatus("error")
        setLoading(false)
        return
      }

      // Importer dynamiquement le service de préchargement
      const { preloadForOffline } = await import("@/lib/preload-service")

      // Précharger les données pour le mode hors ligne
      const success = await preloadForOffline(user.uid, user.groups || [], (progress, message) => {
        setProgress(progress)
        addLog(message)
      })

      if (success) {
        setProgress(100)
        addLog("Préchargement terminé avec succès")
        setStatus("success")

        // Stocker la date du dernier préchargement
        const now = new Date()
        setLastPreload(now.toLocaleString())
      } else {
        setProgress(100)
        addLog("Préchargement terminé avec des erreurs")
        setStatus("error")
      }
    } catch (error) {
      console.error("Erreur lors du préchargement des données:", error)
      addLog(`Erreur globale: ${error instanceof Error ? error.message : String(error)}`)
      setProgress(100)
      setStatus("error")
    } finally {
      setLoading(false)
    }
  }

  // Vérifier la date du dernier préchargement au montage du composant
  useEffect(() => {
    const lastPreloadDate = localStorage.getItem("offline_last_preload")
    if (lastPreloadDate) {
      try {
        const date = new Date(lastPreloadDate)
        setLastPreload(date.toLocaleString())
      } catch (e) {
        console.error("Erreur lors de la récupération de la date du dernier préchargement:", e)
      }
    }
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Download className="h-5 w-5 mr-2 text-blue-600" />
          Préchargement pour le mode hors ligne
        </CardTitle>
        <CardDescription>Préchargez les données essentielles pour une utilisation hors ligne</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {status === "success" && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Préchargement réussi</AlertTitle>
            <AlertDescription>
              Les données ont été préchargées avec succès. Vous pouvez maintenant utiliser l'application hors ligne.
            </AlertDescription>
          </Alert>
        )}

        {status === "error" && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Erreur de préchargement</AlertTitle>
            <AlertDescription>
              Une erreur est survenue lors du préchargement des données. Veuillez réessayer.
            </AlertDescription>
          </Alert>
        )}

        {loading && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Préchargement en cours...</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        <div className="text-sm">
          {lastPreload ? <p>Dernier préchargement: {lastPreload}</p> : <p>Aucun préchargement effectué</p>}

          {user ? (
            <p className="mt-1">Les données seront préchargées pour l'utilisateur: {user.email}</p>
          ) : (
            <p className="mt-1 text-amber-600">Vous devez être connecté pour précharger les données</p>
          )}
        </div>

        {logs.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Logs de préchargement:</h4>
            <div className="bg-muted p-2 rounded-md h-32 overflow-y-auto text-xs font-mono">
              {logs.map((log, index) => (
                <div key={index} className="border-b border-border/20 pb-1 mb-1 last:border-0">
                  {log}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button onClick={preloadOfflineData} disabled={loading || !user} className="w-full">
          {loading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Préchargement en cours...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Précharger les données
            </>
          )}
        </Button>

        {logs.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => {
              // Créer un objet contenant les logs
              const exportData = {
                timestamp: new Date().toISOString(),
                user: user ? { uid: user.uid, email: user.email } : null,
                logs,
                lastPreload,
                status,
                progress,
                isOnline: navigator.onLine,
                browserInfo: navigator.userAgent,
                screenSize: {
                  width: window.innerWidth,
                  height: window.innerHeight,
                },
              }

              // Convertir en JSON
              const jsonData = JSON.stringify(exportData, null, 2)

              // Créer un blob et un lien de téléchargement
              const blob = new Blob([jsonData], { type: "application/json" })
              const url = URL.createObjectURL(blob)
              const a = document.createElement("a")
              a.href = url
              a.download = `acr-direct-offline-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`
              document.body.appendChild(a)
              a.click()

              // Nettoyer
              setTimeout(() => {
                document.body.removeChild(a)
                URL.revokeObjectURL(url)
              }, 100)

              addLog("Logs exportés avec succès")
            }}
          >
            Exporter les logs
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
