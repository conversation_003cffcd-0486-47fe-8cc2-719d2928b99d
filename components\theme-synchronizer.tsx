"use client"

import { useEffect } from "react"
import { doc, onSnapshot, getFirestore } from "firebase/firestore"
import { db, app } from "@/lib/firebase"
import { applyTheme, defaultTheme } from "@/lib/theme-service"

/**
 * ThemeSynchronizer component listens for theme changes in Firestore
 * and applies them in real-time to ensure consistent theming across
 * the entire application for all users.
 */
export function ThemeSynchronizer() {
  useEffect(() => {
    // Only run on the client
    if (typeof window === "undefined") return

    let unsubscribe = () => {}

    try {
      // Ensure we're on the client side
      if (typeof window === "undefined") return

      // Try to get a direct Firestore instance as a fallback
      let firestore
      try {
        firestore = db()

        // Verify if the returned object is a valid Firestore instance
        if (!firestore || typeof firestore.collection !== "function" || typeof firestore.doc !== "function") {
          console.warn("Invalid Firestore instance from db(), trying direct initialization")
          firestore = getFirestore(app)
        }
      } catch (error) {
        console.warn("Error getting Firestore instance from db(), trying direct initialization", error)
        firestore = getFirestore(app)
      }

      // Set up a real-time listener for theme changes in Firestore
      unsubscribe = onSnapshot(
        doc(firestore, "settings", "site"),
        (doc) => {
          if (doc.exists()) {
            const siteData = doc.data()

            // Create a theme object from the Firestore data
            const theme = { ...defaultTheme }

            // Update with stored values if they exist - mode clair
            if (siteData.primary) theme.primary = siteData.primary
            if (siteData.secondary) theme.secondary = siteData.secondary
            if (siteData.accent) theme.accent = siteData.accent
            if (siteData.success) theme.success = siteData.success
            if (siteData.warning) theme.warning = siteData.warning
            if (siteData.error) theme.error = siteData.error
            if (siteData.info) theme.info = siteData.info
            if (siteData.background) theme.background = siteData.background
            if (siteData.foreground) theme.foreground = siteData.foreground
            if (siteData.muted) theme.muted = siteData.muted
            if (siteData.mutedForeground) theme.mutedForeground = siteData.mutedForeground
            if (siteData.card) theme.card = siteData.card
            if (siteData.cardForeground) theme.cardForeground = siteData.cardForeground
            if (siteData.border) theme.border = siteData.border
            if (siteData.input) theme.input = siteData.input

            // Dark mode colors
            if (siteData.darkPrimary) theme.darkPrimary = siteData.darkPrimary
            if (siteData.darkSecondary) theme.darkSecondary = siteData.darkSecondary
            if (siteData.darkAccent) theme.darkAccent = siteData.darkAccent
            if (siteData.darkSuccess) theme.darkSuccess = siteData.darkSuccess
            if (siteData.darkWarning) theme.darkWarning = siteData.darkWarning
            if (siteData.darkError) theme.darkError = siteData.darkError
            if (siteData.darkInfo) theme.darkInfo = siteData.darkInfo
            if (siteData.darkBackground) theme.darkBackground = siteData.darkBackground
            if (siteData.darkForeground) theme.darkForeground = siteData.darkForeground
            if (siteData.darkMuted) theme.darkMuted = siteData.darkMuted
            if (siteData.darkMutedForeground) theme.darkMutedForeground = siteData.darkMutedForeground
            if (siteData.darkCard) theme.darkCard = siteData.darkCard
            if (siteData.darkCardForeground) theme.darkCardForeground = siteData.darkCardForeground
            if (siteData.darkBorder) theme.darkBorder = siteData.darkBorder
            if (siteData.darkInput) theme.darkInput = siteData.darkInput

            // Apply the updated theme
            const isDark = document.documentElement.classList.contains("dark")
            applyTheme(theme, isDark)

            // Update cache with timestamp to ensure freshness
            const themeWithTimestamp = {
              ...theme,
              _timestamp: Date.now(),
            }
            localStorage.setItem("cached_theme", JSON.stringify(themeWithTimestamp))
            sessionStorage.setItem("current_theme", JSON.stringify(themeWithTimestamp))

            console.log("Theme synchronized from Firestore")
          }
        },
        (error) => {
          console.error("Error listening to theme changes:", error)
        },
      )
    } catch (error) {
      console.error("Failed to set up Firestore theme listener:", error)

      // Try to load theme from cache as fallback
      try {
        const cachedTheme = localStorage.getItem("cached_theme")
        if (cachedTheme) {
          const theme = JSON.parse(cachedTheme)
          const isDark = document.documentElement.classList.contains("dark")
          applyTheme(theme, isDark)
          console.log("Applied theme from cache as fallback")
        }
      } catch (cacheError) {
        console.error("Failed to load theme from cache:", cacheError)
      }
    }

    // Clean up the listener when the component unmounts
    return () => {
      try {
        unsubscribe()
      } catch (error) {
        console.error("Error cleaning up Firestore listener:", error)
      }
    }
  }, [])

  // This component doesn't render anything
  return null
}
