"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import { preloadAllData } from "@/lib/preload-service"

/**
 * Composant qui précharge automatiquement les données en arrière-plan
 * après la connexion de l'utilisateur
 */
export function AutoPrefetch() {
  const { user } = useAuth()
  const [hasPrefetched, setHasPrefetched] = useState(false)

  useEffect(() => {
    // Ne rien faire si l'utilisateur n'est pas connecté ou si le préchargement a déjà été effectué
    if (!user || hasPrefetched) return

    // Vérifier si nous sommes en ligne
    if (!navigator.onLine) return

    // Vérifier si le préchargement a déjà été effectué récemment
    const lastPrefetch = localStorage.getItem("last_prefetch_timestamp")
    const now = Date.now()

    if (lastPrefetch) {
      const lastPrefetchTime = Number.parseInt(lastPrefetch, 10)
      const hoursSinceLastPrefetch = (now - lastPrefetchTime) / (1000 * 60 * 60)

      // Si le dernier préchargement date de moins de 4 heures, ne pas précharger à nouveau
      if (hoursSinceLastPrefetch < 4) {
        console.log("Préchargement automatique ignoré (dernier préchargement il y a moins de 4 heures)")
        setHasPrefetched(true)
        return
      }
    }

    // Attendre que l'application soit chargée avant de précharger les données
    const timer = setTimeout(() => {
      console.log("Démarrage du préchargement automatique des données...")

      preloadAllData(user.uid)
        .then(() => {
          console.log("Préchargement automatique terminé avec succès")
          // Stocker la date du dernier préchargement
          localStorage.setItem("last_prefetch_timestamp", now.toString())
          setHasPrefetched(true)
        })
        .catch((error) => {
          console.error("Erreur lors du préchargement automatique:", error)
        })
    }, 5000) // Attendre 5 secondes après le chargement de la page

    return () => clearTimeout(timer)
  }, [user, hasPrefetched])

  // Ce composant ne rend rien visuellement
  return null
}

export default AutoPrefetch
