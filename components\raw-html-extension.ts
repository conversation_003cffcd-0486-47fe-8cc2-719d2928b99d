import { Node, mergeAttributes } from "@tiptap/core"
import { ReactNodeViewRenderer } from "@tiptap/react"
import { RawHTMLComponent } from "./raw-html-component"

export const RawHTML = Node.create({
  name: "rawHtml",
  group: "block",
  content: "text*",
  parseHTML() {
    return [
      {
        tag: 'div[data-type="raw-html"]',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ["div", mergeAttributes(HTMLAttributes, { "data-type": "raw-html" }), 0]
  },
  addNodeView() {
    return ReactNodeViewRenderer(RawHTMLComponent)
  },
  addAttributes() {
    return {
      htmlContent: {
        default: "",
        parseHTML: (element) => element.getAttribute("data-html-content") || "",
        renderHTML: (attributes) => {
          return {
            "data-html-content": attributes.htmlContent,
          }
        },
      },
    }
  },
})
