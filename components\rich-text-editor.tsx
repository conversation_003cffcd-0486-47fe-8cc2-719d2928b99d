// Remplacer tout le contenu du fichier par une redirection vers notre nouvel éditeur
"use client"

import { ContentEditor } from "./content-editor/content-editor"

export default function RichTextEditor({
  value,
  onChange,
  placeholder,
}: {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}) {
  return <ContentEditor value={value} onChange={onChange} placeholder={placeholder} />
}
