"use client"

import { useAuth } from "@/components/auth-provider"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { auth } from "@/lib/firebase"
import { LogOut, LayoutDashboard, User, CheckCircle } from "lucide-react"
import Link from "next/link"

// Ajouter l'import pour getDoc et doc
import { getDoc, doc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useState, useEffect } from "react"

// Modifier le composant pour ajouter la vérification du rôle commercial
export function UserAccountNav() {
  const { user, isAdmin } = useAuth()
  const [hasCommercialRole, setHasCommercialRole] = useState(false)

  useEffect(() => {
    if (user) {
      // Vérifier si l'utilisateur a le rôle "commercial"
      const checkCommercialRole = async () => {
        try {
          const userDoc = await getDoc(doc(db(), "users", user.uid))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            const roles = userData.roles || []
            setHasCommercialRole(roles.includes("commercial"))
          }
        } catch (error) {
          console.error("Error checking commercial role:", error)
        }
      }

      checkCommercialRole()
    }
  }, [user])

  if (!user) {
    return null
  }

  const initials = user.email ? user.email.substring(0, 2).toUpperCase() : "UN"

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            {user.displayName && <p className="font-medium">{user.displayName}</p>}
            {user.email && <p className="w-[200px] truncate text-sm text-muted-foreground">{user.email}</p>}
          </div>
        </div>
        <DropdownMenuSeparator />

        {/* Ajouter le lien vers le profil commercial uniquement pour les utilisateurs avec le rôle "commercial" */}
        <DropdownMenuItem asChild>
          <Link href="/dashboard/commercial-profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            <span>Mon profil commercial</span>
            {hasCommercialRole && <CheckCircle className="ml-1 h-4 w-4 text-green-500" />}
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <button className="w-full cursor-pointer text-left" onClick={() => auth.signOut()}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Déconnexion</span>
          </button>
        </DropdownMenuItem>

        {isAdmin && (
          <>
            <div className="px-2 pt-2 pb-1">
              <Link
                href="/admin"
                className="flex w-full items-center justify-center gap-2 rounded-md border border-primary p-2 text-sm font-medium text-primary hover:bg-primary hover:text-primary-foreground transition-colors"
              >
                <LayoutDashboard className="h-4 w-4" />
                Administration
              </Link>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
