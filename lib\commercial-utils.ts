import { collection, doc, getDocs, getDoc, setDoc, deleteDoc, query, where } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"
import { db, storage } from "@/lib/firebase"
import type { CommercialContact } from "@/lib/commercial-types"

const COLLECTION_NAME = "commerciaux"

// Get all commercial contacts
export async function getCommercialContacts(): Promise<CommercialContact[]> {
  try {
    const querySnapshot = await getDocs(collection(db(), COLLECTION_NAME))
    return querySnapshot.docs.map(
      (doc) =>
        ({
          id: doc.id,
          ...doc.data(),
        }) as CommercialContact,
    )
  } catch (error) {
    console.error("Error getting commercial contacts:", error)
    throw error
  }
}

// Get a single commercial contact by ID
export async function getCommercialContact(id: string): Promise<CommercialContact | null> {
  try {
    const docRef = doc(db(), COLLECTION_NAME, id)
    const docSnap = await getDoc(docRef)

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data(),
      } as CommercialContact
    }

    return null
  } catch (error) {
    console.error("Error getting commercial contact:", error)
    throw error
  }
}

// Get commercial contact by department code
export async function getCommercialContactByDepartment(departmentCode: string): Promise<CommercialContact | null> {
  try {
    const q = query(collection(db(), COLLECTION_NAME), where("departments", "array-contains", departmentCode))

    const querySnapshot = await getDocs(q)

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0]
      return {
        id: doc.id,
        ...doc.data(),
      } as CommercialContact
    }

    return null
  } catch (error) {
    console.error("Error getting commercial contact by department:", error)
    throw error
  }
}

// Get commercial contact by user ID
export async function getCommercialContactByUserId(userId: string): Promise<CommercialContact | null> {
  try {
    const q = query(collection(db(), COLLECTION_NAME), where("userId", "==", userId))

    const querySnapshot = await getDocs(q)

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0]
      return {
        id: doc.id,
        ...doc.data(),
      } as CommercialContact
    }

    return null
  } catch (error) {
    console.error("Error getting commercial contact by user ID:", error)
    throw error
  }
}

// Create or update a commercial contact
export async function saveCommercialContact(contact: CommercialContact): Promise<string> {
  try {
    const id = contact.id || doc(collection(db(), COLLECTION_NAME)).id
    const docRef = doc(db(), COLLECTION_NAME, id)

    // Remove the id field before saving to Firestore
    const { id: _, ...contactData } = contact

    await setDoc(docRef, contactData)
    return id
  } catch (error) {
    console.error("Error saving commercial contact:", error)
    throw error
  }
}

// Delete a commercial contact
export async function deleteCommercialContact(id: string): Promise<void> {
  try {
    // Get the contact to check if it has a photo
    const contact = await getCommercialContact(id)

    // Delete the document
    await deleteDoc(doc(db(), COLLECTION_NAME, id))

    // If the contact has a photo stored in Firebase Storage, delete it too
    if (contact && contact.photoURL && contact.photoURL.includes("firebasestorage")) {
      try {
        const photoRef = ref(storage(), contact.photoURL)
        await deleteObject(photoRef)
      } catch (photoError) {
        console.error("Error deleting photo:", photoError)
        // Continue even if photo deletion fails
      }
    }
  } catch (error) {
    console.error("Error deleting commercial contact:", error)
    throw error
  }
}

// Upload a photo to Firebase Storage
export async function uploadCommercialPhoto(file: File, contactId: string): Promise<string> {
  try {
    const fileExtension = file.name.split(".").pop()
    const fileName = `commercial_${contactId}_${Date.now()}.${fileExtension}`
    const storageRef = ref(storage(), `commerciaux/${fileName}`)

    await uploadBytes(storageRef, file)
    const downloadURL = await getDownloadURL(storageRef)

    return downloadURL
  } catch (error) {
    console.error("Error uploading commercial photo:", error)
    throw error
  }
}
