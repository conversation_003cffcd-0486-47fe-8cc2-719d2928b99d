"use client"

/**
 * Hook React pour utiliser les collections Firestore avec cache
 * Ce hook gère automatiquement la mise en cache et la récupération des données
 */

import { useState, useEffect, useCallback } from "react"
import type { DocumentData } from "firebase/firestore"
import { getCollectionWithCache, listenToCollectionWithCache, type QueryParams } from "@/lib/firestore-cache"
import { CACHE_DURATIONS, type CacheOptions } from "@/lib/cache-service"
import { useAuth } from "@/components/auth-provider"

interface UseCachedCollectionOptions extends CacheOptions {
  /** Écouter les changements en temps réel */
  listen?: boolean
  /** Recharger automatiquement les données lors des changements de dépendances */
  autoRefresh?: boolean
}

interface UseCachedCollectionResult<T> {
  /** Données de la collection */
  data: T[]
  /** Indique si les données sont en cours de chargement */
  loading: boolean
  /** Erreur éventuelle */
  error: Error | null
  /** Fonction pour rafraîchir les données */
  refresh: (forceRefresh?: boolean) => Promise<void>
  /** Indique si les données proviennent du cache */
  fromCache: boolean
}

/**
 * Hook pour utiliser une collection Firestore avec cache
 * @param path Chemin de la collection
 * @param queryParams Paramètres de requête (where, orderBy, limit)
 * @param options Options de cache et d'écoute
 * @param dependencies Dépendances pour le rechargement automatique
 * @returns Résultat contenant les données, l'état de chargement, etc.
 */
export function useCachedCollection<T = DocumentData>(
  path: string,
  queryParams: QueryParams = {},
  options: UseCachedCollectionOptions = {},
  dependencies: any[] = [],
): UseCachedCollectionResult<T> {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)
  const [fromCache, setFromCache] = useState<boolean>(false)
  const { user } = useAuth()

  // Options par défaut
  const { listen = false, autoRefresh = true, duration = CACHE_DURATIONS.MEDIUM, forceRefresh = false } = options

  // Fonction pour charger les données
  const loadData = useCallback(
    async (force = false) => {
      if (!path) {
        setLoading(false)
        return
      }

      setLoading(true)
      setError(null)

      try {
        const result = await getCollectionWithCache<T>(path, queryParams, {
          duration,
          forceRefresh: force || forceRefresh,
        })

        setData(result)
        setFromCache(!force && !forceRefresh)
        setLoading(false)
      } catch (err) {
        console.error(`Error fetching collection ${path}:`, err)
        setError(err as Error)
        setLoading(false)
      }
    },
    [path, JSON.stringify(queryParams), duration, forceRefresh],
  )

  // Fonction de rafraîchissement exposée
  const refresh = useCallback(
    async (force = true) => {
      await loadData(force)
    },
    [loadData],
  )

  // Effet pour le chargement initial et l'écoute des changements
  useEffect(() => {
    // Ne pas charger si le chemin est vide ou si l'utilisateur n'est pas connecté
    if (!path || !user) {
      setLoading(false)
      return () => {}
    }

    // Si on écoute les changements en temps réel
    if (listen) {
      setLoading(true)

      // Charger d'abord depuis le cache pour un affichage rapide
      getCollectionWithCache<T>(path, queryParams, { forceRefresh: false })
        .then((cachedData) => {
          setData(cachedData)
          setFromCache(true)
          setLoading(false)
        })
        .catch((err) => {
          console.error(`Error fetching cached collection ${path}:`, err)
        })

      // Puis configurer l'écouteur pour les mises à jour en temps réel
      const unsubscribe = listenToCollectionWithCache<T>(path, queryParams, (newData) => {
        setData(newData)
        setFromCache(false)
        setLoading(false)
      })

      return () => {
        unsubscribe()
      }
    }
    // Sinon, charger une seule fois
    else {
      loadData(forceRefresh)
      return () => {}
    }
  }, [path, JSON.stringify(queryParams), listen, user?.uid, ...(autoRefresh ? dependencies : [])])

  return {
    data,
    loading,
    error,
    refresh,
    fromCache,
  }
}
