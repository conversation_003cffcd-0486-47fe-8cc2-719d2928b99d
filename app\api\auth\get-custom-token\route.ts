import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getFirebaseAdminApp } from "@/lib/server-auth"

export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET(request: NextRequest) {
  try {
    // Récupérer le cookie de session
    const sessionCookie = cookies().get("session")?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: "Aucune session trouvée" }, { status: 401 })
    }

    // Vérifier la session avec Firebase Admin
    const admin = getFirebaseAdminApp()
    const decodedClaims = await admin.auth().verifySessionCookie(sessionCookie, true)

    if (!decodedClaims) {
      return NextResponse.json({ error: "Session invalide" }, { status: 401 })
    }

    // Générer un token personnalisé pour l'utilisateur
    const uid = decodedClaims.uid
    const customToken = await admin.auth().createCustomToken(uid)

    return NextResponse.json(
      { token: customToken },
      {
        status: 200,
        headers: {
          "Cache-Control": "no-store, max-age=0",
        },
      },
    )
  } catch (error) {
    console.error("Erreur lors de la génération du token personnalisé:", error)

    return NextResponse.json({ error: "Erreur lors de la génération du token personnalisé" }, { status: 500 })
  }
}
