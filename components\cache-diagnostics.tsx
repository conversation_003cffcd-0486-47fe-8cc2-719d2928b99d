"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, RefreshCw, Trash2, Download, Database, Info } from "lucide-react"
import { cacheService } from "@/lib/cache-service"
import { useAuth } from "@/components/auth-provider"
import { preloadAllData } from "@/lib/preload-service"

interface CacheStats {
  name: string
  count: number
  size: number
}

export function CacheDiagnostics() {
  const { user } = useAuth()
  const [stats, setStats] = useState<CacheStats[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isClearing, setIsClearing] = useState(false)
  const [isPrefetching, setIsPrefetching] = useState(false)
  const [message, setMessage] = useState<{ type: "success" | "error" | "info"; text: string } | null>(null)
  const [activeTab, setActiveTab] = useState("stats")

  // Charger les statistiques du cache
  const loadCacheStats = async () => {
    setIsLoading(true)
    try {
      const cacheStats = await cacheService.getStats()

      const formattedStats: CacheStats[] = Object.entries(cacheStats).map(([name, data]) => ({
        name,
        count: data.count,
        size: data.size,
      }))

      setStats(formattedStats)
    } catch (error) {
      console.error("Error loading cache stats:", error)
      setMessage({ type: "error", text: "Erreur lors du chargement des statistiques du cache" })
    } finally {
      setIsLoading(false)
    }
  }

  // Vider le cache
  const handleClearCache = async (storeType?: string) => {
    setIsClearing(true)
    try {
      if (storeType) {
        await cacheService.clear(storeType as any)
        setMessage({ type: "success", text: `Cache ${storeType} vidé avec succès` })
      } else {
        await cacheService.clearAll()
        setMessage({ type: "success", text: "Tous les caches ont été vidés avec succès" })
      }

      // Recharger les statistiques
      await loadCacheStats()
    } catch (error) {
      console.error("Error clearing cache:", error)
      setMessage({ type: "error", text: "Erreur lors du vidage du cache" })
    } finally {
      setIsClearing(false)
    }
  }

  // Précharger les données
  const handlePrefetch = async () => {
    if (!user) {
      setMessage({ type: "error", text: "Vous devez être connecté pour précharger les données" })
      return
    }

    setIsPrefetching(true)
    try {
      await preloadAllData(user.uid)
      setMessage({ type: "success", text: "Données préchargées avec succès" })

      // Recharger les statistiques
      await loadCacheStats()
    } catch (error) {
      console.error("Error prefetching data:", error)
      setMessage({ type: "error", text: "Erreur lors du préchargement des données" })
    } finally {
      setIsPrefetching(false)
    }
  }

  // Charger les statistiques au chargement du composant (côté client uniquement)
  useEffect(() => {
    // Vérifier que nous sommes côté client
    if (typeof window !== "undefined") {
      loadCacheStats()
    }
  }, [])

  // Effacer le message après 5 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  // Formater la taille en Ko ou Mo
  const formatSize = (bytes: number) => {
    if (bytes < 1024) {
      return `${bytes} octets`
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} Ko`
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} Mo`
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Diagnostic du Cache</CardTitle>
        <CardDescription>Visualisez et gérez le cache de l'application</CardDescription>
      </CardHeader>
      <CardContent>
        {message && (
          <Alert
            variant={message.type === "error" ? "destructive" : message.type === "info" ? "default" : "default"}
            className="mb-4"
          >
            <Info className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="stats" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="stats">Statistiques</TabsTrigger>
            <TabsTrigger value="prefetch">Préchargement</TabsTrigger>
            <TabsTrigger value="clear">Nettoyage</TabsTrigger>
          </TabsList>

          <TabsContent value="stats" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Statistiques du cache</h3>
              <Button variant="outline" size="sm" onClick={loadCacheStats} disabled={isLoading}>
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
                Actualiser
              </Button>
            </div>

            <div className="space-y-4">
              {stats.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  {isLoading ? "Chargement des statistiques..." : "Aucune donnée en cache"}
                </div>
              ) : (
                <div className="grid gap-4">
                  {stats.map((stat) => (
                    <div key={stat.name} className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <h4 className="font-medium">{stat.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {stat.count} élément{stat.count !== 1 ? "s" : ""}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatSize(stat.size)}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleClearCache(stat.name)}
                          className="text-xs"
                        >
                          Vider
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="prefetch">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Préchargement des données</h3>
                <p className="text-sm text-muted-foreground">
                  Préchargez les données essentielles pour améliorer les performances de l'application.
                </p>
                <Button onClick={handlePrefetch} disabled={isPrefetching || !user} className="mt-2">
                  {isPrefetching ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="mr-2 h-4 w-4" />
                  )}
                  Précharger les données
                </Button>
              </div>

              <Alert>
                <Database className="h-4 w-4" />
                <AlertTitle>À propos du préchargement</AlertTitle>
                <AlertDescription>
                  Le préchargement stocke les données fréquemment utilisées dans le cache local pour accélérer
                  l'affichage et réduire les appels à Firebase. Cela améliore les performances et permet l'utilisation
                  hors ligne.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="clear">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Vider le cache</h3>
                <p className="text-sm text-muted-foreground">
                  Videz le cache pour forcer le rechargement des ressources depuis le serveur.
                </p>
                <Button variant="destructive" onClick={() => handleClearCache()} disabled={isClearing} className="mt-2">
                  {isClearing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />}
                  Vider tout le cache
                </Button>
              </div>

              <Alert variant="warning">
                <AlertTitle>Attention</AlertTitle>
                <AlertDescription>
                  Vider le cache peut temporairement ralentir l'application car les données devront être rechargées
                  depuis le serveur. Utilisez cette option uniquement en cas de problème.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Le cache est géré par le service de cache centralisé et IndexedDB
        </p>
      </CardFooter>
    </Card>
  )
}
