import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Mail, Phone, MapPin } from "lucide-react"
import type { CommercialContact } from "@/lib/commercial-types"
import { getDepartmentName } from "@/lib/commercial-types"
import { Badge } from "@/components/ui/badge"

interface CommercialContactCardProps {
  contact: CommercialContact
  showDepartments?: boolean
}

export function CommercialContactCard({ contact, showDepartments = false }: CommercialContactCardProps) {
  const initials = contact.name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .substring(0, 2)

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-semibold">Contact commercial</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col sm:flex-row items-center gap-4">
        <Avatar className="h-24 w-24">
          {contact.photoURL ? (
            <AvatarImage src={contact.photoURL || "/placeholder.svg"} alt={contact.name} />
          ) : (
            <AvatarFallback className="text-2xl">{initials}</AvatarFallback>
          )}
        </Avatar>
        <div className="space-y-3 text-center sm:text-left w-full">
          <h3 className="text-lg font-medium">{contact.name}</h3>

          <div className="flex flex-col gap-3 mt-3 w-full">
            <a
              href={`tel:${contact.phone.replace(/\s/g, "")}`}
              className="flex items-center justify-center sm:justify-start gap-2 text-lg font-bold hover:text-primary transition-colors p-3 rounded-md hover:bg-primary/10 border border-primary/20"
            >
              <Phone className="h-6 w-6 text-primary" />
              <span>{contact.phone}</span>
            </a>

            <a
              href={`mailto:${contact.email}`}
              className="flex items-center justify-center sm:justify-start gap-2 text-sm hover:text-primary transition-colors p-2 rounded-md hover:bg-primary/10"
            >
              <Mail className="h-5 w-5 text-primary" />
              <span className="font-medium">{contact.email}</span>
            </a>
          </div>

          {showDepartments && contact.departments.length > 0 && (
            <div className="text-sm mt-4 pt-3 border-t border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-muted-foreground">Départements:</span>
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {contact.departments.map((dept) => (
                  <Badge key={dept} variant="outline" className="bg-primary/10">
                    {dept} - {getDepartmentName(dept)}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
