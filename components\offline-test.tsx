"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { Wifi, WifiOff, RefreshCw, Download } from "lucide-react"

export function OfflineTest() {
  const [isOnline, setIsOnline] = useState<boolean>(true)
  const [logs, setLogs] = useState<string[]>([])
  const [cacheStatus, setCacheStatus] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(0)
  const [testResults, setTestResults] = useState<Record<string, boolean | null>>({
    authentication: null,
    pages: null,
    images: null,
    api: null,
    localStorage: null,
    indexedDB: null,
  })

  // Fonction pour ajouter un log
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs((prev) => [`[${timestamp}] ${message}`, ...prev])
  }

  // Vérifier l'état de la connexion
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      addLog("Connexion réseau rétablie")
    }

    const handleOffline = () => {
      setIsOnline(false)
      addLog("Connexion réseau perdue")
    }

    // Initialiser l'état
    setIsOnline(navigator.onLine)
    addLog(`État initial de la connexion: ${navigator.onLine ? "En ligne" : "Hors ligne"}`)

    // Ajouter les écouteurs d'événements
    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  // Fonction pour vérifier l'état du cache
  const checkCacheStatus = async () => {
    try {
      setLoading(true)
      addLog("Vérification de l'état du cache...")

      // Vérifier les caches disponibles
      const cacheNames = await caches.keys()

      // Vérifier le service worker
      const swRegistration = await navigator.serviceWorker.getRegistration()

      // Vérifier le service worker d'authentification
      let authSwStatus = null
      if (typeof window !== "undefined" && window.checkAuthServiceWorker) {
        authSwStatus = await window.checkAuthServiceWorker()
      }

      // Vérifier IndexedDB
      let indexedDBAvailable = false
      try {
        indexedDBAvailable = !!window.indexedDB
      } catch (e) {
        console.error("Erreur lors de la vérification d'IndexedDB:", e)
      }

      setCacheStatus({
        cacheNames,
        serviceWorker: {
          registered: !!swRegistration,
          scope: swRegistration?.scope || null,
          active: !!swRegistration?.active,
          waiting: !!swRegistration?.waiting,
          installing: !!swRegistration?.installing,
        },
        authServiceWorker: authSwStatus,
        indexedDB: {
          available: indexedDBAvailable,
        },
      })

      addLog(`${cacheNames.length} cache(s) trouvé(s): ${cacheNames.join(", ")}`)
      addLog(`Service Worker: ${!!swRegistration ? "Enregistré" : "Non enregistré"}`)

      setLoading(false)
    } catch (error) {
      console.error("Erreur lors de la vérification de l'état du cache:", error)
      addLog(`Erreur: ${error instanceof Error ? error.message : String(error)}`)
      setLoading(false)
    }
  }

  // Fonction pour tester le fonctionnement hors ligne
  const runOfflineTests = async () => {
    try {
      setLoading(true)
      setProgress(0)
      addLog("Démarrage des tests de fonctionnement hors ligne...")

      // Réinitialiser les résultats
      setTestResults({
        authentication: null,
        pages: null,
        images: null,
        api: null,
        localStorage: null,
        indexedDB: null,
      })

      // Test 1: Vérifier le localStorage
      addLog("Test 1/6: Vérification du localStorage...")
      setProgress(10)
      try {
        localStorage.setItem("offline_test", Date.now().toString())
        const testValue = localStorage.getItem("offline_test")
        const result = !!testValue
        setTestResults((prev) => ({ ...prev, localStorage: result }))
        addLog(`Test localStorage: ${result ? "Réussi" : "Échoué"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, localStorage: false }))
        addLog(`Test localStorage échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Test 2: Vérifier IndexedDB
      addLog("Test 2/6: Vérification d'IndexedDB...")
      setProgress(25)
      try {
        if (!window.indexedDB) {
          throw new Error("IndexedDB non supporté")
        }

        const dbName = "offline_test_db"
        const request = window.indexedDB.open(dbName, 1)

        request.onupgradeneeded = (event) => {
          const db = request.result
          if (!db.objectStoreNames.contains("test_store")) {
            db.createObjectStore("test_store", { keyPath: "id" })
          }
        }

        const dbPromise = new Promise<boolean>((resolve, reject) => {
          request.onsuccess = (event) => {
            try {
              const db = request.result
              const transaction = db.transaction(["test_store"], "readwrite")
              const store = transaction.objectStore("test_store")

              store.put({ id: "test", value: "test_value", timestamp: Date.now() })

              transaction.oncomplete = () => {
                db.close()
                // Supprimer la base de données de test
                window.indexedDB.deleteDatabase(dbName)
                resolve(true)
              }

              transaction.onerror = (event) => {
                db.close()
                reject(new Error("Erreur de transaction"))
              }
            } catch (e) {
              reject(e)
            }
          }

          request.onerror = (event) => {
            reject(new Error("Erreur d'ouverture de la base de données"))
          }
        })

        const result = await dbPromise
        setTestResults((prev) => ({ ...prev, indexedDB: result }))
        addLog(`Test IndexedDB: ${result ? "Réussi" : "Échoué"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, indexedDB: false }))
        addLog(`Test IndexedDB échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Test 3: Vérifier l'authentification
      addLog("Test 3/6: Vérification de l'authentification...")
      setProgress(40)
      try {
        // Vérifier si le service worker d'authentification est actif
        let authSwActive = false
        if (typeof window !== "undefined" && window.checkAuthServiceWorker) {
          const status = await window.checkAuthServiceWorker()
          authSwActive = status.isActive && status.authCacheExists
        }

        // Vérifier si des données d'authentification sont en cache
        let authDataInCache = false
        try {
          const response = await fetch("/auth-data")
          authDataInCache = response.ok
        } catch (e) {
          console.error("Erreur lors de la vérification des données d'authentification:", e)
        }

        const result = authSwActive && authDataInCache
        setTestResults((prev) => ({ ...prev, authentication: result }))
        addLog(`Test authentification: ${result ? "Réussi" : "Échoué"}`)
        addLog(`- Service Worker d'authentification: ${authSwActive ? "Actif" : "Inactif"}`)
        addLog(`- Données d'authentification en cache: ${authDataInCache ? "Présentes" : "Absentes"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, authentication: false }))
        addLog(`Test authentification échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Test 4: Vérifier les pages
      addLog("Test 4/6: Vérification des pages...")
      setProgress(55)
      try {
        // Vérifier si la page d'accueil est en cache
        const homeCache = await caches.match("/")

        // Vérifier si la page de dashboard est en cache
        const dashboardCache = await caches.match("/dashboard")

        const result = !!homeCache || !!dashboardCache
        setTestResults((prev) => ({ ...prev, pages: result }))
        addLog(`Test pages: ${result ? "Réussi" : "Échoué"}`)
        addLog(`- Page d'accueil en cache: ${!!homeCache ? "Oui" : "Non"}`)
        addLog(`- Page dashboard en cache: ${!!dashboardCache ? "Oui" : "Non"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, pages: false }))
        addLog(`Test pages échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Test 5: Vérifier les images
      addLog("Test 5/6: Vérification des images...")
      setProgress(70)
      try {
        // Vérifier si des images sont en cache
        const cacheNames = await caches.keys()
        let imagesFound = false

        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName)
          const keys = await cache.keys()

          const imageKeys = keys.filter((key) => key.url.match(/\.(jpg|jpeg|png|gif|svg|webp)($|\?)/))

          if (imageKeys.length > 0) {
            imagesFound = true
            addLog(`- ${imageKeys.length} image(s) trouvée(s) dans le cache "${cacheName}"`)
            break
          }
        }

        setTestResults((prev) => ({ ...prev, images: imagesFound }))
        addLog(`Test images: ${imagesFound ? "Réussi" : "Échoué"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, images: false }))
        addLog(`Test images échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Test 6: Vérifier les API
      addLog("Test 6/6: Vérification des API...")
      setProgress(85)
      try {
        // Vérifier si des requêtes API sont en cache
        const cacheNames = await caches.keys()
        let apisFound = false

        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName)
          const keys = await cache.keys()

          const apiKeys = keys.filter(
            (key) =>
              key.url.includes("/api/") || key.url.includes("firestore.googleapis.com") || key.url.includes("firebase"),
          )

          if (apiKeys.length > 0) {
            apisFound = true
            addLog(`- ${apiKeys.length} requête(s) API trouvée(s) dans le cache "${cacheName}"`)
            break
          }
        }

        setTestResults((prev) => ({ ...prev, api: apisFound }))
        addLog(`Test API: ${apisFound ? "Réussi" : "Échoué"}`)
      } catch (e) {
        setTestResults((prev) => ({ ...prev, api: false }))
        addLog(`Test API échoué: ${e instanceof Error ? e.message : String(e)}`)
      }

      setProgress(100)
      addLog("Tests terminés")
      setLoading(false)
    } catch (error) {
      console.error("Erreur lors des tests de fonctionnement hors ligne:", error)
      addLog(`Erreur globale: ${error instanceof Error ? error.message : String(error)}`)
      setProgress(100)
      setLoading(false)
    }
  }

  // Fonction pour précharger les données pour le mode hors ligne
  const preloadOfflineData = async () => {
    try {
      setLoading(true)
      setProgress(0)
      addLog("Démarrage du préchargement des données pour le mode hors ligne...")

      // Étape 1: Forcer la persistance d'authentification
      addLog("Étape 1/5: Forçage de la persistance d'authentification...")
      setProgress(10)
      if (typeof window !== "undefined" && window.forceMaximumPersistence) {
        const result = await window.forceMaximumPersistence()
        addLog(`Persistance d'authentification: ${result ? "Forcée avec succès" : "Échec du forçage"}`)
      } else {
        addLog("Fonction de persistance maximale non disponible")
      }

      // Étape 2: Précharger la page d'accueil
      addLog("Étape 2/5: Préchargement de la page d'accueil...")
      setProgress(30)
      try {
        const homeResponse = await fetch("/", { method: "GET", cache: "reload" })
        addLog(`Page d'accueil: ${homeResponse.ok ? "Préchargée avec succès" : "Échec du préchargement"}`)
      } catch (e) {
        addLog(`Échec du préchargement de la page d'accueil: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Étape 3: Précharger la page de dashboard
      addLog("Étape 3/5: Préchargement de la page de dashboard...")
      setProgress(50)
      try {
        const dashboardResponse = await fetch("/dashboard", { method: "GET", cache: "reload" })
        addLog(`Page de dashboard: ${dashboardResponse.ok ? "Préchargée avec succès" : "Échec du préchargement"}`)
      } catch (e) {
        addLog(`Échec du préchargement de la page de dashboard: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Étape 4: Précharger les données d'API
      addLog("Étape 4/5: Préchargement des données d'API...")
      setProgress(70)
      try {
        // Précharger les données d'actualités
        const newsResponse = await fetch("/api/news", { method: "GET", cache: "reload" })
        addLog(`API actualités: ${newsResponse.ok ? "Préchargée avec succès" : "Échec du préchargement"}`)

        // Précharger les données de pages
        const pagesResponse = await fetch("/api/pages", { method: "GET", cache: "reload" })
        addLog(`API pages: ${pagesResponse.ok ? "Préchargée avec succès" : "Échec du préchargement"}`)
      } catch (e) {
        addLog(`Échec du préchargement des données d'API: ${e instanceof Error ? e.message : String(e)}`)
      }

      // Étape 5: Vérifier l'état du cache
      addLog("Étape 5/5: Vérification de l'état du cache...")
      setProgress(90)
      await checkCacheStatus()

      setProgress(100)
      addLog("Préchargement terminé")
      setLoading(false)
    } catch (error) {
      console.error("Erreur lors du préchargement des données:", error)
      addLog(`Erreur globale: ${error instanceof Error ? error.message : String(error)}`)
      setProgress(100)
      setLoading(false)
    }
  }

  // Fonction pour exporter les logs
  const exportLogs = () => {
    try {
      const logsText = logs.join("\n")
      const blob = new Blob([logsText], { type: "text/plain" })
      const url = URL.createObjectURL(blob)

      const a = document.createElement("a")
      a.href = url
      a.download = `offline-test-logs-${new Date().toISOString().replace(/:/g, "-")}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      addLog("Logs exportés avec succès")
    } catch (error) {
      console.error("Erreur lors de l'exportation des logs:", error)
      addLog(`Erreur lors de l'exportation des logs: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // Charger l'état du cache au montage du composant
  useEffect(() => {
    checkCacheStatus()
  }, [])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            {isOnline ? (
              <Wifi className="h-5 w-5 mr-2 text-green-600" />
            ) : (
              <WifiOff className="h-5 w-5 mr-2 text-amber-600" />
            )}
            Test de Fonctionnement Hors Ligne
          </CardTitle>
          <CardDescription>Testez et préparez votre application pour une utilisation hors ligne</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Badge className={isOnline ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"}>
                {isOnline ? "En ligne" : "Hors ligne"}
              </Badge>
            </div>

            {loading && (
              <div className="w-full max-w-xs">
                <Progress value={progress} className="h-2" />
              </div>
            )}
          </div>

          <Tabs defaultValue="tests" className="mt-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tests">Tests</TabsTrigger>
              <TabsTrigger value="status">État du cache</TabsTrigger>
              <TabsTrigger value="logs">Logs</TabsTrigger>
            </TabsList>

            <TabsContent value="tests" className="mt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Stockage local</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">LocalStorage:</span>
                      {testResults.localStorage === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.localStorage ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Disponible</Badge>
                      ) : (
                        <Badge variant="destructive">Non disponible</Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">IndexedDB:</span>
                      {testResults.indexedDB === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.indexedDB ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Disponible</Badge>
                      ) : (
                        <Badge variant="destructive">Non disponible</Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Contenu</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Pages:</span>
                      {testResults.pages === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.pages ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">En cache</Badge>
                      ) : (
                        <Badge variant="destructive">Non mises en cache</Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Images:</span>
                      {testResults.images === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.images ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">En cache</Badge>
                      ) : (
                        <Badge variant="destructive">Non mises en cache</Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">API:</span>
                      {testResults.api === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.api ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">En cache</Badge>
                      ) : (
                        <Badge variant="destructive">Non mises en cache</Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Authentification</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Session:</span>
                      {testResults.authentication === null ? (
                        <Badge variant="outline">Non testé</Badge>
                      ) : testResults.authentication ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Persistante</Badge>
                      ) : (
                        <Badge variant="destructive">Non persistante</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                <Button onClick={runOfflineTests} disabled={loading}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Lancer les tests
                </Button>
                <Button onClick={preloadOfflineData} disabled={loading} variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Précharger les données
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="status" className="mt-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center justify-between">
                  <span>État du cache</span>
                  <Button variant="outline" size="sm" onClick={checkCacheStatus} disabled={loading}>
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Actualiser
                  </Button>
                </h3>

                {cacheStatus ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Caches disponibles:</h4>
                      {cacheStatus.cacheNames.length > 0 ? (
                        <ul className="list-disc pl-6 text-sm space-y-1">
                          {cacheStatus.cacheNames.map((name: string) => (
                            <li key={name}>{name}</li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-muted-foreground">Aucun cache trouvé</p>
                      )}
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-1">Service Worker:</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Enregistré:</div>
                        <div>{cacheStatus.serviceWorker.registered ? "Oui" : "Non"}</div>

                        <div>Actif:</div>
                        <div>{cacheStatus.serviceWorker.active ? "Oui" : "Non"}</div>

                        <div>Scope:</div>
                        <div>{cacheStatus.serviceWorker.scope || "N/A"}</div>

                        <div>En attente:</div>
                        <div>{cacheStatus.serviceWorker.waiting ? "Oui" : "Non"}</div>

                        <div>En installation:</div>
                        <div>{cacheStatus.serviceWorker.installing ? "Oui" : "Non"}</div>
                      </div>
                    </div>

                    {cacheStatus.authServiceWorker && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">Service Worker d'authentification:</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>Version:</div>
                          <div>{cacheStatus.authServiceWorker.version || "Inconnue"}</div>

                          <div>Actif:</div>
                          <div>{cacheStatus.authServiceWorker.isActive ? "Oui" : "Non"}</div>

                          <div>Cache existant:</div>
                          <div>{cacheStatus.authServiceWorker.authCacheExists ? "Oui" : "Non"}</div>

                          <div>Données en cache:</div>
                          <div>{cacheStatus.authServiceWorker.authDataExists ? "Oui" : "Non"}</div>
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="text-sm font-medium mb-1">IndexedDB:</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Disponible:</div>
                        <div>{cacheStatus.indexedDB.available ? "Oui" : "Non"}</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">Chargement de l'état du cache...</div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="logs" className="mt-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center justify-between">
                  <span>Logs</span>
                  <Button variant="outline" size="sm" onClick={exportLogs} disabled={logs.length === 0}>
                    <Download className="h-4 w-4 mr-1" />
                    Exporter
                  </Button>
                </h3>
                <ScrollArea className="h-[300px] w-full rounded-md border p-4">
                  {logs.length > 0 ? (
                    <div className="space-y-1 font-mono text-xs">
                      {logs.map((log, index) => (
                        <div key={index} className="border-b pb-1">
                          {log}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">Aucun log disponible</div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button onClick={checkCacheStatus} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Vérifier l'état du cache
          </Button>
          <Button onClick={runOfflineTests} disabled={loading} variant="outline">
            <WifiOff className="h-4 w-4 mr-2" />
            Tester le mode hors ligne
          </Button>
          <Button onClick={preloadOfflineData} disabled={loading} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Précharger les données
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
