import { NextResponse } from "next/server"
import { getUserSession } from "@/lib/server-auth"

export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET() {
  try {
    const { user, isAdmin } = await getUserSession()

    if (user) {
      return NextResponse.json({
        authenticated: true,
        user,
        isAdmin,
      })
    }

    return NextResponse.json({
      authenticated: false,
      user: null,
      isAdmin: false,
    })
  } catch (error) {
    console.error("Erreur lors de la vérification de la session:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: "Erreur lors de la vérification de la session",
      },
      { status: 500 },
    )
  }
}
