// Enregistrement du service worker pour la persistance d'authentification
// Ce script gère l'enregistrement du service worker dédié à l'authentification
// et fournit des fonctions pour interagir avec lui

// Configuration
const AUTH_SW_VERSION = "v3"
const AUTH_CACHE_NAME = `acr-direct-auth-${AUTH_SW_VERSION}`

// Fonction pour enregistrer le service worker d'authentification
function registerAuthServiceWorker() {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker
      .register("/auth-persistence-sw.js", {
        scope: "/",
        updateViaCache: "none", // Ne pas utiliser le cache pour les mises à jour
      })
      .then((registration) => {
        console.log("Auth Service Worker registered with scope:", registration.scope)

        // Vérifier les mises à jour toutes les heures
        setInterval(
          () => {
            registration
              .update()
              .then(() => console.log("Auth Service Worker update check completed"))
              .catch((err) => console.error("Auth Service Worker update check failed:", err))
          },
          60 * 60 * 1000,
        ) // 1 heure

        // Définir la fonction globale pour mettre en cache les données d'authentification
        window.cacheAuthData = (authData) => {
          if (navigator.serviceWorker.controller) {
            // Créer un canal de communication
            const messageChannel = new MessageChannel()

            // Promesse pour attendre la confirmation
            return new Promise((resolve, reject) => {
              // Définir un timeout pour éviter les blocages
              const timeout = setTimeout(() => {
                console.warn("Timeout lors de la mise en cache des données d'authentification")
                resolve({ success: false, reason: "timeout" })
              }, 5000) // 5 secondes de timeout

              messageChannel.port1.onmessage = (event) => {
                clearTimeout(timeout) // Annuler le timeout
                if (event.data.error) {
                  reject(event.data.error)
                } else {
                  resolve(event.data)
                }
              }

              // Envoyer les données au service worker
              navigator.serviceWorker.controller.postMessage(
                {
                  type: "AUTH_DATA",
                  payload: authData,
                  timestamp: Date.now(),
                  version: AUTH_SW_VERSION,
                },
                [messageChannel.port2],
              )
            })
          } else {
            console.warn("Service Worker not controlling the page yet")
            return Promise.resolve({ success: false, reason: "no_controller" })
          }
        }

        // Fonction pour récupérer les données d'authentification
        window.getAuthDataFromCache = async () => {
          try {
            const response = await fetch("/auth-data")
            if (response.ok) {
              return await response.json()
            }
            return null
          } catch (error) {
            console.error("Error fetching auth data from cache:", error)
            return null
          }
        }

        // Fonction pour vérifier l'état du service worker d'authentification
        window.checkAuthServiceWorker = async () => {
          try {
            // Vérifier si le service worker est actif
            const isActive = !!navigator.serviceWorker.controller

            // Vérifier si le cache d'authentification existe
            const cacheKeys = await caches.keys()
            const authCacheExists = cacheKeys.includes(AUTH_CACHE_NAME)

            // Vérifier si des données d'authentification sont en cache
            let authDataExists = false
            if (authCacheExists) {
              const cache = await caches.open(AUTH_CACHE_NAME)
              const authDataResponse = await cache.match("/auth-data")
              authDataExists = !!authDataResponse
            }

            return {
              isActive,
              authCacheExists,
              authDataExists,
              version: AUTH_SW_VERSION,
            }
          } catch (error) {
            console.error("Error checking auth service worker:", error)
            return {
              isActive: false,
              authCacheExists: false,
              authDataExists: false,
              error: error.message,
              version: AUTH_SW_VERSION,
            }
          }
        }

        // Fonction pour forcer la mise à jour du service worker d'authentification
        window.updateAuthServiceWorker = async () => {
          try {
            await registration.update()
            return { success: true }
          } catch (error) {
            console.error("Error updating auth service worker:", error)
            return { success: false, error: error.message }
          }
        }
      })
      .catch((error) => {
        console.error("Auth Service Worker registration failed:", error)
      })
  } else {
    console.warn("Service Worker not supported in this browser")

    // Définir des fonctions de remplacement pour éviter les erreurs
    window.cacheAuthData = () => Promise.resolve({ success: false, reason: "not_supported" })
    window.getAuthDataFromCache = () => Promise.resolve(null)
    window.checkAuthServiceWorker = () =>
      Promise.resolve({
        isActive: false,
        authCacheExists: false,
        authDataExists: false,
        supported: false,
        version: AUTH_SW_VERSION,
      })
    window.updateAuthServiceWorker = () => Promise.resolve({ success: false, reason: "not_supported" })
  }
}

// Fonction pour vérifier si l'authentification est en cache
async function isAuthInCache() {
  try {
    // Vérifier si le service worker est supporté
    if (!("serviceWorker" in navigator)) {
      return false
    }

    // Vérifier si le cache d'authentification existe
    const cacheKeys = await caches.keys()
    const authCacheExists = cacheKeys.includes(AUTH_CACHE_NAME)

    if (!authCacheExists) {
      return false
    }

    // Vérifier si des données d'authentification sont en cache
    const cache = await caches.open(AUTH_CACHE_NAME)
    const authDataResponse = await cache.match("/auth-data")

    return !!authDataResponse
  } catch (error) {
    console.error("Error checking auth in cache:", error)
    return false
  }
}

// Exposer la fonction globalement
window.isAuthInCache = isAuthInCache

// Enregistrer le service worker
registerAuthServiceWorker()

// Informer que le script a été chargé
console.log("Auth Service Worker registration script initialized (v3)")
