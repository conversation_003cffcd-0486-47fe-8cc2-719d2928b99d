/**
 * Utilitaires pour la mise en cache des données utilisateur
 * Ces fonctions permettent de stocker et récupérer des données en cache
 * pour accélérer le chargement du dashboard
 */

// Durée de validité du cache en millisecondes (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000

/**
 * Stocke des données en cache avec une durée de validité
 * @param key Clé du cache
 * @param data Données à stocker
 * @param duration Durée de validité en ms (par défaut 5 minutes)
 */
export function setCacheData(key: string, data: any, duration: number = CACHE_DURATION): void {
  try {
    const cacheItem = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + duration,
    }

    localStorage.setItem(key, JSON.stringify(cacheItem))
  } catch (error) {
    console.error(`Erreur lors du stockage des données en cache pour ${key}:`, error)
  }
}

/**
 * Récupère des données du cache si elles sont valides
 * @param key Clé du cache
 * @returns Les données en cache ou null si elles sont expirées ou inexistantes
 */
export function getCacheData<T>(key: string): T | null {
  try {
    const cachedItem = localStorage.getItem(key)

    if (!cachedItem) {
      return null
    }

    const { data, expiry } = JSON.parse(cachedItem)

    // Vérifier si les données sont expirées
    if (Date.now() > expiry) {
      localStorage.removeItem(key)
      return null
    }

    return data as T
  } catch (error) {
    console.error(`Erreur lors de la récupération des données en cache pour ${key}:`, error)
    return null
  }
}

/**
 * Invalide une entrée du cache
 * @param key Clé du cache à invalider
 */
export function invalidateCache(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error(`Erreur lors de l'invalidation du cache pour ${key}:`, error)
  }
}

/**
 * Invalide toutes les entrées du cache correspondant à un préfixe
 * @param prefix Préfixe des clés à invalider
 */
export function invalidateCacheByPrefix(prefix: string): void {
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        localStorage.removeItem(key)
      }
    }
  } catch (error) {
    console.error(`Erreur lors de l'invalidation du cache pour le préfixe ${prefix}:`, error)
  }
}

/**
 * Génère une clé de cache pour un utilisateur et un type de données
 * @param userId ID de l'utilisateur
 * @param dataType Type de données (groups, news, etc.)
 * @returns Clé de cache
 */
export function getUserCacheKey(userId: string, dataType: string): string {
  return `user_${userId}_${dataType}`
}
