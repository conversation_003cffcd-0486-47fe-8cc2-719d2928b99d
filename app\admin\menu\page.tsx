"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MenuService } from "@/lib/menu-service"
import { useAuth } from "@/components/auth-provider"
import { toast } from "@/components/ui/use-toast"

export default function MenuAdminPage() {
  const [menuItems, setMenuItems] = useState([])
  const [loading, setLoading] = useState(true)
  const { user, refreshMenuItems } = useAuth()

  useEffect(() => {
    loadMenuItems()
  }, [])

  const loadMenuItems = async () => {
    try {
      setLoading(true)
      const items = await MenuService.getAllMenuItems()
      setMenuItems(items)
      setLoading(false)
    } catch (error) {
      console.error("Failed to load menu items:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les éléments de menu",
        variant: "destructive",
      })
      setLoading(false)
    }
  }

  const handlePublishToggle = async (itemId, isCurrentlyPublished) => {
    try {
      await MenuService.updateMenuItem(itemId, { isPublished: !isCurrentlyPublished }, user.uid)

      // Rafraîchir la liste des éléments de menu
      await loadMenuItems()

      // Rafraîchir les éléments de menu dans le contexte d'authentification
      await refreshMenuItems()

      toast({
        title: "Succès",
        description: `Élément de menu ${isCurrentlyPublished ? "masqué" : "publié"} avec succès`,
        variant: "default",
      })
    } catch (error) {
      console.error("Failed to update menu item:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour l'élément de menu",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Gestion des menus</h1>

      <div className="grid gap-6">
        {loading ? (
          <p>Chargement des éléments de menu...</p>
        ) : (
          menuItems.map((item) => (
            <Card key={item.id}>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>{item.title}</CardTitle>
                <Button
                  variant={item.isPublished ? "default" : "outline"}
                  onClick={() => handlePublishToggle(item.id, item.isPublished)}
                >
                  {item.isPublished ? "Publié" : "Non publié"}
                </Button>
              </CardHeader>
              <CardContent>
                <p>Chemin: {item.slug}</p>
                <p>Groupes cibles: {item.targetGroups.join(", ")}</p>
                <p>Ordre d'affichage: {item.displayOrder || 0}</p>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
