"use client"

import { auth } from "./firebase"
import { onAuthStateChanged, getIdToken } from "firebase/auth"

/**
 * Fonction pour améliorer la persistance de l'authentification Firebase
 * en utilisant des techniques supplémentaires de stockage local
 */
export function enhanceAuthPersistence() {
  if (typeof window === "undefined") return

  // Vérifier si nous sommes dans un contexte sécurisé (HTTPS ou localhost)
  const isSecureContext =
    window.isSecureContext || window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1"

  if (!isSecureContext) {
    console.warn("Contexte non sécurisé: certaines fonctionnalités de persistance peuvent ne pas fonctionner")
  }

  // Configurer l'écouteur d'état d'authentification
  const unsubscribe = onAuthStateChanged(auth(), async (user) => {
    if (user) {
      try {
        // Obtenir le token ID et le stocker
        const token = await getIdToken(user, true)

        // Stocker les informations utilisateur essentielles
        localStorage.setItem("auth_user_id", user.uid)
        localStorage.setItem("auth_user_email", user.email || "")
        localStorage.setItem("auth_user_token", token)
        localStorage.setItem("auth_last_login", Date.now().toString())

        // Stocker les données dans IndexedDB via localForage si disponible
        if (window.indexedDB) {
          try {
            // Utiliser dynamiquement localForage pour éviter les problèmes SSR
            const localforage = await import("localforage")
            const authStore = localforage.default.createInstance({
              name: "acrDirectAuth",
              storeName: "authData",
            })

            await authStore.setItem("user", {
              uid: user.uid,
              email: user.email,
              token,
              lastLogin: Date.now(),
            })
          } catch (error) {
            console.error("Erreur lors du stockage dans IndexedDB:", error)
          }
        }

        // Informer le service worker si disponible
        if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
          navigator.serviceWorker.controller.postMessage({
            type: "CACHE_AUTH_DATA",
            authData: {
              uid: user.uid,
              token,
            },
          })
        }

        console.log("Persistance d'authentification améliorée activée")
      } catch (error) {
        console.error("Erreur lors de l'amélioration de la persistance:", error)
      }
    } else {
      // Utilisateur déconnecté, nettoyer les données stockées
      // Nous ne supprimons pas les données ici pour permettre la reconnexion automatique
      console.log("Utilisateur non authentifié")
    }
  })

  // Configurer un rafraîchissement périodique du token
  let refreshInterval: number | null = null

  if (typeof window !== "undefined") {
    refreshInterval = window.setInterval(
      async () => {
        const currentUser = auth().currentUser
        if (currentUser) {
          try {
            const token = await getIdToken(currentUser, true)
            localStorage.setItem("auth_user_token", token)
            localStorage.setItem("auth_token_refresh", Date.now().toString())
          } catch (error) {
            console.error("Erreur lors du rafraîchissement du token:", error)
          }
        }
      },
      10 * 60 * 1000,
    ) // Rafraîchir toutes les 10 minutes
  }

  // Retourner une fonction de nettoyage
  return () => {
    unsubscribe()
    if (refreshInterval !== null) {
      clearInterval(refreshInterval)
    }
  }
}

/**
 * Fonction pour tenter de restaurer une session utilisateur
 * à partir des données stockées localement
 */
export async function attemptSessionRestore() {
  if (typeof window === "undefined") return null

  // Vérifier si nous avons des données d'authentification stockées
  const userId = localStorage.getItem("auth_user_id")
  const userEmail = localStorage.getItem("auth_user_email")
  const userToken = localStorage.getItem("auth_user_token")

  if (userId && userToken) {
    // Nous avons des données stockées, mais vérifions si l'utilisateur est déjà connecté
    const currentUser = auth().currentUser

    if (currentUser) {
      console.log("Session utilisateur déjà active")
      return currentUser
    }

    console.log("Tentative de restauration de session...")

    // Tenter de restaurer la session via Firebase
    // Note: Ceci est principalement géré par Firebase lui-même grâce à browserLocalPersistence
    // Cette fonction sert de couche supplémentaire de sécurité

    // Attendre que Firebase vérifie l'état d'authentification
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth(), (user) => {
        unsubscribe()
        if (user) {
          console.log("Session restaurée avec succès")
          resolve(user)
        } else {
          console.log("Impossible de restaurer la session automatiquement")
          resolve(null)
        }
      })
    })
  }

  return null
}

export async function forceAuthPersistence(user: any) {
  try {
    // Stocker dans localStorage
    if (user) {
      localStorage.setItem("firebase:auth:user", JSON.stringify(user))

      // Stocker également dans IndexedDB si disponible
      if ("indexedDB" in window) {
        const db = await openAuthDatabase()
        const transaction = db.transaction(["auth"], "readwrite")
        const store = transaction.objectStore("auth")
        await store.put({
          id: "currentUser",
          user: user,
          timestamp: Date.now(),
        })
      }

      // Envoyer au service worker si disponible
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: "CACHE_AUTH_DATA",
          authData: user,
        })
      }

      console.log("Données d'authentification persistées avec succès")
      return true
    }
    return false
  } catch (error) {
    console.error("Erreur lors de la persistance des données d'authentification:", error)
    return false
  }
}

// Fonction pour ouvrir la base de données d'authentification
async function openAuthDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open("authDB", 1)

    request.onupgradeneeded = (event) => {
      const db = request.result
      if (!db.objectStoreNames.contains("auth")) {
        db.createObjectStore("auth", { keyPath: "id" })
      }
    }

    request.onsuccess = () => resolve(request.result)
    request.onerror = () => reject(request.error)
  })
}
