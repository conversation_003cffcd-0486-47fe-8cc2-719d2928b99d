"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { X, Upload, Info } from "lucide-react"
import { saveCommercialContact, uploadCommercialPhoto } from "@/lib/commercial-utils"
import { DEPARTMENTS } from "@/lib/commercial-types"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"

// Import getUserById from user-service
import { getUsersByRole, getUserById } from "@/lib/user-service"

// Replace the entire component with this updated version
export default function CreateCommercialPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [departments, setDepartments] = useState<string[]>([])
  const [photoFile, setPhotoFile] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [commercialUsers, setCommercialUsers] = useState<any[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [isLoadingUserData, setIsLoadingUserData] = useState(false)
  const [userPhotoURL, setUserPhotoURL] = useState<string | null>(null)

  // Load commercial users on component mount
  useEffect(() => {
    const loadCommercialUsers = async () => {
      try {
        const users = await getUsersByRole("commercial")
        setCommercialUsers(users)
      } catch (error) {
        console.error("Error loading commercial users:", error)
      }
    }

    loadCommercialUsers()
  }, [])

  // Load user data when a user is selected
  useEffect(() => {
    const loadUserData = async () => {
      if (!selectedUserId || selectedUserId === "none") {
        // Clear form if no user is selected
        setName("")
        setEmail("")
        setPhone("")
        setUserPhotoURL(null)
        setPhotoPreview(null)
        return
      }

      setIsLoadingUserData(true)
      try {
        const userData = await getUserById(selectedUserId)
        if (userData) {
          // Populate form with user data
          setName(userData.displayName || "")
          setEmail(userData.email || "")
          setPhone(userData.phoneNumber || userData.phone || "")

          // Set user photo if available
          if (userData.photoURL) {
            setUserPhotoURL(userData.photoURL)
            setPhotoPreview(userData.photoURL)
          }

          // Ajoutons également un log pour déboguer
          console.log("User data loaded:", userData)
        }
      } catch (error) {
        console.error("Error loading user data:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les données de l'utilisateur.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingUserData(false)
      }
    }

    loadUserData()
  }, [selectedUserId, toast])

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setPhotoFile(file)

      // Create a preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setPhotoPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleAddDepartment = (departmentCode: string) => {
    if (!departments.includes(departmentCode)) {
      setDepartments([...departments, departmentCode])
    }
  }

  const handleRemoveDepartment = (departmentCode: string) => {
    setDepartments(departments.filter((d) => d !== departmentCode))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedUserId || selectedUserId === "none") {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un utilisateur.",
        variant: "destructive",
      })
      return
    }

    if (!name || !email || departments.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      // Create a temporary ID for the contact
      const tempId = `temp_${Date.now()}`

      // Upload photo if provided
      let photoURL = ""
      if (photoFile) {
        photoURL = await uploadCommercialPhoto(photoFile, tempId)
      } else if (userPhotoURL) {
        // Use the user's photo if available and no new photo was uploaded
        photoURL = userPhotoURL
      }

      // Save the contact
      const contactId = await saveCommercialContact({
        id: "",
        name,
        email,
        phone,
        departments,
        photoURL,
        userId: selectedUserId,
      })

      toast({
        title: "Succès",
        description: "Le profil commercial a été créé avec succès.",
      })

      router.push("/admin/commerciaux")
    } catch (error) {
      console.error("Error creating commercial contact:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du profil commercial.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Ajouter un profil commercial</h1>
        <Button variant="outline" onClick={() => router.push("/admin/commerciaux")}>
          Retour à la liste
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informations du profil commercial</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              {/* User selection field */}
              <div className="space-y-2">
                <Label htmlFor="userId">Utilisateur associé *</Label>
                <Select
                  onValueChange={(value) => {
                    setSelectedUserId(value === "none" ? "" : value)
                  }}
                  value={selectedUserId || "none"}
                >
                  <SelectTrigger id="userId">
                    <SelectValue placeholder="Sélectionner un utilisateur" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Sélectionner un utilisateur</SelectItem>
                    {commercialUsers.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.displayName || user.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Sélectionnez l'utilisateur pour lequel vous souhaitez créer un profil commercial.
                </p>
              </div>

              {selectedUserId && selectedUserId !== "none" && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Les informations de l'utilisateur ont été automatiquement importées. Vous pouvez les modifier si
                    nécessaire.
                  </AlertDescription>
                </Alert>
              )}

              {isLoadingUserData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Nom complet *</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Jean Dupont"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Téléphone</Label>
                      <Input
                        id="phone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder="06 12 34 56 78"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="department">Ajouter un département *</Label>
                      <Select onValueChange={handleAddDepartment}>
                        <SelectTrigger id="department">
                          <SelectValue placeholder="Sélectionner un département" />
                        </SelectTrigger>
                        <SelectContent>
                          {DEPARTMENTS.map((dept) => (
                            <SelectItem key={dept.code} value={dept.code}>
                              {dept.code} - {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Départements sélectionnés *</Label>
                    {departments.length === 0 ? (
                      <p className="text-sm text-muted-foreground">Aucun département sélectionné</p>
                    ) : (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {departments.map((dept) => {
                          const deptName = DEPARTMENTS.find((d) => d.code === dept)?.name || dept
                          return (
                            <Badge key={dept} variant="secondary" className="flex items-center gap-1">
                              {dept} - {deptName}
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                                onClick={() => handleRemoveDepartment(dept)}
                              >
                                <X className="h-3 w-3" />
                                <span className="sr-only">Supprimer</span>
                              </Button>
                            </Badge>
                          )
                        })}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="photo">Photo de profil</Label>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-16 w-16">
                        {photoPreview ? (
                          <AvatarImage src={photoPreview || "/placeholder.svg"} alt="Preview" />
                        ) : (
                          <AvatarFallback>?</AvatarFallback>
                        )}
                      </Avatar>
                      <div className="flex-1">
                        <Input
                          id="photo"
                          type="file"
                          accept="image/*"
                          onChange={handlePhotoChange}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById("photo")?.click()}
                          className="w-full"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          {photoFile
                            ? "Changer la photo"
                            : userPhotoURL
                              ? "Modifier la photo"
                              : "Télécharger une photo"}
                        </Button>
                        {photoFile && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {photoFile.name} ({Math.round(photoFile.size / 1024)} Ko)
                          </p>
                        )}
                        {userPhotoURL && !photoFile && (
                          <p className="text-xs text-muted-foreground mt-1">Photo de profil de l'utilisateur</p>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/commerciaux")}
                disabled={loading}
              >
                Annuler
              </Button>
              <Button type="submit" disabled={loading || isLoadingUserData}>
                {loading ? (
                  <>
                    <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                    Enregistrement...
                  </>
                ) : (
                  "Enregistrer"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
