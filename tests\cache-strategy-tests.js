/**
 * Tests automatisés pour les stratégies de mise en cache
 * Ce script teste les différentes stratégies de mise en cache
 * en utilisant Puppeteer pour simuler un navigateur
 */

const puppeteer = require("puppeteer")
const fs = require("fs")
const path = require("path")
const lighthouse = require("lighthouse")
const { URL } = require("url")

// Configuration
const config = {
  baseUrl: "http://localhost:3000",
  loginEmail: "<EMAIL>",
  loginPassword: "password",
  outputDir: path.join(__dirname, "reports"),
  screenshotDir: path.join(__dirname, "screenshots"),
  timeout: 30000, // 30 secondes
}

// Créer les répertoires de sortie s'ils n'existent pas
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true })
}
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true })
}

// Fonction pour générer un rapport
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, "-")
  const reportPath = path.join(config.outputDir, `cache-strategy-report-${timestamp}.json`)

  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
  console.log(`Rapport généré: ${reportPath}`)

  // Générer un rapport HTML
  const htmlReportPath = path.join(config.outputDir, `cache-strategy-report-${timestamp}.html`)
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de test des stratégies de cache</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .summary { display: flex; margin-bottom: 20px; }
        .summary-box { flex: 1; padding: 15px; margin: 0 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .failure { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .test-case { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .test-case h3 { margin-top: 0; }
        .passed { border-left: 5px solid #28a745; }
        .failed { border-left: 5px solid #dc3545; }
        .skipped { border-left: 5px solid #ffc107; }
        .error { color: #dc3545; }
        .steps { margin-left: 20px; }
        .step { margin-bottom: 5px; }
        .step-passed { color: #28a745; }
        .step-failed { color: #dc3545; }
        .screenshot { max-width: 100%; height: auto; border: 1px solid #ddd; margin-top: 10px; }
        .metrics { margin-top: 15px; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 5px; }
        .metric-name { font-weight: bold; }
        .metric-value { text-align: right; }
        .good { color: #28a745; }
        .average { color: #ffc107; }
        .poor { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
      </style>
    </head>
    <body>
      <h1>Rapport de test des stratégies de cache</h1>
      <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
      <p><strong>URL de base:</strong> ${config.baseUrl}</p>
      
      <div class="summary">
        <div class="summary-box ${results.success ? "success" : "failure"}">
          <h2>Résultat global</h2>
          <p>${results.success ? "Succès" : "Échec"}</p>
          <p>${results.testsPassed}/${results.totalTests} tests réussis</p>
        </div>
        <div class="summary-box info">
          <h2>Durée</h2>
          <p>${results.duration} secondes</p>
        </div>
      </div>
      
      <h2>Détails des tests</h2>
      ${results.tests
        .map(
          (test) => `
        <div class="test-case ${test.success ? "passed" : "failed"}">
          <h3>${test.name}</h3>
          <p><strong>Statut:</strong> ${test.success ? "Réussi" : "Échoué"}</p>
          ${test.error ? `<p class="error"><strong>Erreur:</strong> ${test.error}</p>` : ""}
          
          <div class="steps">
            ${test.steps
              .map(
                (step) => `
              <div class="step ${step.success ? "step-passed" : "step-failed"}">
                ${step.success ? "✓" : "✗"} ${step.name}
                ${step.error ? `<div class="error">${step.error}</div>` : ""}
              </div>
            `,
              )
              .join("")}
          </div>
          
          ${
            test.metrics
              ? `
            <div class="metrics">
              <h4>Métriques de performance</h4>
              <div class="metric">
                <span class="metric-name">Temps de chargement:</span>
                <span class="metric-value ${test.metrics.loadTime < 1000 ? "good" : test.metrics.loadTime < 3000 ? "average" : "poor"}">
                  ${test.metrics.loadTime} ms
                </span>
              </div>
              <div class="metric">
                <span class="metric-name">Temps de chargement (2e visite):</span>
                <span class="metric-value ${test.metrics.secondLoadTime < 500 ? "good" : test.metrics.secondLoadTime < 1000 ? "average" : "poor"}">
                  ${test.metrics.secondLoadTime} ms
                </span>
              </div>
              <div class="metric">
                <span class="metric-name">Amélioration:</span>
                <span class="metric-value ${test.metrics.improvement > 70 ? "good" : test.metrics.improvement > 30 ? "average" : "poor"}">
                  ${test.metrics.improvement.toFixed(1)}%
                </span>
              </div>
            </div>
          `
              : ""
          }
          
          ${
            test.resources
              ? `
            <div class="resources">
              <h4>Ressources mises en cache</h4>
              <table>
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Nombre</th>
                    <th>Taille totale</th>
                    <th>Mise en cache</th>
                  </tr>
                </thead>
                <tbody>
                  ${Object.entries(test.resources)
                    .map(
                      ([type, data]) => `
                    <tr>
                      <td>${type}</td>
                      <td>${data.count}</td>
                      <td>${(data.size / 1024).toFixed(1)} KB</td>
                      <td class="${data.cacheRate > 90 ? "good" : data.cacheRate > 50 ? "average" : "poor"}">
                        ${data.cacheRate.toFixed(1)}%
                      </td>
                    </tr>
                  `,
                    )
                    .join("")}
                </tbody>
              </table>
            </div>
          `
              : ""
          }
          
          ${test.screenshot ? `<img class="screenshot" src="../screenshots/${path.basename(test.screenshot)}" alt="Capture d'écran">` : ""}
        </div>
      `,
        )
        .join("")}
    </body>
    </html>
  `

  fs.writeFileSync(htmlReportPath, htmlContent)
  console.log(`Rapport HTML généré: ${htmlReportPath}`)

  return reportPath
}

// Fonction pour analyser les ressources mises en cache
async function analyzeResources(page) {
  return await page.evaluate(() => {
    const resources = performance.getEntriesByType("resource")
    const result = {
      script: { count: 0, size: 0, cached: 0 },
      style: { count: 0, size: 0, cached: 0 },
      image: { count: 0, size: 0, cached: 0 },
      font: { count: 0, size: 0, cached: 0 },
      other: { count: 0, size: 0, cached: 0 },
    }

    resources.forEach((resource) => {
      let type = "other"
      const url = resource.name

      if (url.endsWith(".js")) type = "script"
      else if (url.endsWith(".css")) type = "style"
      else if (
        url.endsWith(".png") ||
        url.endsWith(".jpg") ||
        url.endsWith(".jpeg") ||
        url.endsWith(".gif") ||
        url.endsWith(".svg")
      )
        type = "image"
      else if (url.endsWith(".woff") || url.endsWith(".woff2") || url.endsWith(".ttf")) type = "font"

      result[type].count++
      result[type].size += resource.transferSize || 0

      // Si transferSize est 0 ou très petit, la ressource a probablement été mise en cache
      if (
        resource.transferSize === 0 ||
        (resource.transferSize > 0 && resource.transferSize < resource.encodedBodySize * 0.1)
      ) {
        result[type].cached++
      }
    })

    // Calculer le taux de mise en cache
    Object.keys(result).forEach((type) => {
      result[type].cacheRate = result[type].count > 0 ? (result[type].cached / result[type].count) * 100 : 0
    })

    return result
  })
}

// Fonction principale pour exécuter les tests
async function runTests() {
  console.log("Démarrage des tests des stratégies de mise en cache...")

  const startTime = Date.now()
  const browser = await puppeteer.launch({
    headless: false, // Mettre à true pour exécuter sans interface graphique
    defaultViewport: { width: 1280, height: 800 },
    args: ["--window-size=1280,800"],
  })

  const results = {
    timestamp: new Date().toISOString(),
    baseUrl: config.baseUrl,
    success: false,
    testsPassed: 0,
    totalTests: 0,
    duration: 0,
    tests: [],
  }

  try {
    const page = await browser.newPage()
    page.setDefaultTimeout(config.timeout)

    // Activer la journalisation de la console
    page.on("console", (msg) => console.log(`[Page Console] ${msg.text()}`))

    // Test 1: Stratégie de mise en cache des pages HTML
    const htmlCacheTest = {
      name: "Stratégie de mise en cache des pages HTML",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
      metrics: null,
      resources: null,
    }

    try {
      // Étape 1: Accéder à la page d'accueil (première visite)
      htmlCacheTest.steps.push({ name: "Accès à la page d'accueil (première visite)", success: false })
      const firstVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/`, { waitUntil: "networkidle2" })
      const firstVisitDuration = Date.now() - firstVisitStart
      htmlCacheTest.steps[0].success = true

      // Étape 2: Vider le cache du navigateur
      htmlCacheTest.steps.push({ name: "Vidage du cache du navigateur", success: false })
      const client = await page.target().createCDPSession()
      await client.send("Network.clearBrowserCache")
      htmlCacheTest.steps[1].success = true

      // Étape 3: Accéder à la page d'accueil (deuxième visite)
      htmlCacheTest.steps.push({ name: "Accès à la page d'accueil (deuxième visite)", success: false })
      const secondVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/`, { waitUntil: "networkidle2" })
      const secondVisitDuration = Date.now() - secondVisitStart
      htmlCacheTest.steps[2].success = true

      // Étape 4: Analyser les ressources mises en cache
      htmlCacheTest.steps.push({ name: "Analyse des ressources mises en cache", success: false })
      const resources = await analyzeResources(page)
      htmlCacheTest.resources = resources
      htmlCacheTest.steps[3].success = true

      // Calculer les métriques
      const improvement = ((firstVisitDuration - secondVisitDuration) / firstVisitDuration) * 100
      htmlCacheTest.metrics = {
        loadTime: firstVisitDuration,
        secondLoadTime: secondVisitDuration,
        improvement: improvement,
      }

      // Vérifier si le test est réussi
      htmlCacheTest.success = improvement > 20 // Au moins 20% d'amélioration

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "html-cache-test.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      htmlCacheTest.screenshot = screenshotPath
    } catch (error) {
      htmlCacheTest.error = error.message
      console.error("Erreur lors du test de mise en cache des pages HTML:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "html-cache-test-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      htmlCacheTest.screenshot = screenshotPath
    }

    results.tests.push(htmlCacheTest)
    results.totalTests++
    if (htmlCacheTest.success) results.testsPassed++

    // Test 2: Stratégie de mise en cache des images
    const imageCacheTest = {
      name: "Stratégie de mise en cache des images",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
      metrics: null,
      resources: null,
    }

    try {
      // Étape 1: Accéder à la page du tableau de bord (première visite)
      imageCacheTest.steps.push({ name: "Accès à la page du tableau de bord (première visite)", success: false })

      // Se connecter si nécessaire
      if (!page.url().includes("/dashboard")) {
        await page.goto(`${config.baseUrl}/`, { waitUntil: "networkidle2" })
        await page.waitForSelector('input[type="email"]')
        await page.type('input[type="email"]', config.loginEmail)
        await page.type('input[type="password"]', config.loginPassword)
        await Promise.all([page.waitForNavigation({ waitUntil: "networkidle2" }), page.click('button[type="submit"]')])
      }

      // Vider les performances
      await page.evaluate(() => performance.clearResourceTimings())

      const firstVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/dashboard`, { waitUntil: "networkidle2" })
      const firstVisitDuration = Date.now() - firstVisitStart
      imageCacheTest.steps[0].success = true

      // Étape 2: Analyser les ressources chargées (première visite)
      imageCacheTest.steps.push({ name: "Analyse des ressources chargées (première visite)", success: false })
      const firstVisitResources = await analyzeResources(page)
      imageCacheTest.steps[1].success = true

      // Étape 3: Accéder à la page du tableau de bord (deuxième visite)
      imageCacheTest.steps.push({ name: "Accès à la page du tableau de bord (deuxième visite)", success: false })

      // Vider les performances
      await page.evaluate(() => performance.clearResourceTimings())

      const secondVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/dashboard`, { waitUntil: "networkidle2" })
      const secondVisitDuration = Date.now() - secondVisitStart
      imageCacheTest.steps[2].success = true

      // Étape 4: Analyser les ressources chargées (deuxième visite)
      imageCacheTest.steps.push({ name: "Analyse des ressources chargées (deuxième visite)", success: false })
      const secondVisitResources = await analyzeResources(page)
      imageCacheTest.resources = secondVisitResources
      imageCacheTest.steps[3].success = true

      // Calculer les métriques
      const improvement = ((firstVisitDuration - secondVisitDuration) / firstVisitDuration) * 100
      imageCacheTest.metrics = {
        loadTime: firstVisitDuration,
        secondLoadTime: secondVisitDuration,
        improvement: improvement,
      }

      // Vérifier si le test est réussi
      imageCacheTest.success = improvement > 30 && secondVisitResources.image.cacheRate > 80

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "image-cache-test.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      imageCacheTest.screenshot = screenshotPath
    } catch (error) {
      imageCacheTest.error = error.message
      console.error("Erreur lors du test de mise en cache des images:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "image-cache-test-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      imageCacheTest.screenshot = screenshotPath
    }

    results.tests.push(imageCacheTest)
    results.totalTests++
    if (imageCacheTest.success) results.testsPassed++

    // Test 3: Stratégie de mise en cache des API
    const apiCacheTest = {
      name: "Stratégie de mise en cache des API",
      success: false,
      steps: [],
      error: null,
      screenshot: null,
      metrics: null,
    }

    try {
      // Étape 1: Accéder à la page des actualités (première visite)
      apiCacheTest.steps.push({ name: "Accès à la page des actualités (première visite)", success: false })

      // Vider les performances
      await page.evaluate(() => performance.clearResourceTimings())

      const firstVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/dashboard/news`, { waitUntil: "networkidle2" })
      const firstVisitDuration = Date.now() - firstVisitStart
      apiCacheTest.steps[0].success = true

      // Étape 2: Vérifier les requêtes API
      apiCacheTest.steps.push({ name: "Vérification des requêtes API", success: false })
      const apiRequests = await page.evaluate(() => {
        return performance
          .getEntriesByType("resource")
          .filter((resource) => resource.name.includes("firestore") || resource.name.includes("api"))
          .map((resource) => ({
            url: resource.name,
            duration: resource.duration,
            size: resource.transferSize,
          }))
      })

      if (apiRequests.length === 0) {
        throw new Error("Aucune requête API détectée")
      }

      apiCacheTest.steps[1].success = true

      // Étape 3: Accéder à la page des actualités (deuxième visite)
      apiCacheTest.steps.push({ name: "Accès à la page des actualités (deuxième visite)", success: false })

      // Vider les performances
      await page.evaluate(() => performance.clearResourceTimings())

      const secondVisitStart = Date.now()
      await page.goto(`${config.baseUrl}/dashboard/news`, { waitUntil: "networkidle2" })
      const secondVisitDuration = Date.now() - secondVisitStart
      apiCacheTest.steps[2].success = true

      // Étape 4: Vérifier les requêtes API (deuxième visite)
      apiCacheTest.steps.push({ name: "Vérification des requêtes API (deuxième visite)", success: false })
      const secondApiRequests = await page.evaluate(() => {
        return performance
          .getEntriesByType("resource")
          .filter((resource) => resource.name.includes("firestore") || resource.name.includes("api"))
          .map((resource) => ({
            url: resource.name,
            duration: resource.duration,
            size: resource.transferSize,
          }))
      })

      // Vérifier si les requêtes API sont mises en cache
      const cachedRequests = secondApiRequests.filter((request) => request.size === 0 || request.size < 100)
      const cacheRate = secondApiRequests.length > 0 ? (cachedRequests.length / secondApiRequests.length) * 100 : 0

      apiCacheTest.steps[3].success = true

      // Calculer les métriques
      const improvement = ((firstVisitDuration - secondVisitDuration) / firstVisitDuration) * 100
      apiCacheTest.metrics = {
        loadTime: firstVisitDuration,
        secondLoadTime: secondVisitDuration,
        improvement: improvement,
        apiRequests: apiRequests.length,
        secondApiRequests: secondApiRequests.length,
        cachedRequests: cachedRequests.length,
        cacheRate: cacheRate,
      }

      // Vérifier si le test est réussi
      apiCacheTest.success = improvement > 20 && cacheRate > 50

      // Prendre une capture d'écran
      const screenshotPath = path.join(config.screenshotDir, "api-cache-test.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      apiCacheTest.screenshot = screenshotPath
    } catch (error) {
      apiCacheTest.error = error.message
      console.error("Erreur lors du test de mise en cache des API:", error)

      // Prendre une capture d'écran en cas d'erreur
      const screenshotPath = path.join(config.screenshotDir, "api-cache-test-error.png")
      await page.screenshot({ path: screenshotPath, fullPage: true })
      apiCacheTest.screenshot = screenshotPath
    }

    results.tests.push(apiCacheTest)
    results.totalTests++
    if (apiCacheTest.success) results.testsPassed++

    // Calculer le résultat global
    results.success = results.testsPassed === results.totalTests
    results.duration = ((Date.now() - startTime) / 1000).toFixed(2)

    // Générer le rapport
    generateReport(results)
  } catch (error) {
    console.error("Erreur lors de l'exécution des tests:", error)
    results.error = error.message
  } finally {
    await browser.close()
  }

  return results
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  runTests()
    .then((results) => {
      console.log(`Tests terminés: ${results.testsPassed}/${results.totalTests} réussis`)
      process.exit(results.success ? 0 : 1)
    })
    .catch((error) => {
      console.error("Erreur lors de l'exécution des tests:", error)
      process.exit(1)
    })
}

module.exports = { runTests }
