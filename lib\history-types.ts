import type { Timestamp } from "firebase/firestore"

// Types communs pour l'historique des versions
export interface VersionMetadata {
  versionId: string
  createdAt: Timestamp
  createdBy: {
    uid: string
    displayName: string
  }
  description?: string
}

// Types spécifiques pour l'historique des actualités
export interface NewsVersionData {
  title: string
  summary?: string
  content: string
  imageUrl?: string
  showThumbnail?: boolean
  showContentImage?: boolean
  targetGroups: string[]
}

export interface NewsVersion extends VersionMetadata {
  newsId: string
  data: NewsVersionData
}

// Types spécifiques pour l'historique des pages
export interface PageVersionData {
  title: string
  slug: string
  content: string
  iconUrl?: string
  isPublished: boolean
  showFrame?: boolean
  targetGroups: string[]
}

export interface PageVersion extends VersionMetadata {
  pageId: string
  data: PageVersionData
}

// Enum pour les types de contenu
export enum ContentType {
  NEWS = "news",
  PAGE = "menuItems",
}
