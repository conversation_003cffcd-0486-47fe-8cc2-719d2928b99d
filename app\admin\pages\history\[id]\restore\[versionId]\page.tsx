"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowLeft, RotateCcw, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { doc, getDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { capturePageVersionBeforeUpdate } from "@/lib/history-utils"

// Définir ContentType directement dans ce fichier
enum ContentType {
  NEWS = "news",
  PAGE = "menuItems",
}

interface PageVersionRestoreProps {
  params: {
    id: string
    versionId: string
  }
}

export default function PageVersionRestorePage({ params }: PageVersionRestoreProps) {
  const { id, versionId } = params
  const router = useRouter()
  const { toast } = useToast()
  const [version, setVersion] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showConfirmation, setShowConfirmation] = useState(false)

  useEffect(() => {
    const fetchVersionDetails = async () => {
      try {
        setIsLoading(true)

        // Récupérer les détails de la version
        const baseCollection = ContentType.PAGE === ContentType.NEWS ? "news" : "menuItems"
        const versionDocRef =
          versionId === "current" ? doc(db(), baseCollection, id) : doc(db(), baseCollection, id, "versions", versionId)
        const versionDoc = await getDoc(versionDocRef)

        if (!versionDoc.exists()) {
          toast({
            title: "Erreur",
            description: "Version introuvable",
            variant: "destructive",
          })
          router.push(`/admin/pages/history/${id}`)
          return
        }

        setVersion(versionDoc.data())
      } catch (error) {
        console.error("Erreur lors du chargement de la version:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les détails de la version",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchVersionDetails()
  }, [id, versionId, router, toast])

  const handleRestoreVersion = async () => {
    try {
      setIsLoading(true)

      // Capturer la version actuelle avant la restauration
      await capturePageVersionBeforeUpdate(id, "Sauvegarde avant restauration vers une version antérieure")

      // Récupérer les données de la version à restaurer
      const baseCollection = ContentType.PAGE === ContentType.NEWS ? "news" : "menuItems"
      const versionDocRef = doc(db(), baseCollection, id, "versions", versionId)
      const versionDoc = await getDoc(versionDocRef)

      if (!versionDoc.exists()) {
        toast({
          title: "Erreur",
          description: "Version introuvable",
          variant: "destructive",
        })
        router.push(`/admin/pages/history/${id}`)
        return
      }

      const versionData = versionDoc.data().data

      // Mettre à jour l'article avec les données de la version
      const newsRef = doc(db(), "menuItems", id)
      await updateDoc(newsRef, {
        title: versionData.title,
        slug: versionData.slug,
        content: versionData.content,
        iconUrl: versionData.iconUrl,
        isPublished: versionData.isPublished,
        showFrame: versionData.showFrame,
        targetGroups: versionData.targetGroups,
        updatedAt: serverTimestamp(),
      })

      toast({
        title: "Succès",
        description: "La page a été restaurée avec succès",
      })

      router.push(`/admin/pages/edit/${id}`)
    } catch (error) {
      console.error("Erreur lors de la restauration de la version:", error)
      toast({
        title: "Erreur",
        description: "Impossible de restaurer la version",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-2 mb-6">
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-10 w-64 mb-6" />
        <div className="space-y-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    )
  }

  if (!version) return null

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour à l'historique
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Restaurer la version</CardTitle>
          <CardDescription>
            Êtes-vous sûr de vouloir restaurer cette version ? L'état actuel de la page sera sauvegardé avant la
            restauration.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-between">
          <div className="text-lg">
            Restaurer la version du{" "}
            {version.createdAt &&
              new Intl.DateTimeFormat("fr-FR", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              }).format(version.createdAt.toDate())}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button
              variant="destructive"
              onClick={handleRestoreVersion}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RotateCcw className="mr-2 h-4 w-4" />}
              Restaurer
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
