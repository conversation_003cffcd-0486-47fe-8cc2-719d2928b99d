"use client"

import { But<PERSON> } from "@/components/ui/button"
import { History } from "lucide-react"
import { useRouter } from "next/navigation"
import { ContentType } from "@/lib/history-types"

interface HistoryButtonProps {
  contentId: string
  contentType: ContentType
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

export function HistoryButton({
  contentId,
  contentType,
  variant = "outline",
  size = "icon", // Changé la valeur par défaut à "icon"
  className,
}: HistoryButtonProps) {
  const router = useRouter()

  const handleClick = () => {
    router.push(`/admin/${contentType === ContentType.NEWS ? "news" : "pages"}/history/${contentId}`)
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={className}
      title="Voir l'historique des versions"
    >
      <History className="h-4 w-4" />
    </Button>
  )
}
