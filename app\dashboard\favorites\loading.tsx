import { Skeleton } from "@/components/ui/skeleton"
import { Star } from "lucide-react"

export default function FavoritesLoading() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 border-b pb-4 mb-6">
        <Star className="h-6 w-6 text-yellow-500" />
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Mes favoris</h2>
          <p className="text-muted-foreground">Articles que vous avez ajoutés à vos favoris</p>
        </div>
      </div>

      <div className="grid gap-4 sm:gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="rounded-lg border p-0 overflow-hidden">
            <div className="flex flex-col sm:flex-row">
              <div className="w-full sm:w-32 h-24 sm:h-32">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="p-4 flex-1">
                <Skeleton className="h-6 w-2/3 mb-3" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-4/5 mb-4" />
                <div className="flex justify-end">
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
