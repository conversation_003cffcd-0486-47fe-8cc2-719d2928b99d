"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import { NewsFeedItem } from "@/components/news-feed-item"
import { Skeleton } from "@/components/ui/skeleton"
import { Star } from "lucide-react"
import { collection, getDocs, query, where, doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface NewsItem {
  id: string
  title: string
  content: string
  summary?: string
  imageUrl?: string
  createdAt: any
  isPinned: boolean
  showThumbnail?: boolean
}

export default function FavoritesPage() {
  const { user } = useAuth()
  const [favoriteItems, setFavoriteItems] = useState<NewsItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchFavorites = async () => {
      if (!user) return

      try {
        setIsLoading(true)

        // 1. Récupérer les IDs des favoris de l'utilisateur
        const userFavoritesRef = doc(db(), "userFavorites", user.uid)
        const userFavoritesDoc = await getDoc(userFavoritesRef)

        if (!userFavoritesDoc.exists()) {
          setFavoriteItems([])
          setIsLoading(false)
          return
        }

        const favoriteIds = userFavoritesDoc.data().newsIds || []

        if (favoriteIds.length === 0) {
          setFavoriteItems([])
          setIsLoading(false)
          return
        }

        // 2. Récupérer tous les articles publiés
        const newsRef = collection(db(), "news")
        const newsQuery = query(newsRef, where("isPublished", "==", true))
        const newsSnapshot = await getDocs(newsQuery)

        // 3. Filtrer les articles pour ne garder que les favoris
        const items: NewsItem[] = []
        newsSnapshot.forEach((doc) => {
          const data = doc.data()
          if (favoriteIds.includes(doc.id)) {
            items.push({
              id: doc.id,
              title: data.title,
              content: data.content,
              summary: data.summary,
              imageUrl: data.imageUrl,
              createdAt: data.createdAt,
              isPinned: data.isPinned || false,
              showThumbnail: data.showThumbnail,
            })
          }
        })

        // 4. Trier les articles (épinglés d'abord, puis par date)
        const sortedItems = items.sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1
          if (!a.isPinned && b.isPinned) return 1

          const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
          const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt)
          return dateB.getTime() - dateA.getTime()
        })

        setFavoriteItems(sortedItems)
      } catch (error) {
        console.error("Error fetching favorites:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchFavorites()
  }, [user])

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 border-b pb-4 mb-6">
        <Star className="h-6 w-6 text-yellow-500" />
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Mes favoris</h2>
        </div>
      </div>

      <div className="grid gap-4 sm:gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="rounded-lg border p-0 overflow-hidden">
              <div className="flex flex-col sm:flex-row">
                <div className="w-full sm:w-32 h-24 sm:h-32">
                  <Skeleton className="h-full w-full" />
                </div>
                <div className="p-4 flex-1">
                  <Skeleton className="h-6 w-2/3 mb-3" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-4/5 mb-4" />
                  <div className="flex justify-end">
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : favoriteItems.length > 0 ? (
          favoriteItems.map((item) => (
            <NewsFeedItem
              key={item.id}
              item={{
                ...item,
                date: item.createdAt?.toDate?.() || new Date(item.createdAt),
              }}
            />
          ))
        ) : (
          <div className="rounded-lg border p-8 text-center bg-gray-50/50 dark:bg-gray-900/50">
            <Star className="h-12 w-12 mx-auto mb-3 text-gray-400" />
            <h3 className="text-lg font-medium">Aucun favori</h3>
            <p className="text-muted-foreground mt-2">Vous n'avez pas encore ajouté d'articles à vos favoris</p>
          </div>
        )}
      </div>
    </div>
  )
}
