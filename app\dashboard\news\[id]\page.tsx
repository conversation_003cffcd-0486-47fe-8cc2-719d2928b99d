"use client"

import { useEffect, useState } from "react"
import { use<PERSON>outer } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { getUserGroups } from "@/lib/user-utils"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Calendar, Clock } from "lucide-react"
import { FavoriteButton } from "@/components/favorite-button"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { getDisplaySettings } from "@/lib/display-settings"
// Ajouter ces imports en haut du fichier
import { ContentModelRenderer } from "@/components/content-models/content-model-renderer"
import type { ContentModelType } from "@/components/content-models/content-type-selector"
import { OptimizedImage } from "@/components/optimized-image"

interface NewsDetailProps {
  params: {
    id: string
  }
}

// Modifier l'interface NewsItem pour inclure les nouveaux champs
interface NewsItem {
  id: string
  title: string
  content: string
  summary?: string
  imageUrl?: string
  createdAt: any
  updatedAt: any
  targetGroups: string[]
  isPinned: boolean
  isPublished: boolean
  showFrame?: boolean
  showContentImage?: boolean
  contentType?: ContentModelType
  modelData?: any
}

export default function NewsDetailPage({ params }: NewsDetailProps) {
  const { id } = params
  const router = useRouter()
  const { user } = useAuth()
  const [userGroups, setUserGroups] = useState<string[]>([])
  const [newsItem, setNewsItem] = useState<NewsItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [showDate, setShowDate] = useState(false)
  // Add error handling for image loading
  const [imageError, setImageError] = useState(false)

  // Add error handling for Firebase service unavailability in the news detail page
  // Update the useEffect hook to handle service errors

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return

      try {
        setIsLoading(true)

        // 1. Récupérer les groupes de l'utilisateur
        let groups: string[] = []
        try {
          groups = await getUserGroups(user.uid)
          setUserGroups(groups)
        } catch (groupError) {
          console.error("Erreur lors de la récupération des groupes:", groupError)
          // Fallback: utiliser les groupes en cache si disponibles
          const cachedGroups = localStorage.getItem(`user_groups_${user.uid}`)
          if (cachedGroups) {
            groups = JSON.parse(cachedGroups)
            setUserGroups(groups)
            console.log("Utilisation des groupes en cache:", groups)
          } else {
            // Si pas de cache, utiliser un groupe par défaut pour permettre l'accès
            groups = ["all"]
            setUserGroups(groups)
          }
        }

        // 2. Récupérer l'article
        try {
          const newsRef = doc(db(), "news", id)
          const newsDoc = await getDoc(newsRef)

          if (!newsDoc.exists()) {
            // Vérifier si nous avons une version en cache
            const cachedNews = localStorage.getItem(`news_${id}`)
            if (cachedNews) {
              const cachedItem = JSON.parse(cachedNews)
              setNewsItem(cachedItem)
              console.log("Utilisation de l'article en cache")

              // Vérifier l'autorisation avec les groupes disponibles
              const authorized =
                cachedItem.isPublished &&
                (cachedItem.targetGroups.includes("all") ||
                  cachedItem.targetGroups.some((group: string) => groups.includes(group)))

              setIsAuthorized(authorized)

              if (!authorized) {
                router.push("/dashboard")
              }
              return
            } else {
              router.push("/dashboard")
              return
            }
          }

          const data = newsDoc.data() as Omit<NewsItem, "id">
          const item = { id, ...data } as NewsItem
          setNewsItem(item)

          // Mettre en cache pour une utilisation hors ligne
          localStorage.setItem(`news_${id}`, JSON.stringify(item))

          // 3. Vérifier si l'utilisateur est autorisé à voir cet article
          const authorized =
            item.isPublished &&
            (item.targetGroups.includes("all") || item.targetGroups.some((group) => groups.includes(group)))

          setIsAuthorized(authorized)

          if (!authorized) {
            router.push("/dashboard")
          }
        } catch (newsError) {
          console.error("Erreur lors de la récupération de l'article:", newsError)

          // Vérifier si nous avons une version en cache
          const cachedNews = localStorage.getItem(`news_${id}`)
          if (cachedNews) {
            const cachedItem = JSON.parse(cachedNews)
            setNewsItem(cachedItem)
            console.log("Utilisation de l'article en cache suite à une erreur")

            // Vérifier l'autorisation avec les groupes disponibles
            const authorized =
              cachedItem.isPublished &&
              (cachedItem.targetGroups.includes("all") ||
                cachedItem.targetGroups.some((group: string) => groups.includes(group)))

            setIsAuthorized(authorized)

            if (!authorized) {
              router.push("/dashboard")
            }
          } else {
            // Si pas de cache, rediriger vers le tableau de bord
            router.push("/dashboard")
          }
        }
      } catch (error) {
        console.error("Error fetching news item:", error)
        // Tenter d'utiliser les données en cache comme dernier recours
        const cachedNews = localStorage.getItem(`news_${id}`)
        if (cachedNews) {
          try {
            const cachedItem = JSON.parse(cachedNews)
            setNewsItem(cachedItem)
            setIsAuthorized(true) // Autoriser en mode dégradé
            console.log("Mode dégradé: utilisation des données en cache")
          } catch (cacheError) {
            console.error("Erreur lors de l'utilisation du cache:", cacheError)
            router.push("/dashboard")
          }
        } else {
          router.push("/dashboard")
        }
      } finally {
        setIsLoading(false)
      }
    }

    const loadDisplaySettings = async () => {
      try {
        const settings = await getDisplaySettings()
        setShowDate(settings.showPublicationDates)
      } catch (error) {
        console.error("Erreur lors du chargement des paramètres d'affichage:", error)
        // Valeur par défaut en cas d'erreur
        setShowDate(true)
      }
    }

    loadDisplaySettings()
    fetchData()
  }, [id, user, router])

  // Function to format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return ""
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return format(date, "d MMMM yyyy", { locale: fr })
  }

  // Function to format time
  const formatTime = (timestamp: any) => {
    if (!timestamp) return ""
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return format(date, "HH:mm", { locale: fr })
  }

  // Function to process content and ensure HTML is properly rendered
  const processContent = (content: string) => {
    // Handle custom HTML blocks if needed
    return content
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Retour
          </Button>
        </div>
        <Skeleton className="h-10 w-3/4 mb-4" />
        <Skeleton className="h-6 w-1/2 mb-8" />
        <Skeleton className="h-64 w-full mb-6" />
        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      </div>
    )
  }

  if (!newsItem || !isAuthorized) return null

  // Determine if the content image should be shown
  const shouldShowContentImage = newsItem.showContentImage !== false && newsItem.imageUrl

  return (
    <div className="space-y-3 px-4 sm:px-6 md:px-8">
      <div className="flex justify-between items-center w-full">
        <Button variant="ghost" size="sm" onClick={() => router.back()} className="mb-0">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Retour
        </Button>
        {user && <FavoriteButton newsId={newsItem.id} userId={user.uid} size="lg" />}
      </div>

      <p className="text-sm text-muted-foreground font-medium">
        /<span className="ml-1">{newsItem.title}</span>
      </p>

      <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mb-3">
        {showDate && !isLoading && (
          <>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {formatDate(newsItem.createdAt)}
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {formatTime(newsItem.createdAt)}
            </div>
          </>
        )}
      </div>

      {/* Only show the image if showContentImage is true */}
      {newsItem.imageUrl && newsItem.showContentImage !== false && !imageError && (
        <div className="mb-3">
          <OptimizedImage
            src={newsItem.imageUrl}
            alt={newsItem.title}
            className="w-full max-h-96 rounded-lg"
            objectFit="cover"
            onError={() => setImageError(true)}
            priority={true}
            unoptimized={true} // Désactiver l'optimisation Next.js pour permettre le cache du service worker
            fallbackSrc="/placeholder.svg"
          />
        </div>
      )}

      {/* Remplacer le bloc qui commence par "newsItem.showFrame ? (" et se termine par ") : null" */}
      {newsItem.showFrame ? (
        <Card className="dark:border-gray-800">
          <CardContent className="p-4 sm:p-6">
            {newsItem.contentType && newsItem.contentType !== "richtext" ? (
              <ContentModelRenderer
                contentType={newsItem.contentType}
                content={newsItem.modelData}
                richTextContent={newsItem.content}
              />
            ) : (
              <div
                className="prose max-w-none prose-sm sm:prose dark:prose-invert"
                dangerouslySetInnerHTML={{
                  __html: newsItem.content ? processContent(newsItem.content) : "",
                }}
              />
            )}
          </CardContent>
        </Card>
      ) : newsItem.contentType && newsItem.contentType !== "richtext" ? (
        <ContentModelRenderer
          contentType={newsItem.contentType}
          content={newsItem.modelData}
          richTextContent={newsItem.content}
        />
      ) : (
        <div
          className="prose max-w-none prose-sm sm:prose dark:prose-invert"
          dangerouslySetInnerHTML={{
            __html: newsItem.content ? processContent(newsItem.content) : "",
          }}
        />
      )}
    </div>
  )
}
