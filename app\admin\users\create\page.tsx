"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db, auth } from "@/lib/firebase"
import { collection, serverTimestamp, getDocs, query, where, doc, getDoc, setDoc } from "firebase/firestore"
import { createUserWithEmailAndPassword } from "firebase/auth"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { useGroups } from "@/lib/hooks"
import { type Role, PREDEFINED_ROLES } from "@/lib/roles"
import { Checkbox } from "@/components/ui/checkbox"
import { PermissionGate } from "@/components/permission-gate"
import { PERMISSIONS } from "@/lib/permissions"

// Add the following imports
import { DEPARTMENTS } from "@/lib/commercial-types"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function CreateUserPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [displayName, setDisplayName] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [availableRoles, setAvailableRoles] = useState<Role[]>([])
  const { groups, loading: loadingGroups } = useGroups()

  // Add the following state variables
  const [department, setDepartment] = useState("")
  const [phone, setPhone] = useState("")
  const [jobTitle, setJobTitle] = useState("")

  useEffect(() => {
    fetchRoles()
  }, [])

  // Add a useEffect to update displayName when firstName or lastName changes
  useEffect(() => {
    setDisplayName(`${firstName} ${lastName}`.trim())
  }, [firstName, lastName])

  // Update the fetchRoles function to handle errors gracefully

  const fetchRoles = async () => {
    try {
      // Get predefined roles
      const predefinedRoles = Object.values(PREDEFINED_ROLES)

      try {
        // Get custom roles from Firestore
        const rolesDoc = await getDoc(doc(db, "settings", "roles"))
        let customRoles: Role[] = []

        if (rolesDoc.exists()) {
          customRoles = rolesDoc.data().roles || []
        } else {
          // If document doesn't exist, initialize it
          await setDoc(doc(db, "settings", "roles"), { roles: [] }, { merge: true })
        }

        // Combine predefined and custom roles
        setAvailableRoles([...predefinedRoles, ...customRoles])
      } catch (error) {
        console.error("Error fetching custom roles:", error)
        // Fall back to predefined roles
        setAvailableRoles(predefinedRoles)
      }
    } catch (error) {
      console.error("Erreur lors du chargement des rôles:", error)
      // Ensure we at least have predefined roles
      setAvailableRoles(Object.values(PREDEFINED_ROLES))
    }
  }

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups((prev) => (prev.includes(groupId) ? prev.filter((id) => id !== groupId) : [...prev, groupId]))
  }

  const handleRoleToggle = (roleId: string) => {
    // If toggling admin role, also update isAdmin state
    if (roleId === "admin") {
      setIsAdmin(!selectedRoles.includes("admin"))
    }

    setSelectedRoles((prev) => (prev.includes(roleId) ? prev.filter((id) => id !== roleId) : [...prev, roleId]))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password || !firstName || !lastName) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // Check if email already exists
      const emailQuery = query(collection(db, "users"), where("email", "==", email))
      const emailSnapshot = await getDocs(emailQuery)

      if (!emailSnapshot.empty) {
        toast({
          title: "Erreur",
          description: "Un utilisateur avec cette adresse email existe déjà",
          variant: "destructive",
        })
        return
      }

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const user = userCredential.user

      // Prepare user data
      const userData = {
        uid: user.uid,
        email,
        firstName,
        lastName,
        displayName: `${firstName} ${lastName}`.trim(),
        isAdmin,
        groups: selectedGroups,
        roles: selectedRoles,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        // Add the following to the userData object
        department: department,
        phone: phone,
        jobTitle: jobTitle,
      }

      // Add document to Firestore
      await setDoc(doc(db, "users", user.uid), userData)

      toast({
        title: "Succès",
        description: "L'utilisateur a été créé avec succès",
      })

      router.push("/admin/users")
    } catch (error: any) {
      console.error("Erreur lors de la création de l'utilisateur:", error)

      let errorMessage = "Une erreur est survenue lors de la création de l'utilisateur"

      if (error.code === "auth/email-already-in-use") {
        errorMessage = "Cette adresse email est déjà utilisée"
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "L'adresse email n'est pas valide"
      } else if (error.code === "auth/weak-password") {
        errorMessage = "Le mot de passe est trop faible"
      }

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <PermissionGate permissions={[PERMISSIONS.CREATE_USERS, PERMISSIONS.ADMIN]} anyPermission={true}>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Créer un nouvel utilisateur</h1>

        <form onSubmit={handleSubmit}>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Informations de l'utilisateur</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">Prénom *</Label>
                  <Input id="firstName" value={firstName} onChange={(e) => setFirstName(e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Nom *</Label>
                  <Input id="lastName" value={lastName} onChange={(e) => setLastName(e.target.value)} required />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} required />
              </div>

              {/* Add the following JSX after the email input */}
              <div className="space-y-2">
                <Label htmlFor="phone">Numéro de téléphone</Label>
                <Input id="phone" value={phone} onChange={(e) => setPhone(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobTitle">Fonction</Label>
                <Input id="jobTitle" value={jobTitle} onChange={(e) => setJobTitle(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Département</Label>
                <Select value={department} onValueChange={setDepartment}>
                  <SelectTrigger id="department">
                    <SelectValue placeholder="Sélectionner un département" />
                  </SelectTrigger>
                  <SelectContent>
                    {DEPARTMENTS.map((dept) => (
                      <SelectItem key={dept.code} value={dept.code}>
                        {dept.name} ({dept.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Mot de passe *</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <p className="text-sm text-muted-foreground">Le mot de passe doit contenir au moins 6 caractères</p>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Rôles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <Label htmlFor="isAdmin">Administrateur</Label>
                <Switch
                  id="isAdmin"
                  checked={isAdmin}
                  onCheckedChange={(checked) => {
                    setIsAdmin(checked)
                    if (checked && !selectedRoles.includes("admin")) {
                      setSelectedRoles((prev) => [...prev, "admin"])
                    } else if (!checked && selectedRoles.includes("admin")) {
                      setSelectedRoles((prev) => prev.filter((role) => role !== "admin"))
                    }
                  }}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4">
                {availableRoles.map((role) => (
                  <div key={role.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`role-${role.id}`}
                      checked={selectedRoles.includes(role.id)}
                      onCheckedChange={() => handleRoleToggle(role.id)}
                      disabled={role.id === "admin" && isAdmin} // Disable admin role checkbox if isAdmin is true
                    />
                    <div>
                      <Label htmlFor={`role-${role.id}`} className="font-medium">
                        {role.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">{role.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Groupes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loadingGroups ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : (
                <div className="space-y-2">
                  {groups.length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      Aucun groupe disponible. Veuillez d'abord créer des groupes.
                    </p>
                  ) : (
                    groups.map((group) => (
                      <div key={group.id} className="flex items-center space-x-2">
                        <Switch
                          id={`group-${group.id}`}
                          checked={selectedGroups.includes(group.id)}
                          onCheckedChange={() => handleGroupToggle(group.id)}
                        />
                        <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                      </div>
                    ))
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.push("/admin/users")} disabled={isLoading}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Créer l'utilisateur
            </Button>
          </div>
        </form>
      </div>
    </PermissionGate>
  )
}
