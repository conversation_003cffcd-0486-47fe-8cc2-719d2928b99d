import { cookies } from "next/headers"
import { initializeApp, getApps, cert, getApp, type App } from "firebase-admin/app"
import { getAuth, type DecodedIdToken } from "firebase-admin/auth"
import { getFirestore } from "firebase-admin/firestore"

// Type pour l'utilisateur côté serveur
export interface ServerUser {
  uid: string
  email: string | null
  displayName?: string | null
  photoURL?: string | null
}

// Variable globale pour suivre l'état d'initialisation
let isInitialized = false

/**
 * Récupère ou initialise l'application Firebase Admin
 * @returns L'instance de l'application Firebase Admin
 */
export function getFirebaseAdminApp(): App {
  if (!isInitialized && getApps().length === 0) {
    try {
      // Vérifier si nous avons les variables d'environnement requises
      if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_CLIENT_EMAIL || !process.env.FIREBASE_PRIVATE_KEY) {
        throw new Error("Variables d'environnement Firebase Admin SDK manquantes")
      }

      // S'assurer que la clé privée est correctement formatée
      const privateKey = process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n")

      // Initialiser l'application avec des identifiants explicites
      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: privateKey,
        }),
      })

      isInitialized = true
      console.log("Firebase Admin SDK initialisé avec succès via getFirebaseAdminApp")
    } catch (error) {
      console.error("Échec de l'initialisation de Firebase Admin SDK:", error)
      throw error
    }
  } else if (getApps().length > 0) {
    console.log("Firebase Admin SDK déjà initialisé, réutilisation de l'instance existante")
    isInitialized = true
  }

  return getApp()
}

// Fonction pour initialiser Firebase Admin
export function initializeFirebaseAdmin() {
  // Utiliser getFirebaseAdminApp pour garantir la cohérence
  getFirebaseAdminApp()

  return {
    auth: getAuth(),
    firestore: getFirestore(),
  }
}

/**
 * Récupère la session utilisateur à partir du cookie de session
 * @returns Objet contenant l'utilisateur et son statut d'administrateur
 */
export async function getUserSession(): Promise<{ user: ServerUser | null; isAdmin: boolean }> {
  const sessionCookie = cookies().get("__session")?.value

  if (!sessionCookie) {
    return { user: null, isAdmin: false }
  }

  try {
    // Initialiser Firebase Admin
    const { auth, firestore } = initializeFirebaseAdmin()

    // Vérifier le cookie de session. Mettre `true` pour vérifier la révocation.
    const decodedToken: DecodedIdToken = await auth.verifySessionCookie(sessionCookie, true)

    // Simplifier l'objet utilisateur retourné
    const user: ServerUser = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      displayName: decodedToken.name,
      photoURL: decodedToken.picture,
    }

    // Vérifier si l'utilisateur est administrateur (côté serveur)
    let isAdmin = false
    if (user.email) {
      const adminDocRef = firestore.collection("admins").doc(user.email)
      const adminDoc = await adminDocRef.get()
      isAdmin = adminDoc.exists
    }

    return { user, isAdmin }
  } catch (error: any) {
    // Le cookie est invalide (expiré, révoqué, etc.)
    console.error("Erreur lors de la vérification du cookie de session:", error)
    return { user: null, isAdmin: false }
  }
}
