"use client"

import { Workbox, messageSW } from "workbox-window"
import localforage from "localforage"

// Déclaration pour TypeScript
declare global {
  interface Window {
    workbox: Workbox | null
    cacheAuthData: (data: { token: string; uid: string }) => void
    swUpdateReady: boolean
    __WB_MANIFEST: Array<{ url: string; revision: string | null }>
    isOfflineSimulated: boolean
  }
}

// Configuration de localForage pour le cache du service worker
const swCache = localforage.createInstance({
  name: "acrDirect",
  storeName: "serviceWorkerCache",
})

/**
 * Enregistre le service worker pour l'application
 */
export function registerServiceWorker() {
  // Ne rien faire si nous ne sommes pas dans un navigateur
  if (typeof window === "undefined") return

  // Vérifier si nous sommes dans un navigateur qui supporte les Service Workers
  if (!("serviceWorker" in navigator)) {
    console.info("Les Service Workers ne sont pas supportés par ce navigateur.")
    return
  }

  // Vérifier si nous sommes en production
  const isProduction = process.env.NODE_ENV === "production"

  // Vérifier si nous sommes sur un domaine connu (pas un environnement de prévisualisation)
  const isKnownDomain =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1" ||
    window.location.hostname.endsWith("acrdirect.com") || // Remplacer par votre domaine réel
    window.location.hostname.endsWith("vercel.app") // Pour les déploiements Vercel

  // Ne pas enregistrer le Service Worker dans les environnements de prévisualisation ou de développement
  if (!isProduction && !isKnownDomain) {
    console.info("Service Worker désactivé: environnement de développement ou de prévisualisation.")
    return
  }

  // Vérifier si nous sommes dans un contexte sécurisé
  if (!window.isSecureContext) {
    console.info("Service Worker non enregistré: contexte non sécurisé. Les Service Workers nécessitent HTTPS.")
    return
  }

  // Utiliser l'événement load pour réduire l'impact sur les performances de chargement de la page
  if (document.readyState === "complete") {
    registerSW()
  } else {
    window.addEventListener("load", registerSW)
  }
}

function registerSW() {
  try {
    // Créer une nouvelle instance de Workbox
    const wb = new Workbox("/sw.js")
    window.workbox = wb

    // Fonction pour mettre en cache les données d'authentification
    window.cacheAuthData = async (data) => {
      try {
        // Stocker les données dans localForage
        await swCache.setItem("authData", data)

        // Envoyer les données au service worker
        if (wb.active) {
          messageSW(wb.active, {
            type: "CACHE_AUTH_DATA",
            authData: data,
          })
        }
      } catch (error) {
        console.error("Erreur lors de la mise en cache des données d'authentification:", error)
      }
    }

    // Écouter les mises à jour du service worker
    wb.addEventListener("waiting", (event) => {
      console.log("Une mise à jour du Service Worker est disponible!")
      window.swUpdateReady = true

      // Déclencher un événement personnalisé pour notifier l'application
      window.dispatchEvent(new CustomEvent("swUpdateReady"))
    })

    // Écouter les messages du service worker
    wb.addEventListener("message", (event) => {
      if (event.data && event.data.type === "CACHE_UPDATED") {
        console.log("Le cache a été mis à jour:", event.data.updatedURL)
      } else if (event.data && event.data.type === "OFFLINE_SIMULATION_CHANGED") {
        window.isOfflineSimulated = event.data.isOfflineSimulated
        console.log(`Simulation du mode hors ligne ${window.isOfflineSimulated ? "activée" : "désactivée"}`)

        // Déclencher un événement personnalisé pour notifier l'application
        window.dispatchEvent(
          new CustomEvent("offlineSimulationChanged", {
            detail: { isOfflineSimulated: window.isOfflineSimulated },
          }),
        )
      }
    })

    // Enregistrer le service worker
    wb.register()
      .then((registration) => {
        console.log("Service Worker enregistré avec succès:", registration)
      })
      .catch((error) => {
        console.error("Erreur lors de l'enregistrement du Service Worker:", error)
      })
  } catch (error) {
    console.error("Erreur lors de l'initialisation de Workbox:", error)
  }
}

/**
 * Précharge des images dans le cache
 * @param urls Liste des URLs d'images à précharger
 */
export function prefetchImages(urls: string[]): Promise<boolean> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    // Si le service worker n'est pas disponible, précharger manuellement
    return new Promise<boolean>((resolve) => {
      Promise.all(
        urls.map((url) => {
          return new Promise<void>((resolve) => {
            const img = new Image()
            img.onload = () => resolve()
            img.onerror = () => resolve() // Continuer même en cas d'erreur
            img.src = url
          })
        }),
      )
        .then(() => resolve(true))
        .catch(() => resolve(false))
    })
  }

  return new Promise<boolean>((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.success) {
        console.log("Images préchargées avec succès")
        resolve(true)
      } else {
        console.error("Erreur lors du préchargement des images")
        resolve(false)
      }
    }

    navigator.serviceWorker.controller.postMessage(
      {
        type: "CACHE_IMAGES",
        urls: urls,
      },
      [messageChannel.port2],
    )
  })
}

/**
 * Précharge des pages dans le cache
 * @param urls Liste des URLs de pages à précharger
 */
export function prefetchPages(urls: string[]): Promise<boolean> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    console.warn("Le Service Worker n'est pas actif, impossible de précharger les pages")
    return Promise.resolve(false)
  }

  return new Promise<boolean>((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.success) {
        console.log("Pages préchargées avec succès")
        resolve(true)
      } else {
        console.error("Erreur lors du préchargement des pages")
        resolve(false)
      }
    }

    navigator.serviceWorker.controller.postMessage(
      {
        type: "CACHE_PAGES",
        urls: urls,
      },
      [messageChannel.port2],
    )
  })
}

/**
 * Met à jour le service worker
 */
export function updateServiceWorker() {
  if (window.workbox && window.swUpdateReady) {
    window.workbox.addEventListener("controlling", () => {
      // Recharger la page pour utiliser la nouvelle version
      window.location.reload()
    })

    // Demander au service worker de prendre le contrôle immédiatement
    window.workbox.messageSkipWaiting()
  }
}

/**
 * Vérifie si une mise à jour du service worker est disponible
 */
export function checkForUpdates() {
  if (window.workbox) {
    window.workbox.update()
  }
}

/**
 * Vide un cache spécifique ou tous les caches
 * @param cacheName Nom du cache à vider (optionnel)
 */
export function clearCache(cacheName?: string): Promise<boolean> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    console.warn("Le Service Worker n'est pas actif, impossible de vider le cache")
    return Promise.resolve(false)
  }

  return new Promise<boolean>((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.success) {
        console.log(`Cache ${cacheName || "tous les caches"} vidé avec succès`)
        resolve(true)
      } else {
        console.error(`Erreur lors du vidage du cache ${cacheName || "tous les caches"}`)
        resolve(false)
      }
    }

    navigator.serviceWorker.controller.postMessage(
      {
        type: "CLEAR_CACHE",
        cacheName: cacheName,
      },
      [messageChannel.port2],
    )
  })
}

/**
 * Récupère les statistiques du cache
 */
export async function getCacheStats() {
  if (!("caches" in window)) {
    return { supported: false, caches: [] }
  }

  try {
    const cacheNames = await caches.keys()
    const cacheDetails = await Promise.all(
      cacheNames.map(async (name) => {
        const cache = await caches.open(name)
        const keys = await cache.keys()

        // Calculer la taille approximative
        let totalSize = 0
        for (const request of keys) {
          const response = await cache.match(request)
          if (response && response.headers.has("content-length")) {
            totalSize += Number.parseInt(response.headers.get("content-length") || "0", 10)
          }
        }

        // Grouper par type de ressource
        const resourceTypes: Record<string, number> = {}
        for (const request of keys) {
          const url = new URL(request.url)
          const extension = url.pathname.split(".").pop() || "unknown"
          resourceTypes[extension] = (resourceTypes[extension] || 0) + 1
        }

        return {
          name,
          size: keys.length,
          approximateSizeInBytes: totalSize,
          approximateSizeInKB: Math.round(totalSize / 1024),
          lastUpdated: new Date().toISOString(),
          resourceTypes,
        }
      }),
    )

    return {
      supported: true,
      caches: cacheDetails,
      totalItems: cacheDetails.reduce((acc, cache) => acc + cache.size, 0),
      totalSizeInKB: cacheDetails.reduce((acc, cache) => acc + cache.approximateSizeInKB, 0),
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques du cache:", error)
    return { supported: true, caches: [], error: String(error) }
  }
}

/**
 * Fonction pour mettre en cache des images
 * @param urls Liste des URLs d'images à mettre en cache
 */
export async function cacheImages(urls: string[]): Promise<void> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    // Si le service worker n'est pas disponible, précharger manuellement
    await Promise.all(
      urls.map((url) => {
        return new Promise<void>((resolve) => {
          const img = new Image()
          img.onload = () => resolve()
          img.onerror = () => resolve() // Continuer même en cas d'erreur
          img.src = url
        })
      }),
    )
    return
  }

  try {
    const cache = await caches.open("acr-direct-images-v1")
    const cachePromises = urls.map((url) =>
      cache.add(url).catch((error) => {
        console.warn(`Échec de mise en cache de ${url}: ${error}`)
      }),
    )
    await Promise.all(cachePromises)
    console.log("Images mises en cache avec succès:", urls)
  } catch (error) {
    console.error("Erreur lors de la mise en cache des images:", error)
  }
}

// Exporter la fonction d'enregistrement pour l'utiliser dans l'application
/**
 * Simule le mode hors ligne
 * @returns Promise<boolean> Indique si la simulation a été activée avec succès
 */
export function simulateOffline(): Promise<boolean> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    console.warn("Le Service Worker n'est pas actif, impossible de simuler le mode hors ligne")
    return Promise.resolve(false)
  }

  return new Promise<boolean>((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.success) {
        window.isOfflineSimulated = true
        console.log("Simulation du mode hors ligne activée")
        resolve(true)
      } else {
        console.error("Erreur lors de l'activation de la simulation du mode hors ligne")
        resolve(false)
      }
    }

    navigator.serviceWorker.controller.postMessage(
      {
        type: "SIMULATE_OFFLINE",
        value: true,
      },
      [messageChannel.port2],
    )
  })
}

/**
 * Arrête la simulation du mode hors ligne
 * @returns Promise<boolean> Indique si la simulation a été désactivée avec succès
 */
export function stopOfflineSimulation(): Promise<boolean> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    console.warn("Le Service Worker n'est pas actif, impossible d'arrêter la simulation du mode hors ligne")
    return Promise.resolve(false)
  }

  return new Promise<boolean>((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data && event.data.success) {
        window.isOfflineSimulated = false
        console.log("Simulation du mode hors ligne désactivée")
        resolve(true)
      } else {
        console.error("Erreur lors de la désactivation de la simulation du mode hors ligne")
        resolve(false)
      }
    }

    navigator.serviceWorker.controller.postMessage(
      {
        type: "SIMULATE_OFFLINE",
        value: false,
      },
      [messageChannel.port2],
    )
  })
}

/**
 * Vérifie si la simulation du mode hors ligne est supportée
 * @returns Promise<{supported: boolean, isOfflineSimulated: boolean}> Indique si la simulation est supportée et si elle est activée
 */
export function checkOfflineSimulationSupport(): Promise<{ supported: boolean; isOfflineSimulated: boolean }> {
  if (!("serviceWorker" in navigator) || !navigator.serviceWorker.controller) {
    console.warn("Le Service Worker n'est pas actif, la simulation du mode hors ligne n'est pas supportée")
    return Promise.resolve({ supported: false, isOfflineSimulated: false })
  }

  return new Promise((resolve) => {
    const messageChannel = new MessageChannel()

    messageChannel.port1.onmessage = (event) => {
      if (event.data) {
        resolve({
          supported: event.data.supported === true,
          isOfflineSimulated: event.data.isOfflineSimulated === true,
        })
      } else {
        resolve({ supported: false, isOfflineSimulated: false })
      }
    }

    // Définir un timeout au cas où le Service Worker ne répond pas
    const timeout = setTimeout(() => {
      resolve({ supported: false, isOfflineSimulated: false })
    }, 2000)

    navigator.serviceWorker.controller.postMessage(
      {
        type: "CHECK_OFFLINE_SIMULATION",
      },
      [messageChannel.port2],
    )

    // Nettoyer le timeout si nous recevons une réponse
    messageChannel.port1.onmessage = (event) => {
      clearTimeout(timeout)
      if (event.data) {
        resolve({
          supported: event.data.supported === true,
          isOfflineSimulated: event.data.isOfflineSimulated === true,
        })
      } else {
        resolve({ supported: false, isOfflineSimulated: false })
      }
    }
  })
}

export default registerServiceWorker
