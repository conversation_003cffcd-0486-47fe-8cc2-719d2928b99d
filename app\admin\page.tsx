"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { collection, getCountFromServer, query, orderBy, limit, getDocs } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Users, Newspaper, FileText, UserPlus, RefreshCw } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"

// Remplacer le composant AdminDashboard par cette version améliorée
export default function AdminDashboard() {
  const [stats, setStats] = useState({
    users: 0,
    news: 0,
    pages: 0,
    groups: 0,
  })
  const [activities, setActivities] = useState<
    {
      type: string
      message: string
      timestamp: any
      icon: React.ReactNode
    }[]
  >([])
  const [systemStatus, setSystemStatus] = useState<
    {
      service: string
      status: "operational" | "degraded" | "outage"
      details?: string
    }[]
  >([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Récupérer les statistiques de base
        const usersCount = await getCountFromServer(collection(db(), "users"))
        const newsCount = await getCountFromServer(collection(db(), "news"))
        const pagesCount = await getCountFromServer(collection(db(), "menuItems"))
        const groupsCount = await getCountFromServer(collection(db(), "groups"))

        setStats({
          users: usersCount.data().count,
          news: newsCount.data().count,
          pages: pagesCount.data().count,
          groups: groupsCount.data().count,
        })
      } catch (error) {
        console.error("Error fetching stats:", error)
      }
    }

    const fetchRecentActivities = async () => {
      try {
        // Récupérer les dernières activités (utilisateurs, actualités, pages)
        const recentActivities = []

        // Derniers utilisateurs créés
        const usersQuery = query(collection(db(), "users"), orderBy("createdAt", "desc"), limit(3))
        const usersSnapshot = await getDocs(usersQuery)
        usersSnapshot.forEach((doc) => {
          const userData = doc.data()
          recentActivities.push({
            type: "user",
            message: `Nouvel utilisateur : ${userData.displayName || userData.email}`,
            timestamp: userData.createdAt,
            icon: <UserPlus className="h-4 w-4 text-primary" />,
          })
        })

        // Dernières actualités publiées
        const newsQuery = query(collection(db(), "news"), orderBy("createdAt", "desc"), limit(3))
        const newsSnapshot = await getDocs(newsQuery)
        newsSnapshot.forEach((doc) => {
          const newsData = doc.data()
          recentActivities.push({
            type: "news",
            message: `Actualité publiée : ${newsData.title}`,
            timestamp: newsData.createdAt,
            icon: <Newspaper className="h-4 w-4 text-primary" />,
          })
        })

        // Dernières pages créées
        const pagesQuery = query(collection(db(), "menuItems"), orderBy("createdAt", "desc"), limit(3))
        const pagesSnapshot = await getDocs(pagesQuery)
        pagesSnapshot.forEach((doc) => {
          const pageData = doc.data()
          recentActivities.push({
            type: "page",
            message: `Page créée : ${pageData.title}`,
            timestamp: pageData.createdAt,
            icon: <FileText className="h-4 w-4 text-primary" />,
          })
        })

        // Trier toutes les activités par date
        recentActivities.sort((a, b) => {
          const dateA = a.timestamp?.toDate?.() || new Date(a.timestamp)
          const dateB = b.timestamp?.toDate?.() || new Date(b.timestamp)
          return dateB.getTime() - dateA.getTime()
        })

        // Prendre les 5 plus récentes
        setActivities(recentActivities.slice(0, 5))
      } catch (error) {
        console.error("Error fetching activities:", error)
      }
    }

    const checkSystemStatus = async () => {
      // Dans un environnement réel, vous pourriez appeler une API pour vérifier l'état des services
      // Ici, nous simulons des vérifications de base

      const status = [
        {
          service: "Firebase Auth",
          status: "operational" as const,
          details: "Temps de réponse moyen: 120ms",
        },
        {
          service: "Firestore Database",
          status: "operational" as const,
          details: "Utilisation: 12% de la limite quotidienne",
        },
        {
          service: "Firebase Storage",
          status: "operational" as const,
          details: "Espace utilisé: 45MB / 5GB",
        },
        {
          service: "Firebase Hosting",
          status: "operational" as const,
          details: "Dernier déploiement: il y a 2 jours",
        },
      ]

      // Vérifier si Firestore est accessible
      try {
        const testQuery = query(collection(db(), "users"), limit(1))
        await getDocs(testQuery)
      } catch (error) {
        // Si erreur, marquer Firestore comme dégradé
        const firestoreStatus = status.find((s) => s.service === "Firestore Database")
        if (firestoreStatus) {
          firestoreStatus.status = "degraded"
          firestoreStatus.details = "Problème de connexion détecté"
        }
      }

      setSystemStatus(status)
    }

    const fetchAllData = async () => {
      setLoading(true)
      await Promise.all([fetchStats(), fetchRecentActivities(), checkSystemStatus()])
      setLoading(false)
    }

    fetchAllData()
  }, [])

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)

    // Calculer la différence en minutes
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.round(diffMs / 60000)

    if (diffMins < 1) return "À l'instant"
    if (diffMins < 60) return `Il y a ${diffMins} minute${diffMins > 1 ? "s" : ""}`

    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `Il y a ${diffHours} heure${diffHours > 1 ? "s" : ""}`

    const diffDays = Math.floor(diffHours / 24)
    if (diffDays < 30) return `Il y a ${diffDays} jour${diffDays > 1 ? "s" : ""}`

    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "operational":
        return "bg-green-500"
      case "degraded":
        return "bg-yellow-500"
      case "outage":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "operational":
        return "Opérationnel"
      case "degraded":
        return "Performance dégradée"
      case "outage":
        return "Indisponible"
      default:
        return "Inconnu"
    }
  }

  return (
    <div className="space-y-6 px-2 sm:px-0">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Tableau de bord</h2>
      </div>

      <div className="grid gap-3 sm:gap-4 grid-cols-2 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold">{stats.users}</div>
                <p className="text-xs text-muted-foreground">Utilisateurs inscrits</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Actualités</CardTitle>
            <Newspaper className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold">{stats.news}</div>
                <p className="text-xs text-muted-foreground">Publications actives</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pages</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold">{stats.pages}</div>
                <p className="text-xs text-muted-foreground">Pages de contenu</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Groupes</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-xl sm:text-2xl font-bold">{stats.groups}</div>
                <p className="text-xs text-muted-foreground">Groupes d'utilisateurs</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent-activity">
        <TabsList className="bg-muted">
          <TabsTrigger value="recent-activity">Activité récente</TabsTrigger>
          <TabsTrigger value="system-status">État du système</TabsTrigger>
        </TabsList>
        <TabsContent value="recent-activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Activité récente</CardTitle>
              <CardDescription>Les dernières actions effectuées sur la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center">
                      <Skeleton className="h-10 w-10 rounded-full mr-4" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-3 w-[100px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : activities.length > 0 ? (
                <div className="space-y-4">
                  {activities.map((activity, index) => (
                    <div key={index} className="flex items-center">
                      <div className="mr-4 rounded-full bg-primary/10 p-2">{activity.icon}</div>
                      <div>
                        <p className="text-sm font-medium">{activity.message}</p>
                        <p className="text-xs text-muted-foreground">{formatDate(activity.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-4">Aucune activité récente</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="system-status">
          <Card>
            <CardHeader>
              <CardTitle>État du système</CardTitle>
              <CardDescription>Informations sur l'état actuel de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex items-center justify-between">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[100px]" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {systemStatus.map((service, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`mr-4 h-2 w-2 rounded-full ${getStatusColor(service.status)}`} />
                        <div>
                          <p className="text-sm font-medium">{service.service}</p>
                          {service.details && <p className="text-xs text-muted-foreground">{service.details}</p>}
                        </div>
                      </div>
                      <p
                        className={`text-xs ${service.status === "operational" ? "text-green-500" : service.status === "degraded" ? "text-yellow-500" : "text-red-500"}`}
                      >
                        {getStatusText(service.status)}
                      </p>
                    </div>
                  ))}
                  <div className="pt-4 text-xs text-muted-foreground">
                    <p>Dernière vérification: {new Date().toLocaleTimeString("fr-FR")}</p>
                    <Button variant="ghost" size="sm" className="mt-2 text-xs" onClick={() => window.location.reload()}>
                      <RefreshCw className="mr-1 h-3 w-3" />
                      Actualiser
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
