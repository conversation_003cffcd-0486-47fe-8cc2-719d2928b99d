"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, X } from "lucide-react"

interface GalleryImage {
  url: string
  altText: string
}

interface GalleryViewerProps {
  images: GalleryImage[]
  columns?: number
}

export function GalleryViewer({ images, columns = 4 }: GalleryViewerProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null)

  const openLightbox = (index: number) => {
    setSelectedImageIndex(index)
  }

  const closeLightbox = () => {
    setSelectedImageIndex(null)
  }

  const goToPrevious = () => {
    if (selectedImageIndex === null) return
    setSelectedImageIndex((selectedImageIndex - 1 + images.length) % images.length)
  }

  const goToNext = () => {
    if (selectedImageIndex === null) return
    setSelectedImageIndex((selectedImageIndex + 1) % images.length)
  }

  // Determine the grid columns class based on the columns prop
  const getGridColumnsClass = () => {
    switch (columns) {
      case 1:
        return "grid-cols-1"
      case 2:
        return "grid-cols-1 sm:grid-cols-2"
      case 3:
        return "grid-cols-2 sm:grid-cols-3"
      case 5:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
      case 6:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6"
      case 4:
      default:
        return "grid-cols-2 sm:grid-cols-3 md:grid-cols-4"
    }
  }

  return (
    <div>
      <div className={`grid ${getGridColumnsClass()} gap-4`}>
        {images.map((image, index) => (
          <div
            key={index}
            className="aspect-square overflow-hidden rounded-md cursor-pointer"
            onClick={() => openLightbox(index)}
          >
            <img
              src={image.url || "/placeholder.svg"}
              alt={image.altText || `Image ${index + 1}`}
              className="w-full h-full object-cover transition-transform hover:scale-110"
            />
          </div>
        ))}
      </div>

      <Dialog open={selectedImageIndex !== null} onOpenChange={(open) => !open && closeLightbox()}>
        <DialogContent className="max-w-screen-lg w-[90vw] h-[90vh] p-0 border-none bg-black/90">
          {selectedImageIndex !== null && (
            <div className="relative w-full h-full flex items-center justify-center">
              <img
                src={images[selectedImageIndex].url || "/placeholder.svg"}
                alt={images[selectedImageIndex].altText || `Image ${selectedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain"
              />
              <Button size="icon" variant="ghost" className="absolute top-4 right-4 text-white" onClick={closeLightbox}>
                <X className="h-6 w-6" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white"
                onClick={(e) => {
                  e.stopPropagation()
                  goToPrevious()
                }}
              >
                <ChevronLeft className="h-8 w-8" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white"
                onClick={(e) => {
                  e.stopPropagation()
                  goToNext()
                }}
              >
                <ChevronRight className="h-8 w-8" />
              </Button>
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 text-white">
                {selectedImageIndex + 1} / {images.length}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
