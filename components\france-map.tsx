"use client"

import type React from "react"

import { useState, useEffect, useCallback, useRef } from "react"
import { ComposableMap, Geographies, Geography, ZoomableGroup } from "react-simple-maps"
import { Skeleton } from "@/components/ui/skeleton"
import { getDepartmentName } from "@/lib/commercial-types"
import { But<PERSON> } from "@/components/ui/button"
import { ZoomIn, ZoomOut, RotateCcw } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

// France GeoJSON with departments
const FRANCE_GEO_URL = "https://raw.githubusercontent.com/gregoiredavid/france-geojson/master/departements.geojson"

interface FranceMapProps {
  selectedDepartment?: string
  onSelectDepartment: (departmentCode: string) => void
  highlightedDepartments?: string[]
}

export function FranceMap({ selectedDepartment, onSelectDepartment, highlightedDepartments = [] }: FranceMapProps) {
  const [loading, setLoading] = useState(true)
  const [geoData, setGeoData] = useState<any>(null)
  const [tooltipContent, setTooltipContent] = useState<{ content: string; x: number; y: number } | null>(null)
  const [position, setPosition] = useState({ coordinates: [2.2137, 46.2276], zoom: 1.5 })
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 })
  const mapRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Calculate optimal dimensions and scale based on container
  const calculateDimensions = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current
      const containerWidth = container.clientWidth
      const containerHeight = container.clientHeight

      // Ratio de la France (approximatif)
      const franceRatio = 0.85 // hauteur/largeur

      let width = containerWidth
      let height = containerWidth * franceRatio

      // Si la hauteur calculée dépasse la hauteur du container, on ajuste
      if (height > containerHeight) {
        height = containerHeight
        width = height / franceRatio
      }

      setDimensions({ width, height })
    }
  }, [])

  // Load GeoJSON data
  useEffect(() => {
    fetch(FRANCE_GEO_URL)
      .then((response) => response.json())
      .then((data) => {
        setGeoData(data)
        setLoading(false)
      })
      .catch((error) => {
        console.error("Error loading France map data:", error)
        setLoading(false)
      })
  }, [])

  // Calculate dimensions on mount and resize
  useEffect(() => {
    calculateDimensions()

    const resizeObserver = new ResizeObserver(() => {
      calculateDimensions()
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [calculateDimensions])

  // Reset position when dimensions change
  useEffect(() => {
    setPosition({ coordinates: [2.2137, 46.2276], zoom: 1.5 })
  }, [dimensions])

  const handleDepartmentClick = useCallback(
    (geo: any) => {
      const departmentCode = geo.properties.code
      onSelectDepartment(departmentCode)
    },
    [onSelectDepartment],
  )

  const handleZoomIn = () => {
    setPosition((pos) => ({ ...pos, zoom: Math.min(pos.zoom * 1.5, 8) }))
  }

  const handleZoomOut = () => {
    setPosition((pos) => ({ ...pos, zoom: Math.max(pos.zoom / 1.5, 1) }))
  }

  const handleReset = () => {
    setPosition({ coordinates: [2.2137, 46.2276], zoom: 1.5 })
  }

  const handleMouseEnter = (geo: any, e: React.MouseEvent) => {
    const departmentCode = geo.properties.code
    const departmentName = getDepartmentName(departmentCode)

    if (mapRef.current) {
      const rect = mapRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top - 40

      setTooltipContent({
        content: `${departmentName} (${departmentCode})`,
        x,
        y,
      })
    }
  }

  const handleMouseLeave = () => {
    setTooltipContent(null)
  }

  // Calculate projection scale based on container size
  const projectionScale = Math.min(dimensions.width, dimensions.height) * 4.5

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center min-h-[400px]">
        <Skeleton className="w-full h-full rounded-md" />
      </div>
    )
  }

  return (
    <div className="relative w-full h-full min-h-[400px]" ref={containerRef}>
      {/* Zoom controls */}
      <div className="absolute bottom-4 right-4 z-10 flex flex-col gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={handleZoomIn}
          title="Zoom avant"
          className="bg-background/90 backdrop-blur-sm shadow-lg border-2 hover:scale-105 transition-transform"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleZoomOut}
          title="Zoom arrière"
          className="bg-background/90 backdrop-blur-sm shadow-lg border-2 hover:scale-105 transition-transform"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleReset}
          title="Réinitialiser la vue"
          className="bg-background/90 backdrop-blur-sm shadow-lg border-2 hover:scale-105 transition-transform"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>

      {/* Tooltip */}
      <AnimatePresence>
        {tooltipContent && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute z-20 bg-background/95 backdrop-blur-sm border rounded-lg shadow-xl px-3 py-2 text-sm pointer-events-none font-medium"
            style={{
              left: `${tooltipContent.x}px`,
              top: `${tooltipContent.y}px`,
              transform: "translateX(-50%)",
            }}
          >
            {tooltipContent.content}
          </motion.div>
        )}
      </AnimatePresence>

      <div ref={mapRef} className="w-full h-full flex items-center justify-center">
        <ComposableMap
          projection="geoMercator"
          projectionConfig={{
            scale: projectionScale,
            center: [2.2137, 46.2276],
          }}
          width={dimensions.width}
          height={dimensions.height}
          style={{
            width: "100%",
            height: "100%",
            maxWidth: "100%",
            maxHeight: "100%",
          }}
        >
          <ZoomableGroup
            zoom={position.zoom}
            center={position.coordinates as [number, number]}
            maxZoom={8}
            minZoom={0.5}
            onMoveEnd={(position) => setPosition(position)}
            translateExtent={[
              [-dimensions.width * 0.5, -dimensions.height * 0.5],
              [dimensions.width * 1.5, dimensions.height * 1.5],
            ]}
          >
            {geoData && (
              <Geographies geography={geoData}>
                {({ geographies }) =>
                  geographies.map((geo) => {
                    const departmentCode = geo.properties.code
                    const isSelected = selectedDepartment === departmentCode

                    return (
                      <Geography
                        key={geo.rsmKey}
                        geography={geo}
                        onClick={() => handleDepartmentClick(geo)}
                        onMouseEnter={(e) => handleMouseEnter(geo, e)}
                        onMouseLeave={handleMouseLeave}
                        style={{
                          default: {
                            fill: isSelected ? "#3b82f6" : "#f1f5f9",
                            stroke: "#cbd5e1",
                            strokeWidth: 0.5,
                            outline: "none",
                            cursor: "pointer",
                            transition: "all 200ms ease-in-out",
                          },
                          hover: {
                            fill: isSelected ? "#2563eb" : "#e2e8f0",
                            stroke: "#94a3b8",
                            strokeWidth: 1,
                            outline: "none",
                            cursor: "pointer",
                            transition: "all 200ms ease-in-out",
                          },
                          pressed: {
                            fill: "#1d4ed8",
                            stroke: "#475569",
                            strokeWidth: 1,
                            outline: "none",
                            transition: "all 200ms ease-in-out",
                          },
                        }}
                      />
                    )
                  })
                }
              </Geographies>
            )}
          </ZoomableGroup>
        </ComposableMap>
      </div>
    </div>
  )
}
