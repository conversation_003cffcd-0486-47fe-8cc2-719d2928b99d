/**
 * Utility functions for accessibility
 */

// Generate a unique ID for ARIA attributes
export function generateAriaId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 9)}`
}

// Announce a message to screen readers
export function announceToScreenReader(message: string, politeness: "polite" | "assertive" = "polite"): void {
  if (typeof document === "undefined") return

  // Create or get the announcement element
  let announcer = document.getElementById("screen-reader-announcer")

  if (!announcer) {
    announcer = document.createElement("div")
    announcer.id = "screen-reader-announcer"
    announcer.setAttribute("aria-live", politeness)
    announcer.setAttribute("aria-atomic", "true")
    announcer.setAttribute("role", "status")
    announcer.style.position = "absolute"
    announcer.style.width = "1px"
    announcer.style.height = "1px"
    announcer.style.padding = "0"
    announcer.style.margin = "-1px"
    announcer.style.overflow = "hidden"
    announcer.style.clip = "rect(0, 0, 0, 0)"
    announcer.style.whiteSpace = "nowrap"
    announcer.style.border = "0"
    document.body.appendChild(announcer)
  }

  // Set the politeness level
  announcer.setAttribute("aria-live", politeness)

  // Clear the announcer
  announcer.textContent = ""

  // Set the message after a small delay to ensure it's announced
  setTimeout(() => {
    announcer.textContent = message
  }, 50)
}

/**
 * Checks if the user is navigating with a keyboard
 * @returns A function to check if the user is using a keyboard
 */
export function useKeyboardNavigation(): () => boolean {
  if (typeof document === "undefined") return () => false

  let isKeyboardUser = false

  // Set up event listeners if they don't exist yet
  if (!document.body.hasAttribute("data-keyboard-nav-initialized")) {
    document.body.setAttribute("data-keyboard-nav-initialized", "true")

    document.addEventListener("keydown", (e) => {
      if (e.key === "Tab") {
        isKeyboardUser = true
        document.body.classList.add("keyboard-user")
      }
    })

    document.addEventListener("mousedown", () => {
      isKeyboardUser = false
      document.body.classList.remove("keyboard-user")
    })
  }

  return () => isKeyboardUser
}

// Focus trap for modals and dialogs
export function createFocusTrap(containerElement: HTMLElement): {
  activate: () => void
  deactivate: () => void
} {
  let previousActiveElement: Element | null = null

  // Get all focusable elements
  const getFocusableElements = (): HTMLElement[] => {
    return Array.from(
      containerElement.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),
    ).filter((el) => !el.hasAttribute("disabled") && !el.getAttribute("aria-hidden")) as HTMLElement[]
  }

  // Handle tab key to trap focus
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key !== "Tab") return

    const focusableElements = getFocusableElements()
    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    if (event.shiftKey && document.activeElement === firstElement) {
      lastElement.focus()
      event.preventDefault()
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      firstElement.focus()
      event.preventDefault()
    }
  }

  return {
    activate: () => {
      previousActiveElement = document.activeElement

      // Set initial focus to the first focusable element
      const focusableElements = getFocusableElements()
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
      }

      // Add event listener
      document.addEventListener("keydown", handleKeyDown)
    },
    deactivate: () => {
      // Remove event listener
      document.removeEventListener("keydown", handleKeyDown)

      // Restore focus
      if (previousActiveElement && "focus" in previousActiveElement) {
        ;(previousActiveElement as HTMLElement).focus()
      }
    },
  }
}
