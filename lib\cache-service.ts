/**
 * Service centralisé de gestion du cache pour l'application
 * Ce service fournit une interface unifiée pour stocker et récupérer des données en cache
 * en utilisant différentes stratégies de stockage (IndexedDB, localStorage, etc.)
 */

import localforage, { type LocalForage } from "localforage"
import { hash as hashString } from "@/lib/utils"

// Durées de cache prédéfinies (en millisecondes)
export const CACHE_DURATIONS = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 2 * 60 * 60 * 1000, // 2 heures
  VERY_LONG: 24 * 60 * 60 * 1000, // 24 heures
  PERMANENT: 30 * 24 * 60 * 60 * 1000, // 30 jours
}

// Types d'entrées de cache
export type CacheStoreType = "firestore" | "auth" | "ui" | "api" | "images" | "pages"

// Options pour les opérations de cache
export interface CacheOptions {
  /** Durée de validité du cache en millisecondes */
  duration?: number
  /** Forcer le rafraîchissement (ignorer le cache) */
  forceRefresh?: boolean
  /** Version des données (pour invalidation sélective) */
  version?: string
  /** Source des données */
  source?: string
}

// Structure d'une entrée de cache
interface CacheEntry<T> {
  /** Données stockées */
  data: T
  /** Horodatage de création */
  timestamp: number
  /** Horodatage d'expiration */
  expiry: number
  /** Version des données */
  version?: string
  /** Source des données */
  source?: string
}

// Configuration des stores IndexedDB
const STORES: Record<CacheStoreType, localforage.LocalForageOptions> = {
  firestore: {
    name: "acrDirect",
    storeName: "firestoreCache",
    description: "Cache pour les données Firestore",
  },
  auth: {
    name: "acrDirect",
    storeName: "authCache",
    description: "Cache pour les données d'authentification",
  },
  ui: {
    name: "acrDirect",
    storeName: "uiCache",
    description: "Cache pour les préférences d'interface utilisateur",
  },
  api: {
    name: "acrDirect",
    storeName: "apiCache",
    description: "Cache pour les appels API",
  },
  images: {
    name: "acrDirect",
    storeName: "imageCache",
    description: "Cache pour les métadonnées d'images",
  },
  pages: {
    name: "acrDirect",
    storeName: "pageCache",
    description: "Cache pour les données de pages",
  },
}

// Classe pour la gestion du cache
class CacheService {
  private stores: Record<CacheStoreType, LocalForage>
  private initialized = false
  private initPromise: Promise<void> | null = null

  constructor() {
    this.stores = {} as Record<CacheStoreType, LocalForage>
    this.initPromise = this.initialize()
  }

  /**
   * Initialise les stores IndexedDB
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Vérifier si nous sommes dans un environnement navigateur
      if (typeof window === "undefined") {
        console.warn("CacheService: Tentative d'initialisation côté serveur, opération ignorée")
        this.initialized = true
        return
      }

      // Initialiser chaque store
      for (const [type, config] of Object.entries(STORES)) {
        this.stores[type as CacheStoreType] = localforage.createInstance(config)
      }

      this.initialized = true
      console.log("CacheService: Initialisation réussie")
    } catch (error) {
      console.error("CacheService: Erreur lors de l'initialisation", error)
      // Marquer comme initialisé même en cas d'erreur pour éviter les tentatives répétées
      this.initialized = true
    }
  }

  /**
   * S'assure que le service est initialisé avant d'exécuter une opération
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      if (this.initPromise) {
        await this.initPromise
      } else {
        await this.initialize()
      }
    }
  }

  /**
   * Stocke des données dans le cache
   * @param key Clé du cache
   * @param data Données à stocker
   * @param options Options de cache
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    try {
      await this.ensureInitialized()

      // Déterminer le type de store à partir de la clé
      const storeType = this.getStoreTypeFromKey(key)

      // Créer l'entrée de cache
      const cacheEntry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + (options.duration || CACHE_DURATIONS.MEDIUM),
        version: options.version,
        source: options.source,
      }

      // Stocker dans IndexedDB
      if (this.stores[storeType]) {
        await this.stores[storeType].setItem(key, cacheEntry)
      } else {
        console.warn(`CacheService: Store ${storeType} non disponible, utilisation du store par défaut`)
        await this.stores.ui.setItem(key, cacheEntry)
      }

      // Stocker également dans localStorage pour les données critiques (auth)
      if (storeType === "auth" && typeof localStorage !== "undefined") {
        try {
          localStorage.setItem(
            `cache_${key}`,
            JSON.stringify({
              timestamp: cacheEntry.timestamp,
              expiry: cacheEntry.expiry,
            }),
          )
        } catch (e) {
          // Ignorer les erreurs de localStorage (quota dépassé, etc.)
        }
      }

      // Enregistrer la mise à jour dans le service de cycle de vie
      // Importer dynamiquement pour éviter les dépendances circulaires
      const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
      cacheLifecycle.recordUpdate(storeType)

      // Si la clé concerne une collection, mettre à jour la version
      if (key.includes("collection:")) {
        const parts = key.split(":")
        if (parts.length >= 3) {
          const collection = parts[2]
          if (options.version) {
            cacheLifecycle.updateCollectionVersion(collection, options.version)
          }
        }
      }
    } catch (error) {
      console.error(`CacheService: Erreur lors du stockage de ${key}`, error)
    }
  }

  /**
   * Récupère des données du cache
   * @param key Clé du cache
   * @param options Options de cache
   * @returns Données en cache ou null si non trouvées ou expirées
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      await this.ensureInitialized()

      // Si forceRefresh est activé, ignorer le cache
      if (options.forceRefresh) {
        // Importer dynamiquement pour éviter les dépendances circulaires
        const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
        cacheLifecycle.recordCacheMiss()
        return null
      }

      // Déterminer le type de store à partir de la clé
      const storeType = this.getStoreTypeFromKey(key)

      // Récupérer depuis IndexedDB
      let cacheEntry: CacheEntry<T> | null = null

      if (this.stores[storeType]) {
        cacheEntry = await this.stores[storeType].getItem<CacheEntry<T>>(key)
      } else {
        console.warn(`CacheService: Store ${storeType} non disponible, utilisation du store par défaut`)
        cacheEntry = await this.stores.ui.getItem<CacheEntry<T>>(key)
      }

      // Vérifier si l'entrée existe et n'est pas expirée
      if (cacheEntry) {
        // Vérifier si la version correspond (si spécifiée)
        if (options.version && cacheEntry.version && options.version !== cacheEntry.version) {
          console.log(`CacheService: Version différente pour ${key}, cache ignoré`)
          // Importer dynamiquement pour éviter les dépendances circulaires
          const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
          cacheLifecycle.recordCacheMiss()
          return null
        }

        // Vérifier si l'entrée est expirée
        if (Date.now() > cacheEntry.expiry) {
          console.log(`CacheService: Entrée expirée pour ${key}`)
          await this.invalidate(key)
          // Importer dynamiquement pour éviter les dépendances circulaires
          const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
          cacheLifecycle.recordCacheMiss()
          return null
        }

        // Enregistrer un hit de cache
        // Importer dynamiquement pour éviter les dépendances circulaires
        const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
        cacheLifecycle.recordCacheHit()

        return cacheEntry.data
      }

      // Enregistrer un miss de cache
      // Importer dynamiquement pour éviter les dépendances circulaires
      const { cacheLifecycle } = await import("@/lib/cache-lifecycle")
      cacheLifecycle.recordCacheMiss()

      return null
    } catch (error) {
      console.error(`CacheService: Erreur lors de la récupération de ${key}`, error)
      return null
    }
  }

  /**
   * Invalide une entrée du cache
   * @param key Clé du cache à invalider
   */
  async invalidate(key: string): Promise<void> {
    try {
      await this.ensureInitialized()

      // Déterminer le type de store à partir de la clé
      const storeType = this.getStoreTypeFromKey(key)

      // Supprimer de IndexedDB
      if (this.stores[storeType]) {
        await this.stores[storeType].removeItem(key)
      } else {
        // Essayer de supprimer de tous les stores si le type n'est pas clair
        for (const store of Object.values(this.stores)) {
          await store.removeItem(key)
        }
      }

      // Supprimer également de localStorage pour les données critiques
      if (typeof localStorage !== "undefined") {
        localStorage.removeItem(`cache_${key}`)
      }
    } catch (error) {
      console.error(`CacheService: Erreur lors de l'invalidation de ${key}`, error)
    }
  }

  /**
   * Invalide toutes les entrées du cache correspondant à un préfixe
   * @param prefix Préfixe des clés à invalider
   */
  async invalidateByPrefix(prefix: string): Promise<void> {
    try {
      await this.ensureInitialized()

      // Déterminer le type de store à partir du préfixe
      const storeType = this.getStoreTypeFromKey(prefix)

      // Récupérer toutes les clés du store
      if (this.stores[storeType]) {
        await this.stores[storeType].iterate((value, key) => {
          if (key.startsWith(prefix)) {
            this.stores[storeType].removeItem(key)
          }
        })
      } else {
        // Si le type n'est pas clair, vérifier tous les stores
        for (const store of Object.values(this.stores)) {
          await store.iterate((value, key) => {
            if (key.startsWith(prefix)) {
              store.removeItem(key)
            }
          })
        }
      }

      // Supprimer également de localStorage pour les données critiques
      if (typeof localStorage !== "undefined") {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(`cache_${prefix}`)) {
            localStorage.removeItem(key)
          }
        }
      }
    } catch (error) {
      console.error(`CacheService: Erreur lors de l'invalidation par préfixe ${prefix}`, error)
    }
  }

  /**
   * Vide un store de cache spécifique
   * @param storeType Type de store à vider
   */
  async clear(storeType: CacheStoreType): Promise<void> {
    try {
      await this.ensureInitialized()

      if (this.stores[storeType]) {
        await this.stores[storeType].clear()
        console.log(`CacheService: Store ${storeType} vidé avec succès`)
      } else {
        console.warn(`CacheService: Store ${storeType} non disponible`)
      }

      // Vider également localStorage pour les données critiques
      if (storeType === "auth" && typeof localStorage !== "undefined") {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith("cache_auth:")) {
            localStorage.removeItem(key)
          }
        }
      }
    } catch (error) {
      console.error(`CacheService: Erreur lors du vidage du store ${storeType}`, error)
    }
  }

  /**
   * Vide tous les stores de cache
   */
  async clearAll(): Promise<void> {
    try {
      await this.ensureInitialized()

      // Vider chaque store
      for (const store of Object.values(this.stores)) {
        await store.clear()
      }

      console.log("CacheService: Tous les stores vidés avec succès")

      // Vider également les entrées de cache dans localStorage
      if (typeof localStorage !== "undefined") {
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i)
          if (key && key.startsWith("cache_")) {
            localStorage.removeItem(key)
          }
        }
      }
    } catch (error) {
      console.error("CacheService: Erreur lors du vidage de tous les stores", error)
    }
  }

  /**
   * Récupère les statistiques de cache pour chaque store
   * @returns Statistiques de cache
   */
  async getStats(): Promise<Record<string, { count: number; size: number }>> {
    try {
      await this.ensureInitialized()

      const stats: Record<string, { count: number; size: number }> = {}

      // Récupérer les statistiques pour chaque store
      for (const [type, store] of Object.entries(this.stores)) {
        let count = 0
        let size = 0

        await store.iterate((value) => {
          count++
          size += this.estimateSize(value)
        })

        stats[type] = { count, size }
      }

      return stats
    } catch (error) {
      console.error("CacheService: Erreur lors de la récupération des statistiques", error)
      return {}
    }
  }

  /**
   * Génère une clé de cache pour un document Firestore
   * @param collectionPath Chemin de la collection
   * @param documentId ID du document
   * @returns Clé de cache
   */
  getDocumentCacheKey(collectionPath: string, documentId: string): string {
    return `firestore:doc:${collectionPath}:${documentId}`
  }

  /**
   * Génère une clé de cache pour une collection Firestore
   * @param collectionPath Chemin de la collection
   * @param queryParams Paramètres de requête
   * @returns Clé de cache
   */
  getCollectionCacheKey(collectionPath: string, queryParams: any): string {
    // Créer une représentation stable des paramètres de requête
    const paramsString = JSON.stringify(queryParams, Object.keys(queryParams).sort())
    const paramsHash = hashString(paramsString)

    return `firestore:collection:${collectionPath}:${paramsHash}`
  }

  /**
   * Détermine le type de store à partir de la clé
   * @param key Clé du cache
   * @returns Type de store
   */
  private getStoreTypeFromKey(key: string): CacheStoreType {
    if (key.startsWith("firestore:")) return "firestore"
    if (key.startsWith("auth:")) return "auth"
    if (key.startsWith("ui:")) return "ui"
    if (key.startsWith("api:")) return "api"
    if (key.startsWith("image:")) return "images"
    if (key.startsWith("page:")) return "pages"

    // Par défaut, utiliser le store UI
    return "ui"
  }

  /**
   * Estime la taille en octets d'un objet JavaScript
   * @param object Objet à mesurer
   * @returns Taille approximative en octets
   */
  private estimateSize(object: any): number {
    const objectString = JSON.stringify(object)
    return objectString ? objectString.length * 2 : 0 // Approximation: 2 octets par caractère
  }
}

// Exporter une instance singleton du service
export const cacheService = new CacheService()
