// Script pour améliorer la persistance de l'authentification Firebase
// Ce script est chargé avant tout autre script pour assurer la persistance maximale
// et préparer l'environnement pour le fonctionnement hors ligne

;(() => {
  // Vérifier si nous sommes dans un navigateur
  if (typeof window === "undefined") return

  // Configuration
  const AUTH_PERSISTENCE_VERSION = "v3"
  const STORAGE_KEYS = {
    AUTH_STATUS: "auth_status",
    AUTH_USER_ID: "auth_user_id",
    AUTH_LAST_CHECK: "auth_last_check",
    AUTH_PERSISTENCE_HELPER_LOADED: "auth_persistence_helper_loaded",
    AUTH_PERSISTENCE_HELPER_LOADED_AT: "auth_persistence_helper_loaded_at",
    AUTH_PERSISTENCE_HELPER_VERSION: "auth_persistence_helper_version",
    SECURE_CONTEXT: "is_secure_context",
    SERVICE_WORKER_SUPPORTED: "service_worker_supported",
    INDEXED_DB_AVAILABLE: "indexed_db_available",
    OFFLINE_SUPPORT_ENABLED: "offline_support_enabled",
    LAST_ONLINE: "last_online_timestamp",
  }

  // If there's an import of 'hash', replace it with a native implementation
  // For example, if there's:
  // import { hash } from 'some-module';
  // Remove it and implement a simple hash function if needed:

  // Add this function if hash functionality is needed:
  function simpleHash(str) {
    let hash = 0
    if (str.length === 0) return hash
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }
    return hash.toString()
  }

  // Fonction pour vérifier si IndexedDB est disponible
  function isIndexedDBAvailable() {
    try {
      // Tester si IndexedDB est disponible
      return "indexedDB" in window && window.indexedDB !== null
    } catch (e) {
      return false
    }
  }

  // Fonction pour vérifier si localStorage est disponible
  function isLocalStorageAvailable() {
    try {
      const test = "__test__"
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch (e) {
      return false
    }
  }

  // Vérifier si nous sommes dans un contexte sécurisé (HTTPS ou localhost)
  const isSecureContext =
    window.isSecureContext || window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1"

  // Stocker l'information sur le contexte sécurisé
  if (isLocalStorageAvailable()) {
    localStorage.setItem("is_secure_context", isSecureContext.toString())
  }

  // Vérifier si le Service Worker est supporté
  const isServiceWorkerSupported = "serviceWorker" in navigator

  if (isLocalStorageAvailable()) {
    localStorage.setItem("service_worker_supported", isServiceWorkerSupported.toString())
  }

  // Vérifier si IndexedDB est disponible
  const isIDBAvailable = isIndexedDBAvailable()

  if (isLocalStorageAvailable()) {
    localStorage.setItem("indexed_db_available", isIDBAvailable.toString())
  }

  // Fonction pour stocker les données dans IndexedDB
  async function storeAuthDataInIndexedDB(user) {
    if (!isIndexedDBAvailable()) return false

    try {
      // Utiliser une promesse pour gérer l'opération asynchrone
      return new Promise((resolve, reject) => {
        const request = window.indexedDB.open("acrDirectAuth", 1)

        request.onupgradeneeded = (event) => {
          const db = event.target.result
          if (!db.objectStoreNames.contains("authData")) {
            db.createObjectStore("authData", { keyPath: "key" })
          }
        }

        request.onerror = (event) => {
          console.error("Erreur d'ouverture IndexedDB:", event.target.error)
          resolve(false)
        }

        request.onsuccess = (event) => {
          const db = event.target.result
          const transaction = db.transaction(["authData"], "readwrite")
          const store = transaction.objectStore("authData")

          // Stocker les données utilisateur de base
          const userData = {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            timestamp: Date.now(),
          }

          const storeRequest = store.put({
            key: "userData",
            value: userData,
            timestamp: Date.now(),
          })

          storeRequest.onsuccess = () => {
            resolve(true)
          }

          storeRequest.onerror = (event) => {
            console.error("Erreur de stockage IndexedDB:", event.target.error)
            resolve(false)
          }
        }
      })
    } catch (e) {
      console.error("Erreur lors du stockage dans IndexedDB:", e)
      return false
    }
  }

  // Fonction pour vérifier périodiquement l'état de l'authentification
  function setupAuthCheck() {
    // Cette fonction sera appelée une fois que Firebase Auth sera chargé
    window.checkAuthStatus = async () => {
      if (window.firebase && window.firebase.auth) {
        const user = window.firebase.auth().currentUser
        if (user) {
          console.log("Utilisateur authentifié:", user.uid)

          // Stocker l'état d'authentification
          if (isLocalStorageAvailable()) {
            localStorage.setItem(STORAGE_KEYS.AUTH_STATUS, "authenticated")
            localStorage.setItem(STORAGE_KEYS.AUTH_USER_ID, user.uid)
            localStorage.setItem(STORAGE_KEYS.AUTH_LAST_CHECK, Date.now().toString())
            localStorage.setItem(STORAGE_KEYS.LAST_ONLINE, Date.now().toString())
          }

          // Stocker dans IndexedDB pour une persistance maximale
          await storeAuthDataInIndexedDB(user)

          // Utiliser le script ultra-persistence si disponible
          if (window.forceMaximumPersistence) {
            window
              .forceMaximumPersistence()
              .then((result) => {
                if (result) {
                  console.log("Persistance maximale forcée avec succès via helper")
                }
              })
              .catch((error) => {
                console.error("Erreur lors du forçage de la persistance maximale via helper:", error)
              })
          }

          return true
        } else {
          console.log("Utilisateur non authentifié")

          if (isLocalStorageAvailable()) {
            localStorage.setItem(STORAGE_KEYS.AUTH_STATUS, "unauthenticated")
            localStorage.setItem(STORAGE_KEYS.AUTH_LAST_CHECK, Date.now().toString())
          }

          return false
        }
      }
      return false
    }

    // Vérifier périodiquement l'état d'authentification
    setInterval(
      () => {
        if (window.checkAuthStatus) {
          window.checkAuthStatus()
        }
      },
      3 * 60 * 1000, // Vérifier toutes les 3 minutes
    )

    // Vérifier l'état de la connexion
    window.addEventListener("online", () => {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.LAST_ONLINE, Date.now().toString())
      }

      // Vérifier l'authentification immédiatement quand on revient en ligne
      if (window.checkAuthStatus) {
        window.checkAuthStatus()
      }
    })
  }

  // Fonction pour activer le support hors ligne
  function enableOfflineSupport() {
    if (isLocalStorageAvailable()) {
      localStorage.setItem(STORAGE_KEYS.OFFLINE_SUPPORT_ENABLED, "true")
    }

    // Vérifier si le service worker est supporté
    if (isServiceWorkerSupported) {
      // Enregistrer le service worker d'authentification si ce n'est pas déjà fait
      if (typeof window.registerAuthServiceWorker === "function") {
        window.registerAuthServiceWorker()
      }
    }

    // Vérifier si nous sommes en ligne
    if (navigator.onLine) {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(STORAGE_KEYS.LAST_ONLINE, Date.now().toString())
      }
    }
  }

  // Exposer la fonction globalement
  window.enableOfflineSupport = enableOfflineSupport

  // Configurer la vérification d'authentification
  setupAuthCheck()

  // Activer le support hors ligne par défaut
  enableOfflineSupport()

  // Informer que le script a été chargé
  console.log("Auth persistence helper initialized (v3)")

  if (isLocalStorageAvailable()) {
    localStorage.setItem(STORAGE_KEYS.AUTH_PERSISTENCE_HELPER_LOADED, "true")
    localStorage.setItem(STORAGE_KEYS.AUTH_PERSISTENCE_HELPER_LOADED_AT, Date.now().toString())
    localStorage.setItem(STORAGE_KEYS.AUTH_PERSISTENCE_HELPER_VERSION, AUTH_PERSISTENCE_VERSION)
  }
})()
