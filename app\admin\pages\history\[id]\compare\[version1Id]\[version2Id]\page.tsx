import VersionComparison from "@/components/version-comparison"
import { ContentType } from "@/lib/history-utils"

interface PageVersionCompareProps {
  params: {
    id: string
    version1Id: string
    version2Id: string
  }
}

export default function PageVersionComparePage({ params }: PageVersionCompareProps) {
  // Default to comparing with the "current" version if version1Id is "current"
  const version1Id = "current"
  const version2Id = params.version2Id

  return (
    <VersionComparison
      contentType={ContentType.PAGE}
      contentId={params.id}
      version1Id={version1Id}
      version2Id={version2Id}
    />
  )
}
