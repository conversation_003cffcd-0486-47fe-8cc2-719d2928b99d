"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { cacheImages } from "@/app/sw-register"

interface ImagePreloaderProps {
  urls: string[]
  onComplete?: () => void
  onProgress?: (progress: number) => void
  children?: React.ReactNode
}

/**
 * Composant pour précharger des images et les mettre en cache
 * Ce composant est utile pour s'assurer que les images sont disponibles hors ligne
 */
export function ImagePreloader({ urls, onComplete, onProgress, children }: ImagePreloaderProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [progress, setProgress] = useState(0)
  const [loadedImages, setLoadedImages] = useState<string[]>([])
  const [failedImages, setFailedImages] = useState<string[]>([])

  useEffect(() => {
    if (!urls || urls.length === 0) {
      setIsLoading(false)
      setProgress(100)
      if (onComplete) onComplete()
      return
    }

    const preloadImages = async () => {
      setIsLoading(true)
      setProgress(0)

      const loaded: string[] = []
      const failed: string[] = []

      // Vérifier d'abord quelles images sont déjà en cache
      const cachedImages = await Promise.all(
        urls.map(async (url) => {
          try {
            const cache = await caches.open("acr-direct-images-v2.1.0")
            const response = await cache.match(url)
            return { url, cached: !!response }
          } catch (error) {
            console.error(`Erreur lors de la vérification du cache pour ${url}:`, error)
            return { url, cached: false }
          }
        }),
      )

      // Filtrer les images qui ne sont pas encore en cache
      const uncachedUrls = cachedImages.filter((item) => !item.cached).map((item) => item.url)

      // Marquer les images déjà en cache comme chargées
      const alreadyCachedUrls = cachedImages.filter((item) => item.cached).map((item) => item.url)

      if (alreadyCachedUrls.length > 0) {
        loaded.push(...alreadyCachedUrls)
        setLoadedImages(alreadyCachedUrls)
        const currentProgress = Math.round((alreadyCachedUrls.length / urls.length) * 100)
        setProgress(currentProgress)
        if (onProgress) onProgress(currentProgress)
      }

      // Si toutes les images sont déjà en cache, terminer
      if (uncachedUrls.length === 0) {
        setIsLoading(false)
        setProgress(100)
        if (onComplete) onComplete()
        return
      }

      // Précharger les images qui ne sont pas encore en cache
      try {
        // Utiliser le service worker pour mettre en cache les images
        if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
          await cacheImages(uncachedUrls)
          loaded.push(...uncachedUrls)
        } else {
          // Fallback si le service worker n'est pas disponible
          await Promise.all(
            uncachedUrls.map(
              (url) =>
                new Promise<void>((resolve) => {
                  const img = new Image()
                  img.onload = () => {
                    loaded.push(url)
                    setLoadedImages((prev) => [...prev, url])
                    const currentProgress = Math.round(((loaded.length + alreadyCachedUrls.length) / urls.length) * 100)
                    setProgress(currentProgress)
                    if (onProgress) onProgress(currentProgress)
                    resolve()
                  }
                  img.onerror = () => {
                    failed.push(url)
                    setFailedImages((prev) => [...prev, url])
                    resolve()
                  }
                  img.src = url
                }),
            ),
          )
        }
      } catch (error) {
        console.error("Erreur lors du préchargement des images:", error)
      }

      setIsLoading(false)
      setProgress(100)
      if (onProgress) onProgress(100)
      if (onComplete) onComplete()
    }

    preloadImages()
  }, [urls, onComplete, onProgress])

  // Ce composant ne rend rien visuellement, il précharge simplement les images
  return children || null
}

export default ImagePreloader
