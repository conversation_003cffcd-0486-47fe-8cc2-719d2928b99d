/**
 * Utilitaire pour tester la persistance de session
 */

// Fonction pour tester la persistance de session
export async function testSessionPersistence(): Promise<{
  success: boolean
  details: Record<string, any>
}> {
  try {
    // Vérifier si nous sommes dans un navigateur
    if (typeof window === "undefined") {
      return {
        success: false,
        details: { error: "Cette fonction ne peut être exécutée que dans un navigateur" },
      }
    }

    const details: Record<string, any> = {}

    // Vérifier si Firebase est initialisé
    details.firebaseInitialized = !!(window.firebase && window.firebase.auth)

    // Vérifier si un utilisateur est connecté
    details.userLoggedIn = !!(details.firebaseInitialized && window.firebase.auth().currentUser)

    if (details.userLoggedIn) {
      const user = window.firebase.auth().currentUser
      details.userId = user.uid

      // Tester le stockage localStorage
      try {
        const testKey = `test_persistence_${Date.now()}`
        localStorage.setItem(testKey, "test")
        const testValue = localStorage.getItem(testKey)
        localStorage.removeItem(testKey)

        details.localStorageWorking = testValue === "test"
      } catch (e) {
        details.localStorageWorking = false
        details.localStorageError = e.message
      }

      // Vérifier les données d'authentification stockées
      details.authDataStored = {
        uid: !!localStorage.getItem("auth_user_uid"),
        token: !!localStorage.getItem("auth_last_token"),
        timestamp: !!localStorage.getItem("auth_timestamp_v2"),
        lastActive: !!localStorage.getItem("auth_last_active"),
      }

      // Vérifier si IndexedDB est disponible
      try {
        details.indexedDBAvailable = "indexedDB" in window && !!window.indexedDB
      } catch (e) {
        details.indexedDBAvailable = false
        details.indexedDBError = e.message
      }

      // Vérifier si le Service Worker est actif
      try {
        details.serviceWorkerActive = "serviceWorker" in navigator && !!navigator.serviceWorker.controller
      } catch (e) {
        details.serviceWorkerActive = false
        details.serviceWorkerError = e.message
      }

      // Vérifier si nous sommes dans un contexte sécurisé
      try {
        details.secureContext =
          window.isSecureContext || window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1"
      } catch (e) {
        details.secureContext = false
        details.secureContextError = e.message
      }

      // Vérifier si l'application est installée
      try {
        if (
          window.matchMedia("(display-mode: standalone)").matches ||
          window.matchMedia("(display-mode: fullscreen)").matches
        ) {
          details.installStatus = "installée (standalone)"
        } else if (window.matchMedia("(display-mode: minimal-ui)").matches) {
          details.installStatus = "installée (minimal-ui)"
        } else if (window.navigator.standalone === true) {
          details.installStatus = "installée (iOS standalone)"
        } else {
          details.installStatus = "non installée"
        }
      } catch (e) {
        details.installStatus = "erreur de détection"
        details.installStatusError = e.message
      }

      // Forcer le rafraîchissement du token
      try {
        const token = await user.getIdToken(true)
        details.tokenRefreshSuccess = !!token
      } catch (e) {
        details.tokenRefreshSuccess = false
        details.tokenRefreshError = e.message
      }
    }

    // Déterminer le succès global du test
    const success =
      details.userLoggedIn &&
      details.localStorageWorking &&
      details.authDataStored.uid &&
      details.authDataStored.token &&
      details.tokenRefreshSuccess

    return {
      success,
      details,
    }
  } catch (error) {
    return {
      success: false,
      details: {
        error: error.message,
        stack: error.stack,
      },
    }
  }
}
