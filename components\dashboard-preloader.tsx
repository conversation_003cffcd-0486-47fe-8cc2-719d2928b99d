"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { getUserGroups, getUserMenuItems } from "@/lib/user-utils"
import { db } from "@/lib/firebase"
import { collection, getDocs, query, where, limit, doc, getDoc } from "firebase/firestore"
import { getAuth, signInWithCustomToken } from "firebase/auth"

interface DashboardPreloaderProps {
  children: React.ReactNode
}

export function DashboardPreloader({ children }: DashboardPreloaderProps) {
  const { user, userData } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [loadingMessage, setLoadingMessage] = useState("Chargement des données...")
  const [loadingStep, setLoadingStep] = useState(0)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true
    let retryCount = 0
    const maxRetries = 3

    // Fonction pour vérifier si les données essentielles sont en cache
    const checkCacheAndLoad = async () => {
      if (user && userData) {
        // Si l'utilisateur et ses données sont déjà disponibles, afficher immédiatement
        const cachedGroups = localStorage.getItem(`user_groups_${user.uid}`)
        if (cachedGroups) {
          console.log("Utilisateur et données déjà disponibles, affichage immédiat")
          if (isMounted) {
            setIsLoading(false)
            return
          }
        }
      }

      try {
        // Si l'utilisateur n'est pas connecté, essayer de restaurer la session
        if (!user) {
          setLoadingMessage("Chargement...")
          setLoadingStep(1)

          // Vérifier s'il y a une session dans localStorage
          const hasLocalSession = checkLocalSession()

          if (hasLocalSession) {
            console.log("Session locale trouvée, tentative de restauration...")
            await tryRestoreSession()

            // Si toujours pas d'utilisateur après restauration, afficher une erreur
            if (!user && retryCount >= maxRetries) {
              console.error("Impossible de restaurer la session après plusieurs tentatives")
              if (isMounted) {
                setError("Session expirée. Veuillez vous reconnecter.")
                setIsLoading(false)
              }
              return
            }
          } else {
            console.log("Aucune session locale trouvée, redirection vers la page de connexion...")
            window.location.href = "/"
            return
          }
        }

        console.log(`DashboardPreloader: Utilisateur connecté (${user?.uid}), chargement des données...`)
        setLoadingMessage("Chargement des groupes utilisateur...")
        setLoadingStep(2)

        // Vérifier si les groupes sont en cache
        const cachedGroups = localStorage.getItem(`user_groups_${user.uid}`)
        const cachedMenuItems = localStorage.getItem(`menu_items_${user.uid}`)

        // Si les données essentielles sont en cache, afficher immédiatement le contenu
        if (cachedGroups && cachedMenuItems) {
          console.log("Données essentielles trouvées en cache, affichage immédiat")
          if (isMounted) {
            setLoadingStep(4)
            setIsLoading(false)
          }

          // Précharger les données en arrière-plan pour les futures visites
          setTimeout(() => {
            preloadInBackground(user.uid)
          }, 100)

          return
        }

        // Si les données ne sont pas en cache, les charger rapidement
        console.log("Données essentielles non trouvées en cache, chargement rapide")
        setLoadingMessage("Chargement des données essentielles...")
        setLoadingStep(3)
        await loadEssentialData()

        if (isMounted) {
          setLoadingStep(4)
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Erreur lors de la vérification du cache:", error)
        if (isMounted) {
          setError("Erreur lors du chargement des données. Veuillez réessayer.")
          setIsLoading(false)
        }
      }
    }

    // Fonction pour vérifier s'il y a une session dans localStorage
    const checkLocalSession = () => {
      // Vérifier les différentes sources possibles de session
      const authUser = localStorage.getItem("auth_user")
      const firebaseUser = localStorage.getItem("firebase:authUser:AIzaSyDQYgJTXQKIYOC0LEXuTWaZCYSGYrMBXQs:[DEFAULT]")

      // Rechercher toutes les clés Firebase dans localStorage
      const firebaseKeys = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.includes("firebase:authUser") || key.includes("firebase:session"))) {
          firebaseKeys.push(key)
        }
      }

      return authUser || firebaseUser || firebaseKeys.length > 0
    }

    // Fonction pour essayer de restaurer la session
    const tryRestoreSession = async () => {
      try {
        // Essayer de récupérer un token personnalisé depuis le serveur
        const response = await fetch("/api/auth/get-custom-token", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "same-origin",
          cache: "no-store",
        })

        if (response.ok) {
          const data = await response.json()
          if (data.token) {
            // Utiliser le token pour se connecter à Firebase Auth
            const auth = getAuth()
            await signInWithCustomToken(auth, data.token)
            console.log("Session restaurée avec succès via token personnalisé")
            return true
          }
        }

        // Si la récupération du token échoue, incrémenter le compteur de tentatives
        retryCount++

        if (retryCount < maxRetries) {
          console.log(`Tentative de restauration échouée, nouvelle tentative (${retryCount}/${maxRetries})...`)
          // Attendre un peu avant de réessayer
          await new Promise((resolve) => setTimeout(resolve, 1000))
          return false
        } else {
          console.error("Nombre maximum de tentatives atteint")
          return false
        }
      } catch (error) {
        console.error("Erreur lors de la restauration de session:", error)
        return false
      }
    }

    // Fonction pour charger rapidement les données essentielles
    const loadEssentialData = async () => {
      try {
        if (!user) {
          console.error("Impossible de charger les données essentielles: utilisateur non connecté")
          return
        }

        // Charger les groupes et les éléments de menu en parallèle
        const [groups, menuItems] = await Promise.all([getUserGroups(user.uid), getUserMenuItems(user.uid, [])])

        console.log(`Groupes chargés: ${groups.join(", ")}`)
        console.log(`${menuItems.length} éléments de menu chargés`)

        // Stocker les groupes dans localStorage pour un accès rapide
        localStorage.setItem(`user_groups_${user.uid}`, JSON.stringify(groups))
        localStorage.setItem(`user_groups_timestamp_${user.uid}`, Date.now().toString())
      } catch (error) {
        console.error("Erreur lors du chargement des données essentielles:", error)
        throw error
      }
    }

    // Fonction pour précharger les données en arrière-plan
    const preloadInBackground = (userId: string) => {
      try {
        // Précharger les images principales
        const imagesToPreload = ["/logo-acr-direct.png", "/acr-icon.png"]
        imagesToPreload.forEach((src) => {
          const img = new Image()
          img.src = src
        })

        // Précharger les actualités et autres données non essentielles
        getUserGroups(userId).then((groups) => {
          if (!groups || groups.length === 0) return

          // Précharger les actualités
          const newsRef = collection(db(), "news")
          const newsQuery = query(newsRef, where("isPublished", "==", true), limit(10))
          getDocs(newsQuery)

          // Précharger les favoris
          const userFavoritesRef = doc(db(), "userFavorites", userId)
          getDoc(userFavoritesRef)
        })
      } catch (error) {
        console.error("Erreur lors du préchargement en arrière-plan:", error)
      }
    }

    // Définir un timeout maximum pour éviter de bloquer l'utilisateur trop longtemps
    const timeoutId = setTimeout(() => {
      if (isMounted) {
        console.log("Timeout de chargement atteint, affichage du contenu")
        setIsLoading(false)
      }
    }, 3000) // Maximum 3 secondes d'attente

    // Vérifier le cache et charger les données si nécessaire
    checkCacheAndLoad()

    // Nettoyage lors du démontage du composant
    return () => {
      isMounted = false
      clearTimeout(timeoutId)
    }
  }, [user, userData])

  // Afficher un écran de chargement pendant le préchargement
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background">
        <div className="flex flex-col items-center">
          <img src="/acr-icon.png" alt="ACR Direct" className="h-64 w-64 rounded-lg" />
          {error && (
            <div className="mt-4 text-red-500 text-center">
              <p>{error}</p>
              <button
                onClick={() => (window.location.href = "/")}
                className="mt-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
              >
                Retour à la connexion
              </button>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Afficher le contenu une fois le préchargement terminé
  return <>{children}</>
}
