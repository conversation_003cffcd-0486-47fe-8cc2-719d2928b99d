"use client"

import { useState, useEffect } from "react"
import { db } from "@/lib/firebase"
import { collection, query, orderBy, getDocs } from "firebase/firestore"

interface Group {
  id: string
  name: string
  description: string
}

export function useGroups() {
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    async function fetchGroups() {
      try {
        setLoading(true)
        const groupsQuery = query(collection(db(), "groups"), orderBy("name"))
        const querySnapshot = await getDocs(groupsQuery)

        const groupsData: Group[] = []
        querySnapshot.forEach((doc) => {
          groupsData.push({
            id: doc.id,
            ...doc.data(),
          } as Group)
        })

        setGroups(groupsData)
      } catch (err: any) {
        console.error("Error fetching groups:", err)
        setError(err)
      } finally {
        setLoading(false)
      }
    }

    fetchGroups()
  }, [])

  return { groups, loading, error }
}
