/**
 * Converts a hex color string to HSL format for CSS variables
 * @param hex Hex color string (e.g., "#3b82f6")
 * @returns HSL values as a string (e.g., "221 83% 53%")
 */
export function hexToHsl(hex: string): string {
  // Default to a safe color if hex is invalid
  if (!hex || !/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    console.warn(`Invalid hex color: ${hex}, defaulting to #000000`)
    hex = "#000000"
  }

  // Convert hex to RGB
  let r = 0,
    g = 0,
    b = 0
  if (hex.length === 4) {
    // 3 digits
    r = Number.parseInt(hex[1] + hex[1], 16)
    g = Number.parseInt(hex[2] + hex[2], 16)
    b = Number.parseInt(hex[3] + hex[3], 16)
  } else {
    // 6 digits
    r = Number.parseInt(hex.slice(1, 3), 16)
    g = Number.parseInt(hex.slice(3, 5), 16)
    b = Number.parseInt(hex.slice(5, 7), 16)
  }

  // Convert RGB to HSL
  r /= 255
  g /= 255
  b /= 255
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0,
    s = 0,
    l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  // Format HSL values for CSS
  h = Math.round(h * 360)
  s = Math.round(s * 100)
  l = Math.round(l * 100)

  return `${h} ${s}% ${l}%`
}

/**
 * Generates a color palette based on a primary color
 * @param primaryHex Primary color in hex format
 * @returns Object with color variations
 */
export function generateColorPalette(primaryHex: string) {
  const hsl = hexToHsl(primaryHex)
  const [h, s, l] = hsl.split(" ").map((v) => Number.parseFloat(v))

  return {
    50: hslToHex(h, Math.max(0, s - 30), Math.min(100, l + 40)),
    100: hslToHex(h, Math.max(0, s - 25), Math.min(100, l + 35)),
    200: hslToHex(h, Math.max(0, s - 20), Math.min(100, l + 25)),
    300: hslToHex(h, Math.max(0, s - 15), Math.min(100, l + 15)),
    400: hslToHex(h, Math.max(0, s - 10), Math.min(100, l + 5)),
    500: primaryHex,
    600: hslToHex(h, Math.min(100, s + 5), Math.max(0, l - 5)),
    700: hslToHex(h, Math.min(100, s + 10), Math.max(0, l - 15)),
    800: hslToHex(h, Math.min(100, s + 15), Math.max(0, l - 25)),
    900: hslToHex(h, Math.min(100, s + 20), Math.max(0, l - 35)),
    950: hslToHex(h, Math.min(100, s + 25), Math.max(0, l - 45)),
  }
}

// Add a function to convert HSL to hex for debugging
export function hslToHex(h: number, s: number, l: number): string {
  h /= 360
  s /= 100
  l /= 100
  let r, g, b

  if (s === 0) {
    r = g = b = l // achromatic
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1 / 6) return p + (q - p) * 6 * t
      if (t < 1 / 2) return q
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6
      return p
    }
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1 / 3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1 / 3)
  }

  const toHex = (x: number) => {
    const hex = Math.round(x * 255).toString(16)
    return hex.length === 1 ? "0" + hex : hex
  }

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * Checks if a color is light or dark
 * @param hex Hex color string
 * @returns Boolean indicating if the color is light
 */
export function isLightColor(color: string): boolean {
  // If it's already an HSL value in the format "h s% l%"
  if (color.includes("%")) {
    const parts = color.split(" ")
    if (parts.length === 3) {
      const l = Number.parseFloat(parts[2])
      return l > 50
    }
  }

  // For hex colors
  if (color.startsWith("#")) {
    const hex = color.substring(1)
    const rgb = Number.parseInt(hex, 16)
    const r = (rgb >> 16) & 0xff
    const g = (rgb >> 8) & 0xff
    const b = (rgb >> 0) & 0xff

    // Calculate relative luminance
    const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b
    return luminance > 128
  }

  // Default to false for unknown formats
  return false
}

/**
 * Determines appropriate text color (black or white) based on background color
 * @param bgHex Background color in hex format
 * @returns Text color in hex format (#000000 or #ffffff)
 */
export function getContrastTextColor(bgHex: string): string {
  return isLightColor(bgHex) ? "#000000" : "#ffffff"
}

/**
 * Generates a complementary color
 * @param hex Hex color string
 * @returns Complementary color in hex format
 */
export function getComplementaryColor(hex: string): string {
  const hsl = hexToHsl(hex)
  const [h, s, l] = hsl.split(" ").map((v) => Number.parseFloat(v))
  const newH = (h + 180) % 360
  return hslToHex(newH, s, l)
}

/**
 * Adjusts the lightness of an HSL color
 * @param hsl HSL color string (e.g., "221 83% 53%")
 * @param amount Amount to adjust the lightness by (e.g., -10 for 10% darker, 10 for 10% lighter)
 * @returns HSL color string with adjusted lightness
 */
export function adjustHslLightness(hsl: string, amount: number): string {
  const [h, s, l] = hsl.split(" ").map((v) => Number.parseFloat(v))
  const newL = Math.max(0, Math.min(100, l + amount))
  return `${h} ${s}% ${newL}%`
}
