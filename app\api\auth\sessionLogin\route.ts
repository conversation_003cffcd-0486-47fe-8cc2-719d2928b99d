import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { initializeFirebaseAdmin } from "@/lib/server-auth"

export async function POST(request: NextRequest) {
  try {
    console.log("[SessionLogin] Début de la création de session...")

    // Récupérer le token ID du corps de la requête
    let idToken
    try {
      const body = await request.json()
      idToken = body.idToken
      console.log(`[SessionLogin] Token ID reçu: ${idToken ? idToken.substring(0, 10) + "..." : "non défini"}`)
    } catch (parseError) {
      console.error("[SessionLogin] Erreur lors du parsing du corps de la requête:", parseError)
      return NextResponse.json(
        {
          error: "Invalid request body",
          details: parseError instanceof Error ? parseError.message : String(parseError),
        },
        { status: 400 },
      )
    }

    if (!idToken) {
      console.error("[SessionLogin] Aucun token ID fourni dans la requête")
      return NextResponse.json({ error: "ID token is required" }, { status: 400 })
    }

    // Initialiser Firebase Admin
    console.log("[SessionLogin] Initialisation de Firebase Admin...")
    const { auth } = initializeFirebaseAdmin()

    // Vérifier que le token ID est valide
    try {
      console.log("[SessionLogin] Vérification du token ID...")

      // Ajouter un timeout pour éviter les blocages
      const verifyPromise = auth.verifyIdToken(idToken)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout lors de la vérification du token")), 10000),
      )

      const decodedToken = (await Promise.race([verifyPromise, timeoutPromise])) as any

      console.log(`[SessionLogin] Token ID vérifié pour l'utilisateur: ${decodedToken.uid}`)

      // Créer un cookie de session
      console.log("[SessionLogin] Création du cookie de session...")
      const expiresIn = 60 * 60 * 24 * 5 * 1000 // 5 jours

      // Ajouter un timeout pour la création du cookie également
      const cookiePromise = auth.createSessionCookie(idToken, { expiresIn })
      const cookieTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout lors de la création du cookie de session")), 10000),
      )

      const sessionCookie = (await Promise.race([cookiePromise, cookieTimeoutPromise])) as string

      console.log("[SessionLogin] Cookie de session créé avec succès")

      // Définir les options du cookie
      const options = {
        name: "__session",
        value: sessionCookie,
        maxAge: expiresIn / 1000,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        path: "/",
        sameSite: "lax" as const,
      }

      // Définir le cookie
      cookies().set(options)
      console.log("[SessionLogin] Cookie de session défini avec succès")

      // Vérifier que le cookie a bien été défini
      const setCookie = cookies().get("__session")
      if (setCookie) {
        console.log("[SessionLogin] Vérification réussie: cookie __session présent")
      } else {
        console.warn("[SessionLogin] Avertissement: cookie __session non trouvé après définition")
      }

      return NextResponse.json(
        {
          status: "success",
          message: "Session created successfully",
          uid: decodedToken.uid,
        },
        { status: 200 },
      )
    } catch (error: any) {
      console.error("[SessionLogin] Erreur lors de la création du cookie de session:", error)

      if (error.code === "auth/id-token-expired") {
        return NextResponse.json(
          {
            error: "ID token has expired. Please log in again.",
            code: "token_expired",
          },
          { status: 401 },
        )
      }

      if (error.code === "auth/argument-error") {
        // Cela peut aussi arriver si le token est mal formé
        console.error("[SessionLogin] Erreur d'argument - token ID potentiellement invalide")
        return NextResponse.json(
          {
            error: "Invalid ID token provided.",
            code: "invalid_token",
          },
          { status: 401 },
        )
      }

      if (error.code === "auth/the-service-is-currently-unavailable") {
        console.error("[SessionLogin] Service Firebase temporairement indisponible")
        return NextResponse.json(
          {
            error: "Authentication service temporarily unavailable. Please try again later.",
            code: "service_unavailable",
            recoverable: true,
          },
          { status: 503 },
        )
      }

      // Erreur générique si ce n'est pas un code connu de Firebase Auth
      return NextResponse.json(
        {
          error: error.message || "Failed to create session",
          code: error.code || "unknown_error",
        },
        { status: 500 },
      )
    }
  } catch (error: any) {
    console.error("[SessionLogin] Exception globale:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
