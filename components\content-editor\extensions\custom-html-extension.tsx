import { Node, mergeAttributes } from "@tiptap/core"
import { ReactNodeViewRenderer } from "@tiptap/react"
import { CustomHtmlComponent } from "./custom-html-component"

export const CustomHtmlExtension = Node.create({
  name: "customHtml",

  group: "block",

  content: "inline*",

  atom: true,

  addAttributes() {
    return {
      content: {
        default: "",
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="custom-html"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ["div", mergeAttributes(HTMLAttributes, { "data-type": "custom-html" }), 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CustomHtmlComponent)
  },
})
