"use client"

import type React from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card } from "@/components/ui/card"
import { ImageIcon, Images, FileSlidersIcon as Slideshow, FileText, LayoutGrid } from "lucide-react"

export type ContentModelType = "richtext" | "single-image" | "gallery" | "carousel" | "grid"

interface ContentModel {
  id: ContentModelType
  name: string
  description: string
  icon: React.ReactNode
}

const contentModels: ContentModel[] = [
  {
    id: "richtext",
    name: "Texte enrichi",
    description: "Contenu texte avec mise en forme, images et liens",
    icon: <FileText className="h-8 w-8 text-primary" />,
  },
  {
    id: "single-image",
    name: "Image unique",
    description: "Une image plein écran avec zoom et options de partage",
    icon: <ImageIcon className="h-8 w-8 text-primary" />,
  },
  {
    id: "gallery",
    name: "Galerie d'images",
    description: "Mur d'images avec vignettes cliquables",
    icon: <Images className="h-8 w-8 text-primary" />,
  },
  {
    id: "carousel",
    name: "Carousel",
    description: "Diaporama d'images défilantes",
    icon: <Slideshow className="h-8 w-8 text-primary" />,
  },
  {
    id: "grid",
    name: "Grille de contenu",
    description: "Disposition en grille pour présenter plusieurs éléments",
    icon: <LayoutGrid className="h-8 w-8 text-primary" />,
  },
]

interface ContentTypeSelectorProps {
  value: ContentModelType
  onChange: (value: ContentModelType) => void
}

export function ContentTypeSelector({ value, onChange }: ContentTypeSelectorProps) {
  return (
    <div className="space-y-4">
      <Label className="text-base font-medium">Type de contenu</Label>
      <RadioGroup
        value={value}
        onValueChange={(val) => onChange(val as ContentModelType)}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        {contentModels.map((model) => (
          <Label key={model.id} htmlFor={`content-type-${model.id}`} className="cursor-pointer">
            <Card
              className={`p-4 h-full transition-all ${value === model.id ? "border-primary bg-primary/5" : "hover:border-primary/50"}`}
            >
              <RadioGroupItem id={`content-type-${model.id}`} value={model.id} className="sr-only" />
              <div className="flex flex-col h-full">
                <div className="flex items-center gap-3 mb-2">
                  {model.icon}
                  <span className="font-medium">{model.name}</span>
                </div>
                <p className="text-sm text-muted-foreground">{model.description}</p>
              </div>
            </Card>
          </Label>
        ))}
      </RadioGroup>
    </div>
  )
}
