"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, RefreshCw, Trash2, Download, Database, HardDrive, Wifi, WifiOff, CheckCircle2 } from "lucide-react"
import { clearCache, getCacheStats, prefetchImages, prefetchPages, updateServiceWorker } from "@/app/sw-register"
import { cacheService } from "@/lib/cache-service"
import { preloadAllData } from "@/lib/preload-service"
import { Progress } from "@/components/ui/progress"
import { useAuth } from "@/lib/hooks/use-auth"

interface CacheStats {
  name: string
  size: number
  approximateSizeInKB: number
  resourceTypes: Record<string, number>
}

export function CacheManager() {
  const { user } = useAuth()
  const [cacheStats, setCacheStats] = useState<CacheStats[]>([])
  const [totalCacheSize, setTotalCacheSize] = useState<number>(0)
  const [isLoading, setIsLoading] = useState(true)
  const [isClearing, setIsClearing] = useState(false)
  const [isPrefetching, setIsPrefetching] = useState(false)
  const [prefetchProgress, setPrefetchProgress] = useState(0)
  const [updateAvailable, setUpdateAvailable] = useState(false)
  const [isOnline, setIsOnline] = useState<boolean>(true)
  const [activeTab, setActiveTab] = useState("statistics")
  const [message, setMessage] = useState<{ type: "success" | "error" | "info"; text: string } | null>(null)

  // Formater la taille en KB, MB, etc.
  const formatSize = (sizeInKB: number): string => {
    if (sizeInKB < 1024) {
      return `${sizeInKB.toFixed(1)} KB`
    } else if (sizeInKB < 1024 * 1024) {
      return `${(sizeInKB / 1024).toFixed(1)} MB`
    } else {
      return `${(sizeInKB / (1024 * 1024)).toFixed(1)} GB`
    }
  }

  // Load cache statistics
  const loadCacheStats = async () => {
    setIsLoading(true)
    try {
      // Récupérer les statistiques du Service Worker
      const swStats = await getCacheStats()

      // Récupérer les statistiques de IndexedDB
      const dbStats = await cacheService.getStats()

      // Combiner les statistiques
      let allStats: CacheStats[] = []

      if ("caches" in swStats && Array.isArray(swStats.caches)) {
        allStats = [...swStats.caches]
      }

      // Ajouter les statistiques de IndexedDB
      Object.entries(dbStats).forEach(([name, stats]) => {
        allStats.push({
          name: `indexeddb-${name}`,
          size: stats.count,
          approximateSizeInKB: Math.round(stats.size / 1024),
          resourceTypes: {},
        })
      })

      // Calculer la taille totale
      const totalSize = allStats.reduce((total, cache) => total + (cache.approximateSizeInKB || 0), 0)
      setTotalCacheSize(totalSize)

      setCacheStats(allStats)
    } catch (error) {
      console.error("Error loading cache stats:", error)
      setMessage({ type: "error", text: "Erreur lors du chargement des statistiques du cache" })
    } finally {
      setIsLoading(false)
    }
  }

  // Clear a specific cache
  const handleClearCache = async (cacheName?: string) => {
    setIsClearing(true)
    try {
      if (cacheName) {
        if (cacheName.startsWith("indexeddb-")) {
          // Nettoyer un store IndexedDB spécifique
          const storeName = cacheName.replace("indexeddb-", "") as any
          await cacheService.clear(storeName)
        } else {
          // Nettoyer un cache du Service Worker
          await clearCache(cacheName)
        }
        setMessage({ type: "success", text: `Cache ${cacheName} vidé avec succès` })
      } else {
        // Nettoyer tous les caches
        await clearCache()
        await cacheService.clearAll()
        setMessage({ type: "success", text: "Tous les caches ont été vidés avec succès" })
      }

      // Recharger les statistiques
      await loadCacheStats()
    } catch (error) {
      console.error("Error clearing cache:", error)
      setMessage({ type: "error", text: "Erreur lors du vidage du cache" })
    } finally {
      setIsClearing(false)
    }
  }

  // Prefetch important resources
  const handlePrefetch = async () => {
    if (!user) {
      setMessage({ type: "error", text: "Vous devez être connecté pour précharger les données" })
      return
    }

    setIsPrefetching(true)
    setPrefetchProgress(0)

    try {
      setMessage({ type: "info", text: "Préchargement des ressources en cours..." })

      // Étape 1: Précharger les pages importantes (25%)
      setPrefetchProgress(5)
      const pagesToPrefetch = [
        "/",
        "/dashboard",
        "/offline",
        "/dashboard/news",
        "/dashboard/pages",
        "/dashboard/commercial",
      ]

      await prefetchPages(pagesToPrefetch)
      setPrefetchProgress(25)

      // Étape 2: Précharger les images importantes (50%)
      const imagesToPrefetch = [
        "/logo-acr-direct.png",
        "/android-chrome-192x192.png",
        "/android-chrome-512x512.png",
        "/apple-touch-icon.png",
        "/favicon-32x32.png",
        "/placeholder.svg",
      ]

      await prefetchImages(imagesToPrefetch)
      setPrefetchProgress(50)

      // Étape 3: Précharger les données utilisateur (100%)
      setMessage({ type: "info", text: "Préchargement des données utilisateur en cours..." })
      await preloadAllData(user.uid)
      setPrefetchProgress(100)

      setMessage({ type: "success", text: "Toutes les ressources ont été préchargées avec succès" })

      // Recharger les statistiques
      await loadCacheStats()
    } catch (error) {
      console.error("Error prefetching resources:", error)
      setMessage({ type: "error", text: "Erreur lors du préchargement des ressources" })
    } finally {
      setIsPrefetching(false)
    }
  }

  // Apply service worker update if available
  const handleUpdate = () => {
    updateServiceWorker()
    setMessage({ type: "success", text: "Mise à jour en cours..." })
  }

  // Vérifier l'état de la connexion
  const checkOnlineStatus = () => {
    setIsOnline(navigator.onLine)
  }

  // Check for service worker updates and online status
  useEffect(() => {
    // Check if an update is available
    setUpdateAvailable(!!window.swUpdateReady)

    // Listen for future updates
    const handleUpdateFound = () => setUpdateAvailable(true)
    window.addEventListener("swUpdateReady", handleUpdateFound)

    // Vérifier l'état de la connexion
    checkOnlineStatus()
    window.addEventListener("online", checkOnlineStatus)
    window.addEventListener("offline", checkOnlineStatus)

    // Load initial cache stats
    loadCacheStats()

    return () => {
      window.removeEventListener("swUpdateReady", handleUpdateFound)
      window.removeEventListener("online", checkOnlineStatus)
      window.removeEventListener("offline", checkOnlineStatus)
    }
  }, [])

  // Clear message after 8 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 8000)
      return () => clearTimeout(timer)
    }
  }, [message])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Gestion du Cache</span>
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <div className="flex items-center text-green-600 text-sm">
                <Wifi className="h-4 w-4 mr-1" />
                <span>En ligne</span>
              </div>
            ) : (
              <div className="flex items-center text-amber-600 text-sm">
                <WifiOff className="h-4 w-4 mr-1" />
                <span>Hors ligne</span>
              </div>
            )}
          </div>
        </CardTitle>
        <CardDescription>
          Gérez le cache de l'application pour optimiser les performances et le fonctionnement hors ligne
        </CardDescription>
      </CardHeader>
      <CardContent>
        {message && (
          <Alert
            className={`mb-4 ${
              message.type === "success"
                ? "bg-green-50 border-green-200"
                : message.type === "error"
                  ? "bg-red-50 border-red-200"
                  : "bg-blue-50 border-blue-200"
            }`}
          >
            <AlertTitle>
              {message.type === "success" ? "Succès" : message.type === "error" ? "Erreur" : "Information"}
            </AlertTitle>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        {updateAvailable && (
          <Alert className="mb-4 bg-blue-50 border-blue-200">
            <AlertTitle>Mise à jour disponible</AlertTitle>
            <AlertDescription className="flex items-center justify-between">
              <span>Une nouvelle version de l'application est disponible.</span>
              <Button onClick={handleUpdate} size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                Mettre à jour
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Afficher l'état de la connexion et la taille totale du cache */}
        <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-muted/20 p-4 rounded-lg flex items-center">
            <HardDrive className="h-8 w-8 mr-3 text-muted-foreground" />
            <div>
              <h3 className="font-medium">Taille totale du cache</h3>
              <p className="text-2xl font-bold">{formatSize(totalCacheSize)}</p>
            </div>
          </div>
          <div className="bg-muted/20 p-4 rounded-lg flex items-center">
            <Database className="h-8 w-8 mr-3 text-muted-foreground" />
            <div>
              <h3 className="font-medium">Caches actifs</h3>
              <p className="text-2xl font-bold">{cacheStats.length}</p>
            </div>
          </div>
        </div>

        <Tabs defaultValue="statistics" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4 grid grid-cols-3">
            <TabsTrigger value="statistics">Statistiques</TabsTrigger>
            <TabsTrigger value="prefetch">Préchargement</TabsTrigger>
            <TabsTrigger value="clear">Nettoyage</TabsTrigger>
          </TabsList>

          <TabsContent value="statistics">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Statistiques du cache</h3>
                <Button variant="outline" size="sm" onClick={loadCacheStats} disabled={isLoading}>
                  {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                  <span className="ml-2">Actualiser</span>
                </Button>
              </div>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="text-left p-2 pl-4">Nom du cache</th>
                        <th className="text-right p-2">Éléments</th>
                        <th className="text-right p-2">Taille</th>
                        <th className="text-right p-2 pr-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {cacheStats.length === 0 ? (
                        <tr>
                          <td colSpan={4} className="text-center py-8 text-muted-foreground">
                            Aucun cache trouvé
                          </td>
                        </tr>
                      ) : (
                        cacheStats.map((cache) => (
                          <tr key={cache.name} className="border-b last:border-0">
                            <td className="p-2 pl-4">{cache.name}</td>
                            <td className="text-right p-2">{cache.size}</td>
                            <td className="text-right p-2">
                              {cache.approximateSizeInKB ? formatSize(cache.approximateSizeInKB) : "N/A"}
                            </td>
                            <td className="text-right p-2 pr-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleClearCache(cache.name)}
                                disabled={isClearing}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="prefetch">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Préchargement des ressources</h3>
                <p className="text-sm text-muted-foreground">
                  Préchargez les ressources importantes pour améliorer les performances et le fonctionnement hors ligne.
                  Cette opération peut prendre quelques minutes selon votre connexion.
                </p>

                {isPrefetching && (
                  <div className="my-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progression</span>
                      <span>{prefetchProgress}%</span>
                    </div>
                    <Progress value={prefetchProgress} className="h-2" />
                  </div>
                )}

                <Button onClick={handlePrefetch} disabled={isPrefetching || !isOnline} className="mt-2 w-full">
                  {isPrefetching ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Préchargement en cours...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Précharger toutes les ressources
                    </>
                  )}
                </Button>

                {!isOnline && (
                  <p className="text-sm text-amber-600 mt-2">
                    Le préchargement n'est pas disponible en mode hors ligne.
                  </p>
                )}
              </div>

              <Alert className="mt-4 bg-blue-50 border-blue-200">
                <AlertTitle>Que fait le préchargement ?</AlertTitle>
                <AlertDescription className="text-sm">
                  <p>Cette opération va :</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Mettre en cache toutes les pages principales de l'application</li>
                    <li>Précharger les images et ressources statiques</li>
                    <li>Mettre en cache les données utilisateur (actualités, pages, etc.)</li>
                    <li>Préparer l'application pour une utilisation hors ligne</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="clear">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Vider le cache</h3>
                <p className="text-sm text-muted-foreground">
                  Videz le cache pour forcer le rechargement des ressources depuis le serveur. Cette action peut être
                  utile en cas de problème avec l'application.
                </p>
                <Button
                  variant="destructive"
                  onClick={() => handleClearCache()}
                  disabled={isClearing}
                  className="mt-2 w-full"
                >
                  {isClearing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Nettoyage en cours...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Vider tout le cache
                    </>
                  )}
                </Button>
              </div>

              <Alert className="mt-4 bg-amber-50 border-amber-200">
                <AlertTitle>Attention</AlertTitle>
                <AlertDescription className="text-sm">
                  <p>Vider le cache :</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Supprimera toutes les données mises en cache</li>
                    <li>Ralentira temporairement l'application (temps de rechargement)</li>
                    <li>Nécessitera une connexion internet pour recharger les données</li>
                    <li>N'affectera pas vos données utilisateur (favoris, préférences, etc.)</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-xs text-muted-foreground">Le cache est géré par Workbox, IndexedDB et le Service Worker</p>
        {isOnline ? (
          <p className="text-xs text-green-600 flex items-center">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Connecté au serveur
          </p>
        ) : (
          <p className="text-xs text-amber-600 flex items-center">
            <WifiOff className="h-3 w-3 mr-1" />
            Mode hors ligne
          </p>
        )}
      </CardFooter>
    </Card>
  )
}
