"use client"

import { motion } from "framer-motion"

interface LoadingScreenProps {
  message?: string
  minimal?: boolean
}

export function LoadingScreen({ message = "Chargement en cours...", minimal = false }: LoadingScreenProps) {
  // Version minimale pour un chargement plus discret
  if (minimal) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
        <div className="absolute inset-0 overflow-hidden z-0">
          <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center z-10"
        >
          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm inline-block">
            <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-28 md:h-36 w-auto mx-auto rounded-lg" />
          </div>
        </motion.div>
      </div>
    )
  }

  // Version complète avec indicateur de chargement et message
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 md:p-24 relative bg-gradient-to-b from-background to-muted/30">
      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="absolute -inset-[10%] bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute top-1/4 -right-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center z-10"
      >
        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm inline-block mb-4">
          <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-28 md:h-36 w-auto mx-auto rounded-lg" />
        </div>

        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-3 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-muted-foreground text-sm">{message}</p>
        </div>
      </motion.div>
    </div>
  )
}
