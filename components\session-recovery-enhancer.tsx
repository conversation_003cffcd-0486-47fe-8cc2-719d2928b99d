"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/hooks/use-auth"
import { recoverSession, forceLogoutAndReload } from "@/lib/session-recovery"

export function SessionRecoveryEnhancer() {
  const { user, refreshToken } = useAuth()
  const router = useRouter()
  const [lastRecoveryAttempt, setLastRecoveryAttempt] = useState<number>(0)
  const [recoveryCount, setRecoveryCount] = useState<number>(0)
  const [isInitialized, setIsInitialized] = useState<boolean>(false)

  // Fonction pour vérifier l'état du service worker d'authentification
  const checkAuthServiceWorker = async (): Promise<boolean> => {
    try {
      if (typeof window === "undefined" || !window.checkAuthServiceWorker) {
        return false
      }

      const status = await window.checkAuthServiceWorker()
      return status.isActive && status.authCacheExists
    } catch (error) {
      console.error("[Session Recovery] Erreur lors de la vérification du service worker:", error)
      return false
    }
  }

  // Fonction pour forcer la persistance maximale
  const forceMaximumPersistence = async (): Promise<boolean> => {
    try {
      if (typeof window === "undefined" || !window.forceMaximumPersistence) {
        return false
      }

      return await window.forceMaximumPersistence()
    } catch (error) {
      console.error("[Session Recovery] Erreur lors du forçage de la persistance maximale:", error)
      return false
    }
  }

  // Vérifier la session au chargement initial
  useEffect(() => {
    // Fonction pour vérifier et récupérer la session si nécessaire
    const checkAndRecoverSession = async () => {
      try {
        // Éviter les tentatives trop fréquentes
        const now = Date.now()
        if (now - lastRecoveryAttempt < 10000) {
          // 10 secondes minimum entre les tentatives
          return
        }

        setLastRecoveryAttempt(now)

        // Vérifier si l'utilisateur est connecté côté client
        if (user) {
          console.log("[Session Recovery] Utilisateur connecté côté client, vérification de la session serveur...")

          // Forcer la persistance maximale
          await forceMaximumPersistence()

          // Vérifier si la session est valide côté serveur
          const sessionResponse = await fetch("/api/auth/check-session")
          if (sessionResponse.ok) {
            const sessionData = await sessionResponse.json()

            // Si la session serveur n'est pas valide mais que l'utilisateur est connecté côté client
            if (!sessionData.authenticated) {
              console.log("[Session Recovery] Session serveur invalide, tentative de récupération...")
              setRecoveryCount((prev) => prev + 1)

              // Tenter de récupérer la session
              const recovered = await recoverSession()

              if (recovered) {
                console.log("[Session Recovery] Session récupérée avec succès")

                // Vérifier à nouveau après la récupération
                const verifyResponse = await fetch("/api/auth/check-session")
                if (verifyResponse.ok) {
                  const verifyData = await verifyResponse.json()
                  if (!verifyData.authenticated) {
                    console.log(
                      "[Session Recovery] Session toujours invalide après récupération, forçage de la persistance...",
                    )
                    await forceMaximumPersistence()
                  }
                }
              } else {
                console.log("[Session Recovery] Échec de la récupération de session")

                // Si trop de tentatives échouées, forcer la déconnexion
                if (recoveryCount > 3) {
                  console.log("[Session Recovery] Trop de tentatives échouées, déconnexion forcée")
                  await forceLogoutAndReload()
                } else {
                  console.log("[Session Recovery] Rechargement de la page")
                  router.refresh()
                }
              }
            } else {
              console.log("[Session Recovery] Session valide côté client et serveur")

              // Vérifier le service worker d'authentification
              const swActive = await checkAuthServiceWorker()
              if (!swActive) {
                console.log(
                  "[Session Recovery] Service worker d'authentification inactif, forçage de la persistance...",
                )
                await forceMaximumPersistence()
              }
            }
          }
        } else if (typeof window !== "undefined") {
          // Vérifier si une session est stockée localement
          if (window.attemptSessionRestore) {
            console.log("[Session Recovery] Tentative de restauration de session depuis le stockage local...")
            const result = await window.attemptSessionRestore()
            if (result) {
              console.log("[Session Recovery] Session restaurée depuis le stockage local, rechargement...")
              router.refresh()
            }
          }
        }

        setIsInitialized(true)
      } catch (error) {
        console.error("[Session Recovery] Erreur lors de la vérification initiale de la session:", error)
      }
    }

    // Exécuter la vérification après un court délai pour laisser le temps à Firebase de s'initialiser
    const timer = setTimeout(() => {
      checkAndRecoverSession()
    }, 1500)

    return () => clearTimeout(timer)
  }, [user, router, lastRecoveryAttempt, recoveryCount])

  // Configurer un intervalle pour rafraîchir périodiquement la session
  useEffect(() => {
    if (!user || !isInitialized) return

    // Déterminer l'intervalle en fonction du type d'appareil
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const refreshInterval = isMobile ? 3 * 60 * 1000 : 10 * 60 * 1000 // 3 minutes sur mobile, 10 minutes sur desktop

    console.log(
      `[Session Recovery] Configuration du rafraîchissement périodique toutes les ${refreshInterval / 60000} minutes`,
    )

    const intervalId = setInterval(async () => {
      try {
        console.log("[Session Recovery] Rafraîchissement périodique de la session...")

        // Rafraîchir le token
        await refreshToken()

        // Forcer la persistance maximale
        if (typeof window !== "undefined" && window.forceMaximumPersistence) {
          await window.forceMaximumPersistence()
        }

        // Vérifier le service worker d'authentification
        await checkAuthServiceWorker()
      } catch (error) {
        console.error("[Session Recovery] Erreur lors du rafraîchissement périodique:", error)
      }
    }, refreshInterval)

    return () => clearInterval(intervalId)
  }, [user, refreshToken, isInitialized])

  // Configurer un écouteur pour les changements de visibilité de la page
  useEffect(() => {
    if (!user || !isInitialized) return

    const handleVisibilityChange = async () => {
      if (document.visibilityState === "visible") {
        try {
          // Éviter les tentatives trop fréquentes
          const now = Date.now()
          if (now - lastRecoveryAttempt < 10000) {
            // 10 secondes minimum entre les tentatives
            return
          }

          console.log("[Session Recovery] Application redevenue visible, vérification de la session...")
          setLastRecoveryAttempt(now)

          // Vérifier si la session est valide côté serveur
          const sessionResponse = await fetch("/api/auth/check-session")
          if (sessionResponse.ok) {
            const sessionData = await sessionResponse.json()

            // Si la session serveur n'est pas valide mais que l'utilisateur est connecté côté client
            if (!sessionData.authenticated) {
              console.log(
                "[Session Recovery] Session serveur invalide après retour visible, tentative de récupération...",
              )

              // Tenter de récupérer la session
              await recoverSession()

              // Forcer la persistance maximale
              if (typeof window !== "undefined" && window.forceMaximumPersistence) {
                await window.forceMaximumPersistence()
              }
            } else {
              console.log("[Session Recovery] Session valide après retour visible")
            }
          }
        } catch (error) {
          console.error("[Session Recovery] Erreur lors de la vérification après retour visible:", error)
        }
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => document.removeEventListener("visibilitychange", handleVisibilityChange)
  }, [user, lastRecoveryAttempt, isInitialized])

  // Configurer un écouteur pour les événements de connexion réseau
  useEffect(() => {
    if (!user || !isInitialized) return

    const handleOnline = async () => {
      try {
        console.log("[Session Recovery] Connexion réseau rétablie, vérification de la session...")

        // Éviter les tentatives trop fréquentes
        const now = Date.now()
        if (now - lastRecoveryAttempt < 10000) {
          // 10 secondes minimum entre les tentatives
          return
        }

        setLastRecoveryAttempt(now)

        // Vérifier si la session est valide côté serveur
        const sessionResponse = await fetch("/api/auth/check-session")
        if (sessionResponse.ok) {
          const sessionData = await sessionResponse.json()

          // Si la session serveur n'est pas valide mais que l'utilisateur est connecté côté client
          if (!sessionData.authenticated) {
            console.log(
              "[Session Recovery] Session serveur invalide après retour en ligne, tentative de récupération...",
            )

            // Tenter de récupérer la session
            await recoverSession()

            // Forcer la persistance maximale
            if (typeof window !== "undefined" && window.forceMaximumPersistence) {
              await window.forceMaximumPersistence()
            }
          } else {
            console.log("[Session Recovery] Session valide après retour en ligne")
          }
        }
      } catch (error) {
        console.error("[Session Recovery] Erreur lors de la vérification après retour en ligne:", error)
      }
    }

    window.addEventListener("online", handleOnline)

    return () => window.removeEventListener("online", handleOnline)
  }, [user, lastRecoveryAttempt, isInitialized])

  // Ce composant ne rend rien visuellement
  return null
}
