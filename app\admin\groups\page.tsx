"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { db } from "@/lib/firebase"
import { collection, query, orderBy, getDocs, doc, deleteDoc } from "firebase/firestore"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Pencil, Trash2, Users } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"

interface Group {
  id: string
  name: string
  description: string
  createdAt: any
  updatedAt: any
}

export default function GroupsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [groups, setGroups] = useState<Group[]>([])
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchGroups()
  }, [])

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredGroups(groups)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      setFilteredGroups(
        groups.filter(
          (group) =>
            group.name.toLowerCase().includes(lowercasedSearch) ||
            group.description.toLowerCase().includes(lowercasedSearch),
        ),
      )
    }
  }, [searchTerm, groups])

  const fetchGroups = async () => {
    try {
      setIsLoading(true)
      const groupsQuery = query(collection(db(), "groups"), orderBy("name"))
      const querySnapshot = await getDocs(groupsQuery)

      const groupsData: Group[] = []
      querySnapshot.forEach((doc) => {
        groupsData.push({
          id: doc.id,
          ...doc.data(),
        } as Group)
      })

      console.log("Fetched groups:", groupsData) // Add this line to log the fetched groups
      setGroups(groupsData)
      setFilteredGroups(groupsData)
    } catch (error) {
      console.error("Erreur lors du chargement des groupes:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les groupes",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      // Check if group is used by users
      const usersQuery = query(collection(db(), "users"))
      const usersSnapshot = await getDocs(usersQuery)

      let isGroupUsed = false
      usersSnapshot.forEach((doc) => {
        const userData = doc.data()
        if (userData.groups && userData.groups.includes(id)) {
          isGroupUsed = true
        }
      })

      if (isGroupUsed) {
        toast({
          title: "Erreur",
          description: "Ce groupe est utilisé par un ou plusieurs utilisateurs et ne peut pas être supprimé",
          variant: "destructive",
        })
        return
      }

      await deleteDoc(doc(db(), "groups", id))
      setGroups((prev) => prev.filter((group) => group.id !== id))
      toast({
        title: "Succès",
        description: "Le groupe a été supprimé avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la suppression du groupe:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le groupe",
        variant: "destructive",
      })
    }
  }

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "N/A"
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des groupes</h1>
        <Button onClick={() => router.push("/admin/groups/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Nouveau groupe
        </Button>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <Input
            placeholder="Rechercher un groupe..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Date de création</TableHead>
                  <TableHead>Dernière modification</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredGroups.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      Aucun groupe trouvé
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredGroups.map((group) => (
                    <TableRow key={group.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          {group.name}
                        </div>
                      </TableCell>
                      <TableCell>{group.description}</TableCell>
                      <TableCell>{formatDate(group.createdAt)}</TableCell>
                      <TableCell>{formatDate(group.updatedAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => router.push(`/admin/groups/edit/${group.id}`)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Êtes-vous sûr de vouloir supprimer ce groupe ? Cette action est irréversible.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuler</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(group.id)}
                                  className="bg-red-500 hover:bg-red-600"
                                >
                                  Supprimer
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
