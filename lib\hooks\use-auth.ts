"use client"

import { useState, useEffect, useCallback } from "react"
import {
  type User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  updateEmail,
  update<PERSON>assword,
  EmailAuthProvider,
  reauthenticateWithCredential,
  getIdToken,
  browserLocalPersistence,
  setPersistence,
  signInWithCustomToken,
} from "firebase/auth"
import { auth } from "@/lib/firebase"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import {
  storeAuthUser,
  clearAuthData,
  refreshAuthSession,
  restoreAuthSession,
  validateAndRefreshSession,
} from "@/lib/auth-persistence"
import { recoverSession } from "@/lib/session-recovery"

interface UserData {
  id: string
  email: string
  displayName: string | null
  role: string
  groups: string[]
  [key: string]: any
}

interface AuthState {
  user: User | null
  userData: UserData | null
  loading: boolean
  error: Error | null
}

interface UseAuthReturn extends AuthState {
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: Partial<UserData>) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateUserProfile: (data: Partial<UserData>) => Promise<void>
  updateUserEmail: (newEmail: string, password: string) => Promise<void>
  updateUserPassword: (currentPassword: string, newPassword: string) => Promise<void>
  refreshUserData: () => Promise<void>
  refreshToken: () => Promise<string | null>
  restoreSession: () => Promise<boolean>
}

export function useAuth(): UseAuthReturn {
  const [state, setState] = useState<AuthState>({
    user: null,
    userData: null,
    loading: true,
    error: null,
  })

  // Fetch user data from Firestore
  const fetchUserData = useCallback(async (user: User) => {
    try {
      const userDocRef = doc(db(), "users", user.uid)
      const userDoc = await getDoc(userDocRef)

      if (userDoc.exists()) {
        const userData = {
          id: user.uid,
          email: user.email || "",
          displayName: user.displayName,
          ...userDoc.data(),
        } as UserData

        setState((prev) => ({
          ...prev,
          userData,
        }))

        return userData
      } else {
        // User document doesn't exist in Firestore
        setState((prev) => ({
          ...prev,
          error: new Error("User data not found"),
        }))
        return null
      }
    } catch (error) {
      console.error("Error fetching user data:", error)
      setState((prev) => ({
        ...prev,
        error: error as Error,
      }))
      return null
    }
  }, [])

  // Refresh user data
  const refreshUserData = useCallback(async () => {
    if (state.user) {
      await fetchUserData(state.user)
    }
  }, [state.user, fetchUserData])

  // Refresh token
  const refreshToken = useCallback(async (): Promise<string | null> => {
    if (!state.user) {
      console.log("Impossible de rafraîchir le token: utilisateur non connecté")
      return null
    }

    try {
      console.log("Tentative de rafraîchissement du token...")

      // Forcer le rafraîchissement du token
      const token = await getIdToken(state.user, true)
      console.log("Token rafraîchi avec succès côté client")

      // Mettre à jour le token stocké
      if (token) {
        // Mettre en cache le token dans le service worker et localStorage
        if (typeof window !== "undefined") {
          try {
            // Stocker le token dans localStorage pour une récupération rapide
            localStorage.setItem("auth_last_token", token)
            localStorage.setItem("auth_last_active", Date.now().toString())
            console.log("Token stocké dans localStorage")

            // Mettre en cache via l'API cacheAuthData si disponible
            if (window.cacheAuthData) {
              window.cacheAuthData({ token, uid: state.user.uid })
              console.log("Token mis en cache via cacheAuthData")
            }

            // Informer le service worker si disponible
            if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
              navigator.serviceWorker.controller.postMessage({
                type: "CACHE_AUTH_DATA",
                authData: {
                  uid: state.user.uid,
                  token: token,
                  timestamp: Date.now(),
                },
              })
              console.log("Token envoyé au service worker")
            }
          } catch (cacheError) {
            console.error("Erreur lors de la mise en cache du token:", cacheError)
            // Continuer malgré l'erreur
          }
        }

        // Rafraîchir la session côté serveur
        try {
          console.log("Tentative de rafraîchissement de la session côté serveur...")

          // Vérifier d'abord si une session existe côté serveur
          const checkResponse = await fetch("/api/auth/check-session")
          const checkData = await checkResponse.json()
          const serverHasSession = checkData.authenticated

          if (!serverHasSession) {
            console.log("Pas de session côté serveur, tentative de création...")

            // Si pas de session côté serveur, créer une nouvelle session
            const loginResponse = await fetch("/api/auth/sessionLogin", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ idToken: token }),
            })

            if (loginResponse.ok) {
              console.log("Nouvelle session créée avec succès côté serveur")
            } else {
              console.error("Échec de la création de session:", await loginResponse.text())

              // Essayer avec refresh-session comme fallback
              const refreshResponse = await fetch("/api/auth/refresh-session", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ idToken: token }),
              })

              if (refreshResponse.ok) {
                console.log("Session créée via refresh-session avec succès")
              } else {
                console.error("Échec de la création via refresh-session:", await refreshResponse.text())

                // Tenter de récupérer la session comme dernier recours
                const recovered = await recoverSession()
                if (recovered) {
                  console.log("Session récupérée avec succès via recoverSession")
                } else {
                  console.error("Échec de la récupération de session")
                }
              }
            }
          } else {
            console.log("Session existante côté serveur, rafraîchissement...")

            // Si une session existe déjà, la rafraîchir
            const refreshResponse = await fetch("/api/auth/refresh-session", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ idToken: token }),
            })

            if (refreshResponse.ok) {
              console.log("Session rafraîchie avec succès côté serveur")

              // Si la réponse contient un customToken, l'échanger contre un idToken
              const refreshData = await refreshResponse.json()
              if (refreshData.customToken) {
                try {
                  console.log("Custom token reçu, échange contre un ID token...")
                  const authInstance = auth()
                  await signInWithCustomToken(authInstance, refreshData.customToken)
                  console.log("Échange réussi, utilisateur reconnecté avec le custom token")
                } catch (customTokenError) {
                  console.error("Erreur lors de l'échange du custom token:", customTokenError)
                }
              }
            } else {
              console.error("Erreur lors du rafraîchissement de la session côté serveur:", await refreshResponse.text())

              // Tenter de récupérer la session
              const recovered = await recoverSession()
              if (recovered) {
                console.log("Session récupérée avec succès après échec du rafraîchissement")
              } else {
                console.error("Échec de la récupération de session après échec du rafraîchissement")
              }
            }
          }
        } catch (sessionError) {
          console.error("Exception lors du rafraîchissement de la session:", sessionError)
          // Continuer malgré l'erreur
        }

        // Rafraîchir la session locale
        try {
          await refreshAuthSession()
          console.log("Session locale rafraîchie avec succès")
        } catch (localRefreshError) {
          console.error("Erreur lors du rafraîchissement de la session locale:", localRefreshError)
        }
      }

      return token
    } catch (error) {
      console.error("Erreur lors du rafraîchissement du token:", error)
      return null
    }
  }, [state.user])

  // Fonction pour restaurer explicitement une session
  const restoreSession = useCallback(async (): Promise<boolean> => {
    try {
      setState((prev) => ({ ...prev, loading: true }))

      // Vérifier si nous avons déjà un utilisateur
      const authInstance = auth()
      if (authInstance.currentUser) {
        console.log("Session déjà active, vérification de la synchronisation client/serveur")

        // Vérifier si la session est synchronisée entre client et serveur
        const recovered = await recoverSession()
        if (recovered) {
          console.log("Session synchronisée avec succès")
        } else {
          console.log("Session désynchronisée, mais récupération effectuée")
        }

        setState((prev) => ({ ...prev, loading: false }))
        return true
      }

      console.log("Tentative de restauration de session...")

      // S'assurer que la persistance est configurée correctement
      await setPersistence(authInstance, browserLocalPersistence)

      // Tenter de restaurer la session locale
      const restored = await restoreAuthSession()

      if (!restored) {
        console.log("Échec de la restauration de session locale, vérification côté serveur...")

        // Vérifier si une session existe côté serveur
        const sessionResponse = await fetch("/api/auth/check-session")
        if (sessionResponse.ok) {
          const sessionData = await sessionResponse.json()
          if (sessionData.authenticated) {
            console.log("Session trouvée côté serveur mais pas côté client, nettoyage nécessaire")

            // Nettoyer la session côté serveur
            await fetch("/api/auth/sessionLogout", { method: "POST" })
          }
        }

        setState((prev) => ({ ...prev, loading: false }))
        return false
      }

      // La session a été restaurée localement, vérifier la synchronisation avec le serveur
      const recovered = await recoverSession()

      // La session a été restaurée, l'écouteur onAuthStateChanged s'occupera de mettre à jour l'état
      setState((prev) => ({ ...prev, loading: false }))
      return true
    } catch (error) {
      console.error("Erreur lors de la restauration de session:", error)
      setState((prev) => ({ ...prev, loading: false, error: error as Error }))
      return false
    }
  }, [])

  // Set up auth state listener
  useEffect(() => {
    let isMounted = true
    let tokenUnsubscribe: (() => void) | null = null
    let sessionRefreshInterval: NodeJS.Timeout | null = null

    // Vérifier si nous devons restaurer une session
    const needsRestore = localStorage.getItem("auth_needs_restore") === "true"
    if (needsRestore) {
      localStorage.removeItem("auth_needs_restore")
      console.log("Détection de besoin de restauration de session")
      // Attendre un court instant pour laisser Firebase initialiser
      setTimeout(() => {
        if (isMounted) {
          restoreSession().catch(console.error)
        }
      }, 500)
    }

    // Vérifier si nous sommes côté client
    if (typeof window === "undefined") {
      setState((prev) => ({ ...prev, loading: false }))
      return () => {}
    }

    // Obtenir l'instance d'authentification
    const authInstance = auth()

    // Vérifier si onAuthStateChanged est disponible
    if (!authInstance || typeof authInstance.onAuthStateChanged !== "function") {
      console.error("Firebase Auth n'est pas correctement initialisé ou onAuthStateChanged n'est pas disponible")
      setState((prev) => ({
        ...prev,
        loading: false,
        error: new Error("Firebase Auth n'est pas correctement initialisé"),
      }))
      return () => {}
    }

    // Utiliser l'écouteur d'état d'authentification de Firebase
    const unsubscribe = authInstance.onAuthStateChanged(
      async (user) => {
        if (!isMounted) return

        if (user) {
          setState((prev) => ({ ...prev, user, loading: true, error: null }))

          // Stocker les informations d'authentification pour la persistance
          await storeAuthUser(user)

          // Mettre en cache les données d'authentification dans le service worker
          if (typeof window !== "undefined" && window.cacheAuthData) {
            const token = await user.getIdToken()
            window.cacheAuthData({ token, uid: user.uid })

            // Stocker également dans localStorage pour une récupération rapide
            localStorage.setItem("auth_user_uid", user.uid)
            localStorage.setItem("auth_last_token", token)
            localStorage.setItem("auth_last_active", Date.now().toString())
          }

          try {
            await fetchUserData(user)
          } catch (error) {
            console.error("Error fetching user data after auth state change:", error)
            setState((prev) => ({
              ...prev,
              loading: false,
              error: error as Error,
            }))
          }

          // Configurer l'écouteur de changement de token
          if (typeof authInstance.onIdTokenChanged === "function") {
            tokenUnsubscribe = authInstance.onIdTokenChanged(async (updatedUser) => {
              if (updatedUser) {
                // Mettre à jour le token stocké
                await storeAuthUser(updatedUser)

                // Mettre en cache le token dans le service worker
                if (typeof window !== "undefined" && window.cacheAuthData) {
                  const token = await updatedUser.getIdToken()
                  window.cacheAuthData({ token, uid: updatedUser.uid })

                  // Stocker également dans localStorage pour une récupération rapide
                  localStorage.setItem("auth_last_token", token)
                  localStorage.setItem("auth_last_active", Date.now().toString())
                }
              }
            })
          }

          // Configurer un intervalle pour rafraîchir la session
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            typeof navigator !== "undefined" ? navigator.userAgent : "",
          )

          // Intervalle plus court sur mobile
          const refreshInterval = isMobile ? 5 * 60 * 1000 : 15 * 60 * 1000

          sessionRefreshInterval = setInterval(() => {
            if (authInstance.currentUser) {
              // Rafraîchir le token et la session
              authInstance.currentUser
                .getIdToken(true)
                .then((token) => {
                  localStorage.setItem("auth_last_token", token)
                  localStorage.setItem("auth_last_active", Date.now().toString())
                  return refreshAuthSession()
                })
                .catch(console.error)
            }
          }, refreshInterval)
        } else {
          if (tokenUnsubscribe) {
            tokenUnsubscribe()
            tokenUnsubscribe = null
          }

          if (sessionRefreshInterval) {
            clearInterval(sessionRefreshInterval)
            sessionRefreshInterval = null
          }

          setState({ user: null, userData: null, loading: false, error: null })
        }

        if (isMounted) {
          setState((prev) => ({ ...prev, loading: false }))
        }
      },
      (error) => {
        console.error("Erreur dans onAuthStateChanged:", error)
        setState((prev) => ({ ...prev, loading: false, error: error as Error }))
      },
    )

    // Ajouter un gestionnaire d'événements pour détecter la visibilité de la page
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        console.log("Application redevenue visible, validation de la session")
        validateAndRefreshSession().catch(console.error)

        // Forcer le maintien de la session via le script ultra-persistence
        if (window.forceKeepSessionAlive) {
          window.forceKeepSessionAlive()
        }
      }
    }

    if (typeof document !== "undefined") {
      document.addEventListener("visibilitychange", handleVisibilityChange)
    }

    return () => {
      isMounted = false
      if (typeof unsubscribe === "function") unsubscribe()
      if (tokenUnsubscribe) tokenUnsubscribe()
      if (sessionRefreshInterval) clearInterval(sessionRefreshInterval)
      if (typeof document !== "undefined") {
        document.removeEventListener("visibilitychange", handleVisibilityChange)
      }
    }
  }, [fetchUserData, restoreSession])

  // Sign in
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))

      const authInstance = auth()

      // S'assurer que la persistance est configurée correctement avant la connexion
      await setPersistence(authInstance, browserLocalPersistence)

      const userCredential = await signInWithEmailAndPassword(authInstance, email, password)

      // Stocker les informations d'authentification pour la persistance
      await storeAuthUser(userCredential.user)

      // Obtenir un token ID pour créer une session côté serveur
      const idToken = await userCredential.user.getIdToken()

      // Créer une session côté serveur
      try {
        const sessionResponse = await fetch("/api/auth/sessionLogin", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ idToken }),
        })

        if (!sessionResponse.ok) {
          console.error("Erreur lors de la création de la session côté serveur:", await sessionResponse.text())
        } else {
          console.log("Session créée avec succès côté serveur")
        }
      } catch (sessionError) {
        console.error("Erreur lors de la création de la session:", sessionError)
        // Continuer malgré l'erreur car l'authentification côté client a réussi
      }

      // Mettre en cache les données d'authentification dans le service worker
      if (typeof window !== "undefined") {
        try {
          if (window.cacheAuthData) {
            window.cacheAuthData({ token: idToken, uid: userCredential.user.uid })
          }

          // Stocker également dans localStorage pour une récupération rapide
          localStorage.setItem("auth_user_uid", userCredential.user.uid)
          localStorage.setItem("auth_last_token", idToken)
          localStorage.setItem("auth_last_active", Date.now().toString())

          // Informer le service worker si disponible
          if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: "CACHE_AUTH_DATA",
              authData: {
                uid: userCredential.user.uid,
                token: idToken,
                timestamp: Date.now(),
              },
            })
          }
        } catch (cacheError) {
          console.error("Erreur lors de la mise en cache des données d'authentification:", cacheError)
          // Continuer malgré l'erreur
        }
      }

      // Auth state listener will handle updating the state
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error as Error,
      }))
      throw error
    }
  }, [])

  // Sign up
  const signUp = useCallback(async (email: string, password: string, userData: Partial<UserData>) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))

      const authInstance = auth()

      // S'assurer que la persistance est configurée correctement avant l'inscription
      await setPersistence(authInstance, browserLocalPersistence)

      // Create the user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(authInstance, email, password)
      const user = userCredential.user

      // Stocker les informations d'authentification pour la persistance
      await storeAuthUser(user)

      // Mettre en cache les données d'authentification dans le service worker
      if (typeof window !== "undefined" && window.cacheAuthData) {
        const token = await user.getIdToken()
        window.cacheAuthData({ token, uid: user.uid })

        // Stocker également dans localStorage pour une récupération rapide
        localStorage.setItem("auth_user_uid", user.uid)
        localStorage.setItem("auth_last_token", token)
        localStorage.setItem("auth_last_active", Date.now().toString())
      }

      // Update profile if display name is provided
      if (userData.displayName) {
        await updateProfile(user, { displayName: userData.displayName })
      }

      // Create user document in Firestore
      await setDoc(doc(db(), "users", user.uid), {
        email: user.email,
        displayName: userData.displayName || null,
        role: userData.role || "user",
        groups: userData.groups || [],
        createdAt: new Date().toISOString(),
        ...userData,
      })

      // Auth state listener will handle updating the state
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error as Error,
      }))
      throw error
    }
  }, [])

  // Sign out
  const signOut = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))
      console.log("Début du processus de déconnexion...")

      // Nettoyer les données d'authentification stockées
      try {
        await clearAuthData()
        console.log("Données d'authentification locales nettoyées")
      } catch (clearError) {
        console.error("Erreur lors du nettoyage des données d'authentification:", clearError)
      }

      // Utiliser la fonction forceLogoutAndReload pour une déconnexion complète
      try {
        // Importer dynamiquement pour éviter les problèmes de dépendances circulaires
        const { forceLogoutAndReload } = await import("@/lib/session-recovery")

        // Mettre à jour l'état avant de forcer la déconnexion
        setState({
          user: null,
          userData: null,
          loading: false,
          error: null,
        })

        // Déterminer si nous sommes dans un contexte de test
        const isTestContext = window.location.pathname.includes("/session-test")

        // Forcer la déconnexion et le rechargement (sauf si nous sommes dans un contexte de test)
        await forceLogoutAndReload(isTestContext)
      } catch (forceLogoutError) {
        console.error("Erreur lors de la déconnexion forcée:", forceLogoutError)

        // Fallback en cas d'erreur : déconnexion manuelle
        try {
          // Déconnecter côté serveur
          await fetch("/api/auth/sessionLogout", { method: "POST" })

          // Déconnecter côté client
          const authInstance = auth()
          if (authInstance.currentUser) {
            await firebaseSignOut(authInstance)
          }

          // Mettre à jour l'état
          setState({
            user: null,
            userData: null,
            loading: false,
            error: null,
          })

          // Recharger la page (sauf si nous sommes dans un contexte de test)
          if (typeof window !== "undefined") {
            const isTestContext = window.location.pathname.includes("/session-test")
            if (!isTestContext) {
              setTimeout(() => {
                window.location.reload()
              }, 500)
            } else {
              console.log("Rechargement de la page ignoré (mode test)")
            }
          }
        } catch (fallbackError) {
          console.error("Erreur lors de la déconnexion de fallback:", fallbackError)

          // Mettre à jour l'état même en cas d'erreur
          setState({
            user: null,
            userData: null,
            loading: false,
            error: fallbackError as Error,
          })

          throw fallbackError
        }
      }
    } catch (error) {
      console.error("Exception globale lors de la déconnexion:", error)

      setState((prev) => ({
        ...prev,
        loading: false,
        error: error as Error,
      }))

      throw error
    }
  }, [])

  // Reset password
  const resetPassword = useCallback(async (email: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))
      const authInstance = auth()
      await sendPasswordResetEmail(authInstance, email)
      setState((prev) => ({ ...prev, loading: false }))
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error as Error,
      }))
      throw error
    }
  }, [])

  // Update user profile
  const updateUserProfile = useCallback(
    async (data: Partial<UserData>) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }))

        if (!state.user) {
          throw new Error("No authenticated user")
        }

        // Update display name in Firebase Auth if provided
        if (data.displayName) {
          await updateProfile(state.user, { displayName: data.displayName })

          // Mettre à jour les informations stockées
          await storeAuthUser(state.user)
        }

        // Update user document in Firestore
        const userDocRef = doc(db(), "users", state.user.uid)
        await setDoc(userDocRef, { ...data, updatedAt: new Date().toISOString() }, { merge: true })

        // Refresh user data
        await fetchUserData(state.user)
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error as Error,
        }))
        throw error
      }
    },
    [state.user, fetchUserData],
  )

  // Update user email
  const updateUserEmail = useCallback(
    async (newEmail: string, password: string) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }))

        if (!state.user) {
          throw new Error("No authenticated user")
        }

        // Re-authenticate the user
        const credential = EmailAuthProvider.credential(state.user.email!, password)
        await reauthenticateWithCredential(state.user, credential)

        // Update email in Firebase Auth
        await updateEmail(state.user, newEmail)

        // Mettre à jour les informations stockées
        await storeAuthUser(state.user)

        // Update email in Firestore
        const userDocRef = doc(db(), "users", state.user.uid)
        await setDoc(userDocRef, { email: newEmail, updatedAt: new Date().toISOString() }, { merge: true })

        // Refresh user data
        await fetchUserData(state.user)
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error as Error,
        }))
        throw error
      }
    },
    [state.user, fetchUserData],
  )

  // Update user password
  const updateUserPassword = useCallback(
    async (currentPassword: string, newPassword: string) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }))

        if (!state.user) {
          throw new Error("No authenticated user")
        }

        // Re-authenticate the user
        const credential = EmailAuthProvider.credential(state.user.email!, currentPassword)
        await reauthenticateWithCredential(state.user, credential)

        // Update password in Firebase Auth
        await updatePassword(state.user, newPassword)

        setState((prev) => ({ ...prev, loading: false }))
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error as Error,
        }))
        throw error
      }
    },
    [state.user],
  )

  return {
    ...state,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateUserProfile,
    updateUserEmail,
    updateUserPassword,
    refreshUserData,
    refreshToken,
    restoreSession,
  }
}
