"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { applyTheme } from "@/lib/theme-service"
import { doc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useToast } from "@/hooks/use-toast"
import { useState } from "react"
import { Loader2 } from "lucide-react"

type ThemePreset = {
  name: string
  description: string
  colors: {
    primary: string
    secondary: string
    accent: string
    success: string
    warning: string
    error: string
    info: string
    // Add dark mode colors
    darkBackground: string
    darkForeground: string
    darkMuted: string
    darkMutedForeground: string
    darkCard: string
    darkCardForeground: string
  }
}

const presets: ThemePreset[] = [
  {
    name: "Bleu classique",
    description: "Thème par défaut avec une palette de bleus professionnels",
    colors: {
      primary: "#1e40af",
      secondary: "#f3f4f6",
      accent: "#8b5cf6",
      success: "#10b981",
      warning: "#f59e0b",
      error: "#ef4444",
      info: "#06b6d4",
      // Dark mode colors
      darkBackground: "#0f172a", // slate-900
      darkForeground: "#f8fafc", // slate-50
      darkMuted: "#1e293b", // slate-800
      darkMutedForeground: "#94a3b8", // slate-400
      darkCard: "#1e293b", // slate-800
      darkCardForeground: "#f8fafc", // slate-50
    },
  },
  {
    name: "Vert nature",
    description: "Palette apaisante inspirée par la nature",
    colors: {
      primary: "#059669",
      secondary: "#f0fdf4",
      accent: "#0ea5e9",
      success: "#16a34a",
      warning: "#eab308",
      error: "#dc2626",
      info: "#0284c7",
      // Dark mode colors
      darkBackground: "#0c1b17", // dark green-900
      darkForeground: "#f0fdf4", // green-50
      darkMuted: "#14342a", // dark green-800
      darkMutedForeground: "#86efac", // green-300
      darkCard: "#14342a", // dark green-800
      darkCardForeground: "#f0fdf4", // green-50
    },
  },
  {
    name: "Violet créatif",
    description: "Palette moderne avec des tons violets et roses",
    colors: {
      primary: "#7c3aed",
      secondary: "#f5f3ff",
      accent: "#ec4899",
      success: "#10b981",
      warning: "#f59e0b",
      error: "#ef4444",
      info: "#06b6d4",
      // Dark mode colors
      darkBackground: "#1a103d", // dark violet-900
      darkForeground: "#f5f3ff", // violet-50
      darkMuted: "#2e1065", // violet-950
      darkMutedForeground: "#c4b5fd", // violet-300
      darkCard: "#2e1065", // violet-950
      darkCardForeground: "#f5f3ff", // violet-50
    },
  },
  {
    name: "Rouge dynamique",
    description: "Palette énergique avec des tons rouges et oranges",
    colors: {
      primary: "#dc2626",
      secondary: "#fef2f2",
      accent: "#f97316",
      success: "#16a34a",
      warning: "#eab308",
      error: "#b91c1c",
      info: "#0284c7",
      // Dark mode colors
      darkBackground: "#450a0a", // dark red-900
      darkForeground: "#fef2f2", // red-50
      darkMuted: "#7f1d1d", // red-900
      darkMutedForeground: "#fca5a5", // red-300
      darkCard: "#7f1d1d", // red-900
      darkCardForeground: "#fef2f2", // red-50
    },
  },
]

// Update the applyPreset function to include dark mode colors
const applyPreset = async (preset: ThemePreset) => {
  setIsLoading(preset.name)

  try {
    // Appliquer le thème visuellement
    applyTheme({
      ...preset.colors,
      background: "#ffffff",
      foreground: "#1f2937",
      muted: "#f3f4f6",
      mutedForeground: "#6b7280",
      // Include dark mode colors
      darkBackground: preset.colors.darkBackground,
      darkForeground: preset.colors.darkForeground,
      darkMuted: preset.colors.darkMuted,
      darkMutedForeground: preset.colors.darkMutedForeground,
      darkCard: preset.colors.darkCard,
      darkCardForeground: preset.colors.darkCardForeground,
    })

    // Enregistrer dans Firestore
    await setDoc(
      doc(db, "settings", "site"),
      {
        ...preset.colors,
        updatedAt: serverTimestamp(),
      },
      { merge: true },
    )

    toast({
      title: "Thème appliqué",
      description: `Le thème "${preset.name}" a été appliqué avec succès.`,
    })
  } catch (error) {
    console.error("Erreur lors de l'application du thème:", error)
    toast({
      title: "Erreur",
      description: "Impossible d'appliquer le thème.",
      variant: "destructive",
    })
  } finally {
    setIsLoading(null)
  }
}

export function ThemePresets() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState<string | null>(null)

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {presets.map((preset) => (
        <Card key={preset.name} className="overflow-hidden">
          <div className="h-2" style={{ backgroundColor: preset.colors.primary }} />
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{preset.name}</CardTitle>
            <CardDescription>{preset.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 mb-4">
              {Object.entries(preset.colors).map(([key, color]) => (
                <div
                  key={key}
                  className="w-6 h-6 rounded-full border"
                  style={{ backgroundColor: color }}
                  title={`${key}: ${color}`}
                />
              ))}
            </div>
            <Button
              onClick={() => applyPreset(preset)}
              disabled={isLoading !== null}
              className="w-full"
              style={{
                backgroundColor: isLoading === preset.name ? undefined : preset.colors.primary,
              }}
            >
              {isLoading === preset.name ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Application...
                </>
              ) : (
                <>Appliquer ce thème</>
              )}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
