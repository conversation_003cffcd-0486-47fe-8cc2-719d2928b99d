"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { RefreshCw, Shield, Trash2, Download, AlertTriangle, CheckCircle2, XCircle } from "lucide-react"
import { forceLogoutAndReload } from "@/lib/session-recovery"
import { useAuth } from "@/lib/hooks/use-auth"

export function AuthCacheManager() {
  const { user } = useAuth()
  const [authSwStatus, setAuthSwStatus] = useState<any>(null)
  const [authCacheData, setAuthCacheData] = useState<any>(null)
  const [authSessionData, setAuthSessionData] = useState<any>(null)
  const [authUserData, setAuthUserData] = useState<any>(null)
  const [authTokenData, setAuthTokenData] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [localStorageData, setLocalStorageData] = useState<Record<string, string>>({})
  const [indexedDBData, setIndexedDBData] = useState<any>(null)

  // Fonction pour vérifier l'état du service worker d'authentification
  const checkAuthServiceWorker = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      if (typeof window === "undefined" || !window.checkAuthServiceWorker) {
        setError("Service Worker d'authentification non disponible")
        setAuthSwStatus({ supported: false })
        return
      }

      const status = await window.checkAuthServiceWorker()
      setAuthSwStatus(status)

      // Récupérer les données d'authentification du cache
      if (status.isActive && status.authCacheExists) {
        try {
          const authDataResponse = await fetch("/auth-data")
          if (authDataResponse.ok) {
            const authData = await authDataResponse.json()
            setAuthCacheData(authData)
          }

          const authSessionResponse = await fetch("/auth-session")
          if (authSessionResponse.ok) {
            const sessionData = await authSessionResponse.json()
            setAuthSessionData(sessionData)
          }

          const authUserResponse = await fetch("/auth-user")
          if (authUserResponse.ok) {
            const userData = await authUserResponse.json()
            setAuthUserData(userData)
          }

          const authTokenResponse = await fetch("/auth-token")
          if (authTokenResponse.ok) {
            const tokenData = await authTokenResponse.json()
            setAuthTokenData(tokenData)
          }
        } catch (fetchError) {
          console.error("Erreur lors de la récupération des données d'authentification:", fetchError)
        }
      }

      setSuccess("État du service worker d'authentification vérifié avec succès")
    } catch (error) {
      console.error("Erreur lors de la vérification du service worker d'authentification:", error)
      setError("Erreur lors de la vérification du service worker d'authentification")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour forcer la persistance maximale
  const forceMaximumPersistence = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      if (typeof window === "undefined" || !window.forceMaximumPersistence) {
        setError("Fonction de persistance maximale non disponible")
        return
      }

      const result = await window.forceMaximumPersistence()
      if (result) {
        setSuccess("Persistance maximale forcée avec succès")
        // Rafraîchir les données
        await checkAuthServiceWorker()
      } else {
        setError("Échec du forçage de la persistance maximale")
      }
    } catch (error) {
      console.error("Erreur lors du forçage de la persistance maximale:", error)
      setError("Erreur lors du forçage de la persistance maximale")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour mettre à jour le service worker d'authentification
  const updateAuthServiceWorker = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      if (typeof window === "undefined" || !window.updateAuthServiceWorker) {
        setError("Fonction de mise à jour du service worker non disponible")
        return
      }

      const result = await window.updateAuthServiceWorker()
      if (result.success) {
        setSuccess("Service worker d'authentification mis à jour avec succès")
        // Rafraîchir les données
        await checkAuthServiceWorker()
      } else {
        setError("Échec de la mise à jour du service worker d'authentification")
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour du service worker d'authentification:", error)
      setError("Erreur lors de la mise à jour du service worker d'authentification")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour nettoyer le cache d'authentification
  const clearAuthCache = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      if (typeof window === "undefined" || !navigator.serviceWorker || !navigator.serviceWorker.controller) {
        setError("Service Worker non disponible")
        return
      }

      // Envoyer un message au service worker pour nettoyer le cache
      navigator.serviceWorker.controller.postMessage({
        type: "CLEAR_AUTH_CACHE",
        version: "v3",
        timestamp: Date.now(),
      })

      setSuccess("Demande de nettoyage du cache d'authentification envoyée")

      // Attendre un peu pour que le nettoyage soit effectué
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Rafraîchir les données
      await checkAuthServiceWorker()
    } catch (error) {
      console.error("Erreur lors du nettoyage du cache d'authentification:", error)
      setError("Erreur lors du nettoyage du cache d'authentification")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour forcer la déconnexion
  const handleForceLogout = async () => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)

      await forceLogoutAndReload(true) // true pour ne pas recharger la page
      setSuccess("Déconnexion forcée avec succès")

      // Rafraîchir les données
      await checkAuthServiceWorker()
    } catch (error) {
      console.error("Erreur lors de la déconnexion forcée:", error)
      setError("Erreur lors de la déconnexion forcée")
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour récupérer les données du localStorage
  const getLocalStorageData = () => {
    try {
      const data: Record<string, string> = {}

      // Clés d'authentification à rechercher
      const authKeys = [
        "auth_user_v3",
        "auth_session_v3",
        "auth_timestamp_v3",
        "auth_user_uid",
        "auth_last_token",
        "auth_last_active",
        "auth_refresh_token",
        "auth_user_data",
        "auth_session_state",
        "auth_status",
        "auth_user_id",
        "auth_last_check",
        "auth_persistence_helper_loaded",
        "auth_persistence_helper_loaded_at",
        "auth_persistence_helper_version",
        "is_secure_context",
        "service_worker_supported",
        "indexed_db_available",
        "offline_support_enabled",
        "last_online_timestamp",
      ]

      // Récupérer toutes les clés qui commencent par "auth_"
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.startsWith("auth_") || authKeys.includes(key))) {
          data[key] = localStorage.getItem(key) || ""
        }
      }

      setLocalStorageData(data)
    } catch (error) {
      console.error("Erreur lors de la récupération des données du localStorage:", error)
    }
  }

  // Fonction pour récupérer les données d'IndexedDB
  const getIndexedDBData = async () => {
    try {
      if (typeof window === "undefined" || !window.indexedDB) {
        return
      }

      // Vérifier si la base de données existe
      const request = window.indexedDB.open("acrDirectAuth", 1)

      request.onsuccess = async (event) => {
        const db = request.result

        // Vérifier si l'objectStore existe
        if (!db.objectStoreNames.contains("authData")) {
          setIndexedDBData({ error: "Aucune donnée d'authentification trouvée dans IndexedDB" })
          return
        }

        try {
          const transaction = db.transaction(["authData"], "readonly")
          const store = transaction.objectStore("authData")
          const allData = await new Promise((resolve, reject) => {
            const request = store.getAll()
            request.onsuccess = () => {
              resolve(request.result)
            }
            request.onerror = (error) => {
              reject(error)
            }
          })

          setIndexedDBData(allData)
        } catch (error) {
          console.error("Erreur lors de la récupération des données d'IndexedDB:", error)
          setIndexedDBData({ error: "Erreur lors de la récupération des données" })
        } finally {
          db.close()
        }
      }

      request.onerror = (event) => {
        console.error("Erreur lors de l'ouverture de la base de données IndexedDB:", event)
        setIndexedDBData({ error: "Erreur lors de l'ouverture de la base de données" })
      }

      request.onupgradeneeded = (event) => {
        const db = request.result
        if (!db.objectStoreNames.contains("authData")) {
          db.createObjectStore("authData", { keyPath: "key" })
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des données d'IndexedDB:", error)
      setIndexedDBData({ error: "Erreur lors de la récupération des données" })
    }
  }

  // Charger les données au montage du composant
  useEffect(() => {
    checkAuthServiceWorker()
    getLocalStorageData()
    getIndexedDBData()
  }, [])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-blue-600" />
            Gestion du Cache d'Authentification
          </CardTitle>
          <CardDescription>
            Gérez le cache d'authentification pour assurer une persistance maximale des sessions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Succès</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">État du Service Worker</h3>
              {authSwStatus ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Version:</span>
                    <Badge variant="outline">{authSwStatus.version || "Inconnue"}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Actif:</span>
                    {authSwStatus.isActive ? (
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Oui</Badge>
                    ) : (
                      <Badge variant="destructive">Non</Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Cache existant:</span>
                    {authSwStatus.authCacheExists ? (
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Oui</Badge>
                    ) : (
                      <Badge variant="secondary">Non</Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Données en cache:</span>
                    {authSwStatus.authDataExists ? (
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Oui</Badge>
                    ) : (
                      <Badge variant="secondary">Non</Badge>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">Chargement de l'état du service worker...</div>
              )}
            </div>

            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">État de l'Authentification</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Utilisateur connecté:</span>
                  {user ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Oui</Badge>
                  ) : (
                    <Badge variant="destructive">Non</Badge>
                  )}
                </div>
                {user && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">UID:</span>
                      <Badge variant="outline">{user.uid.substring(0, 8)}...</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Email:</span>
                      <span className="text-sm font-mono truncate max-w-[150px]">{user.email}</span>
                    </div>
                  </>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-sm">Session en cache:</span>
                  {authSessionData ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Oui</Badge>
                  ) : (
                    <Badge variant="secondary">Non</Badge>
                  )}
                </div>
                {authSessionData && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">État de la session:</span>
                    <Badge variant="outline">{authSessionData.state || "Inconnu"}</Badge>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Tabs defaultValue="cache" className="mt-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="cache">Cache SW</TabsTrigger>
              <TabsTrigger value="localStorage">LocalStorage</TabsTrigger>
              <TabsTrigger value="indexedDB">IndexedDB</TabsTrigger>
            </TabsList>

            <TabsContent value="cache" className="mt-4 space-y-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Données d'Authentification en Cache</h3>
                <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                  <pre className="text-xs font-mono">
                    {authCacheData ? JSON.stringify(authCacheData, null, 2) : "Aucune donnée en cache"}
                  </pre>
                </ScrollArea>
              </div>

              {authSessionData && (
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Données de Session en Cache</h3>
                  <ScrollArea className="h-[150px] w-full rounded-md border p-4">
                    <pre className="text-xs font-mono">{JSON.stringify(authSessionData, null, 2)}</pre>
                  </ScrollArea>
                </div>
              )}

              {authTokenData && (
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Token d'Authentification en Cache</h3>
                  <ScrollArea className="h-[100px] w-full rounded-md border p-4">
                    <pre className="text-xs font-mono">
                      {JSON.stringify(
                        {
                          ...authTokenData,
                          token: authTokenData.token ? `${authTokenData.token.substring(0, 20)}...` : null,
                        },
                        null,
                        2,
                      )}
                    </pre>
                  </ScrollArea>
                </div>
              )}
            </TabsContent>

            <TabsContent value="localStorage" className="mt-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center justify-between">
                  <span>Données d'Authentification dans LocalStorage</span>
                  <Button variant="outline" size="sm" onClick={getLocalStorageData}>
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Actualiser
                  </Button>
                </h3>
                <ScrollArea className="h-[300px] w-full rounded-md border p-4">
                  {Object.keys(localStorageData).length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(localStorageData).map(([key, value]) => (
                        <div key={key} className="border-b pb-2">
                          <div className="font-medium text-xs">{key}</div>
                          <div className="text-xs font-mono break-all">
                            {key.includes("token") ? `${value.substring(0, 20)}...` : value}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      Aucune donnée d'authentification trouvée dans localStorage
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="indexedDB" className="mt-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center justify-between">
                  <span>Données d'Authentification dans IndexedDB</span>
                  <Button variant="outline" size="sm" onClick={getIndexedDBData}>
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Actualiser
                  </Button>
                </h3>
                <ScrollArea className="h-[300px] w-full rounded-md border p-4">
                  {indexedDBData ? (
                    indexedDBData.error ? (
                      <div className="text-sm text-muted-foreground">{indexedDBData.error}</div>
                    ) : (
                      <div className="space-y-4">
                        {Array.isArray(indexedDBData) &&
                          indexedDBData.map((item, index) => (
                            <div key={index} className="border-b pb-2">
                              <div className="font-medium text-xs">{item.key}</div>
                              <pre className="text-xs font-mono break-all">{JSON.stringify(item.value, null, 2)}</pre>
                              <div className="text-xs text-muted-foreground">
                                Timestamp: {new Date(item.timestamp).toLocaleString()}
                              </div>
                            </div>
                          ))}
                      </div>
                    )
                  ) : (
                    <div className="text-sm text-muted-foreground">Chargement des données IndexedDB...</div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button onClick={checkAuthServiceWorker} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
          <Button onClick={forceMaximumPersistence} disabled={loading} variant="outline">
            <Shield className="h-4 w-4 mr-2" />
            Forcer la persistance
          </Button>
          <Button onClick={updateAuthServiceWorker} disabled={loading} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Mettre à jour le SW
          </Button>
          <Button onClick={clearAuthCache} disabled={loading} variant="outline">
            <Trash2 className="h-4 w-4 mr-2" />
            Nettoyer le cache
          </Button>
          <Button onClick={handleForceLogout} disabled={loading} variant="destructive">
            <XCircle className="h-4 w-4 mr-2" />
            Forcer la déconnexion
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
