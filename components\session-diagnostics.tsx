"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { recoverSession, forceLogoutAndReload } from "@/lib/session-recovery"
import { RefreshCw, AlertTriangle, CheckCircle, XCircle, RotateCw, LogOut } from "lucide-react"

export function SessionDiagnostics() {
  const { user, refreshToken, restoreSession } = useAuth()
  const [serverSession, setServerSession] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  // Fonction pour vérifier l'état de la session côté serveur
  const checkServerSession = async () => {
    setIsChecking(true)
    try {
      const response = await fetch("/api/auth/check-session")
      const data = await response.json()
      setServerSession(data.authenticated)
    } catch (error) {
      console.error("Erreur lors de la vérification de la session serveur:", error)
      setServerSession(null)
    } finally {
      setIsChecking(false)
      setLastChecked(new Date())
    }
  }

  // Vérifier l'état de la session au chargement du composant
  useEffect(() => {
    checkServerSession()
  }, [user])

  // Fonction pour récupérer la session
  const handleRecoverSession = async () => {
    setIsChecking(true)
    try {
      // Vérifier d'abord l'état actuel
      await checkServerSession()

      // Tenter de récupérer la session
      const recovered = await recoverSession()
      if (recovered) {
        console.log("Session récupérée avec succès")
      } else {
        console.log("Échec de la récupération de session")
      }

      // Vérifier à nouveau l'état après la récupération
      await checkServerSession()

      // Si l'utilisateur est connecté côté client mais pas côté serveur, essayer de rafraîchir le token
      if (user && !serverSession) {
        console.log("Tentative de synchronisation client-serveur via rafraîchissement de token...")
        await refreshToken()
        await checkServerSession()
      }
    } catch (error) {
      console.error("Erreur lors de la récupération de session:", error)
    } finally {
      setIsChecking(false)
    }
  }

  // Fonction pour forcer la déconnexion
  const handleForceLogout = async () => {
    if (confirm("Êtes-vous sûr de vouloir forcer la déconnexion ? Cela vous déconnectera et rechargera la page.")) {
      await forceLogoutAndReload()
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Diagnostic de session</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="status">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="status">État de la session</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">État actuel</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <span>Client:</span>
                <span className="flex items-center">
                  {user ? (
                    <CheckCircle className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  {user ? "Connecté" : "Déconnecté"}
                </span>

                <span>Serveur:</span>
                <span className="flex items-center">
                  {serverSession === null ? (
                    <AlertTriangle className="mr-1 h-4 w-4 text-yellow-500" />
                  ) : serverSession ? (
                    <CheckCircle className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  {serverSession === null ? "Inconnu" : serverSession ? "Connecté" : "Déconnecté"}
                </span>

                <span>Synchronisation:</span>
                <span className="flex items-center">
                  {serverSession === null ? (
                    <AlertTriangle className="mr-1 h-4 w-4 text-yellow-500" />
                  ) : user && serverSession ? (
                    <CheckCircle className="mr-1 h-4 w-4 text-green-500" />
                  ) : !user && !serverSession ? (
                    <CheckCircle className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  {serverSession === null
                    ? "Inconnu"
                    : (user && serverSession) || (!user && !serverSession)
                      ? "Synchronisé"
                      : "Désynchronisé"}
                </span>

                {lastChecked && (
                  <>
                    <span>Dernière vérification:</span>
                    <span>{lastChecked.toLocaleTimeString()}</span>
                  </>
                )}
              </div>
            </div>

            <div className="pt-2">
              <Button onClick={checkServerSession} variant="outline" size="sm" disabled={isChecking} className="w-full">
                {isChecking ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    Vérification...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Vérifier l'état de la session
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="actions" className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Actions de récupération</h3>
              <p className="text-sm text-muted-foreground">
                Ces actions vous permettent de résoudre les problèmes de session.
              </p>
            </div>

            <div className="grid gap-2">
              <Button onClick={handleRecoverSession} variant="outline" disabled={isChecking} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Récupérer la session
              </Button>

              <Button
                onClick={() => refreshToken()}
                variant="outline"
                disabled={!user || isChecking}
                className="w-full"
              >
                <RotateCw className="mr-2 h-4 w-4" />
                Rafraîchir le token
              </Button>

              <Button onClick={() => restoreSession()} variant="outline" disabled={isChecking} className="w-full">
                <CheckCircle className="mr-2 h-4 w-4" />
                Restaurer la session
              </Button>

              <Button onClick={handleForceLogout} variant="destructive" disabled={isChecking} className="w-full mt-4">
                <LogOut className="mr-2 h-4 w-4" />
                Forcer la déconnexion
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
