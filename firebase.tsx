import { initializeApp, getApps, getApp } from "firebase/app"
import { getAuth } from "firebase/auth"
import { getFirestore } from "firebase/firestore"
import { getStorage } from "firebase/storage"

let firebaseConfig = {}

try {
  const apiKey = process.env.NEXT_PUBLIC_FIREBASE_API_KEY
  const authDomain = process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
  const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
  const storageBucket = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
  const messagingSenderId = process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
  const appId = process.env.NEXT_PUBLIC_FIREBASE_APP_ID

  if (!apiKey || !authDomain || !projectId || !storageBucket || !messagingSenderId || !appId) {
    throw new Error("Missing Firebase environment variables")
  }

  firebaseConfig = {
    apiKey: apiKey,
    authDomain: authDomain,
    projectId: projectId,
    storageBucket: storageBucket,
    messagingSenderId: messagingSenderId,
    appId: appId,
  }
} catch (error) {
  console.error("Error building firebaseConfig:", error)
}

// Initialize Firebase.
let app
try {
  app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig)
  console.log("Firebase app initialized successfully")
} catch (error) {
  console.error("Firebase app initialization error:", error)
}

let auth
try {
  auth = getAuth(app)
  console.log("Firebase auth initialized successfully")
} catch (error) {
  console.error("Firebase auth initialization error:", error)
}

let db
try {
  db = getFirestore(app)
  console.log("Firebase firestore initialized successfully")
} catch (error) {
  console.error("Firebase firestore initialization error:", error)
}

let storage
try {
  storage = getStorage(app)
  console.log("Firebase storage initialized successfully")
} catch (error) {
  console.error("Firebase storage initialization error:", error)
}

export { app, auth, db, storage }
