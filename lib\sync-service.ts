/**
 * Service de synchronisation bidirectionnelle pour l'application
 * Ce service gère la synchronisation des données entre le client et le serveur
 * en mode hors ligne et en ligne
 */

import localforage from "localforage"
import { hash as hashString } from "@/lib/utils"
import { db, auth } from "@/lib/firebase"
import { doc, setDoc, updateDoc, deleteDoc } from "firebase/firestore"

// Types d'opérations de synchronisation
export enum SyncOperationType {
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
}

// Interface pour une opération de synchronisation
export interface SyncOperation {
  id: string
  type: SyncOperationType
  collection: string
  documentId: string
  data?: any
  timestamp: number
  userId: string
  status: "pending" | "processing" | "completed" | "failed"
  error?: string
  retryCount: number
  lastRetry?: number
}

// Configuration du store IndexedDB pour les opérations de synchronisation
const syncStore = localforage.createInstance({
  name: "acrDirect",
  storeName: "syncOperations",
  description: "Opérations de synchronisation en attente",
})

// Classe pour la gestion de la synchronisation
class SyncService {
  private initialized = false
  private initPromise: Promise<void> | null = null
  private syncInProgress = false
  private maxRetries = 5
  private retryDelay = 5000 // 5 secondes
  private listeners: Array<(operations: SyncOperation[]) => void> = []

  constructor() {
    this.initPromise = this.initialize()
  }

  /**
   * Initialise le service de synchronisation
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Vérifier si nous sommes dans un environnement navigateur
      if (typeof window === "undefined") {
        console.warn("SyncService: Tentative d'initialisation côté serveur, opération ignorée")
        this.initialized = true
        return
      }

      // Écouter les changements d'état de connexion
      window.addEventListener("online", this.handleOnline)
      window.addEventListener("offline", this.handleOffline)

      this.initialized = true
      console.log("SyncService: Initialisation réussie")

      // Si nous sommes en ligne, tenter une synchronisation initiale
      if (navigator.onLine) {
        setTimeout(() => this.synchronize(), 2000)
      }
    } catch (error) {
      console.error("SyncService: Erreur lors de l'initialisation", error)
      // Marquer comme initialisé même en cas d'erreur pour éviter les tentatives répétées
      this.initialized = true
    }
  }

  /**
   * S'assure que le service est initialisé avant d'exécuter une opération
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      if (this.initPromise) {
        await this.initPromise
      } else {
        await this.initialize()
      }
    }
  }

  /**
   * Gère l'événement de retour en ligne
   */
  private handleOnline = async () => {
    console.log("SyncService: Connexion rétablie, tentative de synchronisation...")
    await this.synchronize()
  }

  /**
   * Gère l'événement de passage hors ligne
   */
  private handleOffline = () => {
    console.log("SyncService: Connexion perdue, synchronisation suspendue")
    this.syncInProgress = false
  }

  /**
   * Ajoute une opération de synchronisation
   * @param operation Opération à ajouter
   */
  async addOperation(operation: Omit<SyncOperation, "id" | "timestamp" | "status" | "retryCount">): Promise<string> {
    await this.ensureInitialized()

    // Générer un ID unique pour l'opération
    const id = `${Date.now()}-${hashString(JSON.stringify(operation))}`

    // Créer l'opération complète
    const fullOperation: SyncOperation = {
      ...operation,
      id,
      timestamp: Date.now(),
      status: "pending",
      retryCount: 0,
    }

    // Stocker l'opération
    await syncStore.setItem(id, fullOperation)

    // Notifier les écouteurs
    this.notifyListeners()

    // Si nous sommes en ligne, tenter une synchronisation immédiate
    if (navigator.onLine && !this.syncInProgress) {
      setTimeout(() => this.synchronize(), 1000)
    }

    return id
  }

  /**
   * Crée un document et l'ajoute à la file de synchronisation si hors ligne
   * @param collectionPath Chemin de la collection
   * @param documentId ID du document
   * @param data Données du document
   */
  async createDocument(collectionPath: string, documentId: string, data: any): Promise<string> {
    // Si nous sommes en ligne, créer directement le document
    if (navigator.onLine) {
      try {
        await setDoc(doc(db(), collectionPath, documentId), {
          ...data,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        return documentId
      } catch (error) {
        console.error(`SyncService: Erreur lors de la création du document ${collectionPath}/${documentId}`, error)
        // En cas d'erreur, ajouter à la file de synchronisation
      }
    }

    // Ajouter l'opération à la file de synchronisation
    const userId = auth.currentUser?.uid || "anonymous"
    return this.addOperation({
      type: SyncOperationType.CREATE,
      collection: collectionPath,
      documentId,
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      userId,
    })
  }

  /**
   * Met à jour un document et l'ajoute à la file de synchronisation si hors ligne
   * @param collectionPath Chemin de la collection
   * @param documentId ID du document
   * @param data Données à mettre à jour
   */
  async updateDocument(collectionPath: string, documentId: string, data: any): Promise<string> {
    // Si nous sommes en ligne, mettre à jour directement le document
    if (navigator.onLine) {
      try {
        await updateDoc(doc(db(), collectionPath, documentId), {
          ...data,
          updatedAt: new Date(),
        })
        return documentId
      } catch (error) {
        console.error(`SyncService: Erreur lors de la mise à jour du document ${collectionPath}/${documentId}`, error)
        // En cas d'erreur, ajouter à la file de synchronisation
      }
    }

    // Ajouter l'opération à la file de synchronisation
    const userId = auth.currentUser?.uid || "anonymous"
    return this.addOperation({
      type: SyncOperationType.UPDATE,
      collection: collectionPath,
      documentId,
      data: {
        ...data,
        updatedAt: new Date(),
      },
      userId,
    })
  }

  /**
   * Supprime un document et l'ajoute à la file de synchronisation si hors ligne
   * @param collectionPath Chemin de la collection
   * @param documentId ID du document
   */
  async deleteDocument(collectionPath: string, documentId: string): Promise<string> {
    // Si nous sommes en ligne, supprimer directement le document
    if (navigator.onLine) {
      try {
        await deleteDoc(doc(db(), collectionPath, documentId))
        return documentId
      } catch (error) {
        console.error(`SyncService: Erreur lors de la suppression du document ${collectionPath}/${documentId}`, error)
        // En cas d'erreur, ajouter à la file de synchronisation
      }
    }

    // Ajouter l'opération à la file de synchronisation
    const userId = auth.currentUser?.uid || "anonymous"
    return this.addOperation({
      type: SyncOperationType.DELETE,
      collection: collectionPath,
      documentId,
      userId,
    })
  }

  /**
   * Synchronise les opérations en attente avec le serveur
   */
  async synchronize(): Promise<void> {
    await this.ensureInitialized()

    // Si nous ne sommes pas en ligne ou si une synchronisation est déjà en cours, ne rien faire
    if (!navigator.onLine || this.syncInProgress) {
      return
    }

    this.syncInProgress = true

    try {
      // Récupérer toutes les opérations en attente
      const operations = await this.getPendingOperations()

      if (operations.length === 0) {
        this.syncInProgress = false
        return
      }

      console.log(`SyncService: Synchronisation de ${operations.length} opérations...`)

      // Traiter les opérations une par une
      for (const operation of operations) {
        await this.processOperation(operation)
      }

      // Notifier les écouteurs après la synchronisation
      this.notifyListeners()
    } catch (error) {
      console.error("SyncService: Erreur lors de la synchronisation", error)
    } finally {
      this.syncInProgress = false
    }
  }

  /**
   * Traite une opération de synchronisation
   * @param operation Opération à traiter
   */
  private async processOperation(operation: SyncOperation): Promise<void> {
    // Mettre à jour le statut de l'opération
    operation.status = "processing"
    await syncStore.setItem(operation.id, operation)

    try {
      switch (operation.type) {
        case SyncOperationType.CREATE:
          await setDoc(doc(db(), operation.collection, operation.documentId), operation.data)
          break
        case SyncOperationType.UPDATE:
          await updateDoc(doc(db(), operation.collection, operation.documentId), operation.data)
          break
        case SyncOperationType.DELETE:
          await deleteDoc(doc(db(), operation.collection, operation.documentId))
          break
      }

      // Opération réussie, la marquer comme terminée
      operation.status = "completed"
      await syncStore.setItem(operation.id, operation)

      // Supprimer l'opération après un délai
      setTimeout(() => {
        syncStore.removeItem(operation.id)
      }, 60000) // Garder l'opération pendant 1 minute pour référence
    } catch (error) {
      console.error(`SyncService: Erreur lors du traitement de l'opération ${operation.id}`, error)

      // Incrémenter le compteur de tentatives
      operation.retryCount++
      operation.lastRetry = Date.now()
      operation.error = String(error)

      // Si le nombre maximum de tentatives est atteint, marquer comme échouée
      if (operation.retryCount >= this.maxRetries) {
        operation.status = "failed"
      } else {
        operation.status = "pending"
      }

      await syncStore.setItem(operation.id, operation)

      // Planifier une nouvelle tentative après un délai
      if (operation.status === "pending") {
        setTimeout(() => {
          this.processOperation(operation)
        }, this.retryDelay * operation.retryCount)
      }
    }
  }

  /**
   * Récupère toutes les opérations en attente
   */
  async getPendingOperations(): Promise<SyncOperation[]> {
    await this.ensureInitialized()

    const operations: SyncOperation[] = []

    await syncStore.iterate((value: SyncOperation) => {
      if (value.status === "pending") {
        operations.push(value)
      }
    })

    // Trier par timestamp (les plus anciennes d'abord)
    operations.sort((a, b) => a.timestamp - b.timestamp)

    return operations
  }

  /**
   * Récupère toutes les opérations
   */
  async getAllOperations(): Promise<SyncOperation[]> {
    await this.ensureInitialized()

    const operations: SyncOperation[] = []

    await syncStore.iterate((value: SyncOperation) => {
      operations.push(value)
    })

    // Trier par timestamp (les plus récentes d'abord)
    operations.sort((a, b) => b.timestamp - a.timestamp)

    return operations
  }

  /**
   * Réessaie une opération échouée
   * @param operationId ID de l'opération à réessayer
   */
  async retryOperation(operationId: string): Promise<void> {
    await this.ensureInitialized()

    const operation = await syncStore.getItem<SyncOperation>(operationId)

    if (!operation) {
      throw new Error(`Opération ${operationId} non trouvée`)
    }

    // Réinitialiser le statut et le compteur de tentatives
    operation.status = "pending"
    operation.retryCount = 0
    operation.error = undefined
    operation.lastRetry = undefined

    await syncStore.setItem(operationId, operation)

    // Notifier les écouteurs
    this.notifyListeners()

    // Si nous sommes en ligne, tenter une synchronisation immédiate
    if (navigator.onLine && !this.syncInProgress) {
      setTimeout(() => this.synchronize(), 1000)
    }
  }

  /**
   * Supprime une opération
   * @param operationId ID de l'opération à supprimer
   */
  async removeOperation(operationId: string): Promise<void> {
    await this.ensureInitialized()

    await syncStore.removeItem(operationId)

    // Notifier les écouteurs
    this.notifyListeners()
  }

  /**
   * Supprime toutes les opérations terminées
   */
  async clearCompletedOperations(): Promise<void> {
    await this.ensureInitialized()

    const operations = await this.getAllOperations()

    for (const operation of operations) {
      if (operation.status === "completed") {
        await syncStore.removeItem(operation.id)
      }
    }

    // Notifier les écouteurs
    this.notifyListeners()
  }

  /**
   * Ajoute un écouteur pour les changements d'opérations
   * @param listener Fonction à appeler lors des changements
   */
  addListener(listener: (operations: SyncOperation[]) => void): void {
    this.listeners.push(listener)
  }

  /**
   * Supprime un écouteur
   * @param listener Écouteur à supprimer
   */
  removeListener(listener: (operations: SyncOperation[]) => void): void {
    this.listeners = this.listeners.filter((l) => l !== listener)
  }

  /**
   * Notifie tous les écouteurs des changements
   */
  private async notifyListeners(): Promise<void> {
    const operations = await this.getAllOperations()
    this.listeners.forEach((listener) => listener(operations))
  }

  /**
   * Nettoie les ressources lors de la destruction du service
   */
  cleanup(): void {
    window.removeEventListener("online", this.handleOnline)
    window.removeEventListener("offline", this.handleOffline)
  }
}

// Exporter une instance singleton du service
export const syncService = new SyncService()
