// Script pour forcer la persistance de l'authentification Firebase
// Ce script s'exécute avant le chargement complet de la page

;(() => {
  // Vérifier si nous sommes dans un navigateur
  if (typeof window === "undefined") return

  // Fonction pour stocker les données d'authentification
  window.cacheAuthData = (data) => {
    if (!data) return

    try {
      // Stocker les données dans localStorage pour une récupération rapide
      localStorage.setItem(
        "firebase_auth_user",
        JSON.stringify({
          uid: data.uid,
          timestamp: Date.now(),
        }),
      )

      if (data.token) {
        localStorage.setItem("firebase_auth_token", data.token)
      }

      console.log("Données d'authentification mises en cache")
    } catch (e) {
      console.error("Erreur lors de la mise en cache des données d'authentification:", e)
    }
  }

  // Fonction pour vérifier si l'utilisateur était connecté précédemment
  window.checkCachedAuth = () => {
    try {
      const userData = localStorage.getItem("firebase_auth_user")
      if (userData) {
        const parsed = JSON.parse(userData)
        return {
          uid: parsed.uid,
          timestamp: parsed.timestamp,
          token: localStorage.getItem("firebase_auth_token"),
        }
      }
    } catch (e) {
      console.error("Erreur lors de la vérification des données d'authentification en cache:", e)
    }
    return null
  }

  // Fonction pour nettoyer les données d'authentification en cache
  window.clearCachedAuth = () => {
    try {
      localStorage.removeItem("firebase_auth_user")
      localStorage.removeItem("firebase_auth_token")
      console.log("Données d'authentification en cache nettoyées")
    } catch (e) {
      console.error("Erreur lors du nettoyage des données d'authentification en cache:", e)
    }
  }

  // Vérifier si nous avons des données d'authentification en cache au chargement
  const cachedAuth = window.checkCachedAuth()
  if (cachedAuth) {
    console.log("Données d'authentification en cache trouvées pour l'utilisateur:", cachedAuth.uid)
  }
})()
